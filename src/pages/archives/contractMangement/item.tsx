import { XlbButton } from '@xlb/components';
import { useEffect, useState } from 'react';
// import Api from './server';
import Details from './components/Details';
import { contract_signs_obj, schema1, schema3 } from './data';
import Api from './server';
type InfoProps = {
  onBack: any;
  record: any;
};

const Item = (props: InfoProps) => {
  const { onBack, record } = props;

  const [info, setInfo] = useState<any>();

  const schema2: any = (keyName: string) => {
    return [
      {
        title: `${keyName}信息`,
        formList: [
          {
            label: `${keyName}代码`,
            name: 'sign_entity_code',
            hidden: keyName === '公司',
            style: {
              width: '35%',
            },
          },
          {
            label: `${keyName}名称`,
            name: 'sign_entity_name',
            style: {
              width: '35%',
            },
          },
          {
            label: '营业执照名称',
            name: 'license_name',
            hidden: keyName === '公司',
            style: {
              width: '35%',
            },
          },
          {
            label: '社会信用代码',
            name: 'license_social_credit_code',
            hidden: keyName === '公司',
            style: {
              width: '35%',
            },
          },
          {
            label: '签约人',
            name: 'sign_person_name',
            style: {
              width: '35%',
            },
            hidden: keyName === '公司',
          },
          {
            label: '签约时间',
            name: 'sign_date',
            style: {
              width: '35%',
            },
          },
          {
            label: '法人',
            name: 'license_legal_person',
            style: {
              width: '35%',
            },
          },
        ],
      },
    ];
  };

  //读取
  const readInfo = async () => {
    const res = await Api.readItem({ id: record?.id });
    if (res.code === 0) {
      setInfo({
        ...res?.data,
      });
    }
  };

  useEffect(() => {
    readInfo();
  }, []);

  return (
    <div
      style={{
        padding: '0 12px 12px 12px',
        height: '100%',
      }}
    >
      <div
        style={{
          position: 'sticky',
          top: '10px',
          zIndex: 23,
          // paddingTop: '30px',
          background: '#fff',
          marginBottom: 10,
          // transform: 'translateY(-25px)',
        }}
      >
        <XlbButton.Group>
          <XlbButton
            type="primary"
            onClick={() => {
              if (typeof onBack === 'function') {
                return onBack?.();
              }
              window.history.back();
            }}
          >
            返回
          </XlbButton>
        </XlbButton.Group>
      </div>
      <section
        style={{
          marginTop: 20,
          height: 'calc(100vh - 133px)',
          overflow: 'auto',
        }}
      >
        <div>{info && <Details data={info} schema={schema1} />}</div>
        <div>
          {info?.contract_signs?.map((t: any) => {
            return (
              <Details
                key={t?.sign_type}
                data={t}
                schema={schema2(contract_signs_obj[`${t?.sign_type}`])}
              />
            );
          })}
        </div>
        <div>{info && <Details data={info} schema={schema3} />}</div>
      </section>
    </div>
  );
};

export default Item;
