// import { useKeepAliveRefresh } from '@/hooks'
import { useBaseParams } from '@/hooks/useBaseParams';
import { exportPage } from '@/services/system';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { replaceUrl } from '@/utils/purchaseUrl';
import safeMath from '@/utils/safeMath';
import { LStorage } from '@/utils/storage';
import { AuditOutlined, EditOutlined } from '@ant-design/icons';
import NiceModal from '@ebay/nice-modal-react';
import type { ContextState, SearchFormType } from '@xlb/components';
import {
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbCopyBoard,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbPageContainer,
  XlbProgress,
  XlbSelect,
  XlbTipsModal,
  XlbTooltip,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { message } from 'antd';
import { cloneDeep, isEmpty } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { v4 as uuidV4 } from 'uuid';
import { filterArr, goodsType, spghgxArr } from '../data';
import Index from '../header/components/Additem';
import {
  batchDeleteStoreItemSupplier,
  batchStoreItemSupplier,
  batchUpdateStoreItemSupplierMain,
  getBaseparam,
  getProSupplier,
  updateStoreItemSupplier,
} from '../server';
import Copy from './components/copy/copy';
import HistoryModal from './components/history';
import styles from './index.less';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const SupplierRelationshipManagement = () => {
  const { navigate } = useNavigation();
  // 顶部搜索表单
  const { enable_organization } = useBaseParams((state) => state);
  const [form] = XlbBasicForm.useForm();
  const formList: SearchFormType[] = [
    {
      label: '组织',
      name: 'org_ids',
      type: 'inputDialog',
      treeModalConfig: {
        title: '选择组织',
        url: '/erp-mdm/hxl.erp.org.tree',
        dataType: 'lists',
        checkable: true, // 是否多选
        primaryKey: 'id',
      } as any,
      hidden: !enable_organization,
    },
    {
      label: '门店',
      name: 'store_ids',
      type: 'inputDialog',
      allowClear: true,
      dependencies: ['org_ids'],
      dialogParams: (form) => {
        const data = {
          org_ids: form?.org_ids || [],
          enable_organization: false,
          status: true,
        };
        if (!form?.org_ids) {
          delete data.org_ids;
        }
        return {
          type: 'store',
          dataType: 'lists',
          isLeftColumn: true,
          isMultiple: true,
          data: data,
        };
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },
    {
      label: '供应商',
      name: 'supplier_ids',
      type: 'inputDialog',
      allowClear: true,
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
    },
    {
      label: '商品档案',
      name: 'item_ids',
      type: 'inputDialog',
      allowClear: true,
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
      },
    },
    {
      label: '物流模式',
      name: 'logistics_mode',
      type: 'select',
      allowClear: true,
      options: [
        {
          label: '统配',
          value: 1,
        },
        {
          label: '直配',
          value: 2,
        },
        {
          label: '直供',
          value: 3,
        },
      ],
    },
  ];
  const [formlist, setFormList] = useState(cloneDeep(formList));

  let refresh = () => {};
  const userinfo = LStorage.get('userInfo');
  const setSelectRowKeysRef = useRef<any>(() => {});
  const [itemArr, setItemArr] = useState(cloneDeep(spghgxArr));
  const childRef = useRef<any>();

  // 批量修改弹窗内容
  const [batchForm] = XlbBasicForm.useForm();
  const [taxRates, setTaxRates] = useState<any[]>([]);
  const [BaseparamList, setBaseparam] = useState<any[]>([]);
  const baseparamRef = useRef<any[]>([]);
  const baseparam = async () => {
    const res = await getBaseparam();
    if (res.code === 0) {
      setBaseparam(res?.data?.tax_rates);
      setTaxRates(res?.data?.tax_rates);
      baseparamRef.current = res?.data?.tax_rates;
    }
  };

  // table search
  const prevPost = () => {
    LStorage.set('supplierRelationshipManagement', {
      ...form.getFieldsValue(),
      item_ids: form.getFieldValue('item_ids'),
      store_ids: form.getFieldValue('store_ids'),
      supplier_ids: form.getFieldValue('supplier_ids'),
      logistics_mode: form.getFieldValue('logistics_mode'),
    });
    return {
      actived: form.getFieldValue('dx')?.includes('noStartS') || void 0, //过滤未启用供应商
      item_ids: form.getFieldValue('item_ids') || [], //商品档案id
      main: form.getFieldValue('dx')?.includes('showmain') || void 0, //仅显示主供应商关系
      // operator_store_id: 12,//操作门店
      stop_purchase:
        form.getFieldValue('dx')?.includes('stop_buying') || void 0, //过滤停购商品
      stop_sale: form.getFieldValue('dx')?.includes('stop_saling') || void 0, //过滤停售商品
      store_ids: form.getFieldValue('store_ids') || [], //门店id
      supplier_ids: form.getFieldValue('supplier_ids') || [], //供应商id
      org_ids: enable_organization ? form.getFieldValue('org_ids') : void 0, //组织id
      logistics_mode: form.getFieldValue('logistics_mode') || '',
    };
  };
  const afterPost = (data: any) => ({
    ...data,
    content: data?.content?.map((item: any) => ({ ...item, uid: uuidV4() })),
  });

  // 表格中事件
  // inputChange事件
  const inputChange = (e: any, key: any, record?: any, ratio?: any) => {
    record[key] = e.target.value ? e.target.value : '';
    if (key == 'purchase_price' && parseInt(e.target.value)) {
      record.basic_price = safeMath.divide(e.target.value, ratio);
    }
    if (key == 'basic_price' && Number(e.target.value) === 0) {
      record.purchase_price = 0.0;
    }
    if (key == 'purchase_price' && Number(e.target.value) === 0) {
      record.basic_price = 0.0;
    }
    if (
      key == 'basic_price' &&
      (parseInt(e.target.value) || parseInt(e.target.value) === 0)
    ) {
      record.purchase_price = safeMath.multiply(e.target.value, ratio);
    }
    if (key == 'rebate_ratio' && parseInt(e.target.value)) {
      const formatValue = Number(e.target.value).toFixed(4); // 保留四位小数
      record.rebate_ratio = formatValue;
    }
  };
  // 输入框失去焦点事件
  const inputBlur = async (e: any, record: any) => {
    //正则非负数值
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    if (
      !reg1.test(record?.basic_price) &&
      hasAuth(['商品供货关系/采购价', '编辑'])
    ) {
      await XlbTipsModal({
        title: '提示',
        tips: `基本单价请输入大于等于零的数字!`,
        isConfirm: true,
        isCancel: false,
      });
      record.basic_price = '0.0000';
      refresh();
      return;
    }
    if (
      !reg1.test(record?.purchase_price) &&
      hasAuth(['商品供货关系/采购价', '编辑'])
    ) {
      await XlbTipsModal({
        title: '提示',
        tips: `采购单价请输入大于等于零的数字!`,
        isConfirm: true,
        isCancel: false,
      });
      refresh();
      return;
    }
    if (
      record?.minimum_delivery_quantity &&
      !reg1.test(record?.minimum_delivery_quantity)
    ) {
      await XlbTipsModal({
        title: '提示',
        tips: `起送量请输入大于等于零的数字!`,
        isConfirm: true,
        isCancel: false,
      });
      refresh();
      return;
    }
    // 正则大于等于零的整数
    const reg = /^(0|[1-9][0-9]*)$/;
    if (
      !reg.test(record?.purchase_period) ||
      record?.purchase_period > 999999999
    ) {
      await XlbTipsModal({
        title: '提示',
        tips: `交货周期请输入>=0并且小于999999999的整数!`,
        isConfirm: true,
        isCancel: false,
      });
      refresh();
      return;
    }

    if (
      !isEmpty(record?.warning_days) &&
      (!reg.test(record?.warning_days) ||
        record?.warning_days > 999999999 ||
        record?.warning_days < 2)
    ) {
      await XlbTipsModal({
        title: '提示',
        tips: `预警天数请输入>1并且小于999999999的整数!`,
        isConfirm: true,
        isCancel: false,
      });
      refresh();
      return;
    }
    if (
      (record?.safe_days != null &&
        record?.safe_days !== '' &&
        !reg.test(record?.safe_days)) ||
      record?.safe_days > 999999999
    ) {
      await XlbTipsModal({
        title: '提示',
        tips: `安全天数请输入>=0并且小于999999999的整数!`,
        isConfirm: true,
        isCancel: false,
      });
      refresh();
      return;
    }
    // 正则返点比例
    const regex = /^\d+(\.\d+)?$/;
    if (
      !record?.rebate_ratio &&
      !regex.test(record?.rebate_ratio) &&
      (Number(record?.rebate_ratio) < 0 || Number(record?.rebate_ratio) > 1)
    ) {
      await XlbTipsModal({
        title: '提示',
        tips: `返点比例请输入大于等于零,并且在0-1之间的数字`,
        isConfirm: true,
        isCancel: false,
      });
      form.setFieldValue('rebate_ratio', '-');
      refresh();
      return;
    }
    const data = {
      basic_price: record?.basic_price == '****' ? null : record?.basic_price, //基本单价
      input_tax_rate: record?.input_tax_rate || 0, //进项税率
      purchase_period: record?.purchase_period, //交货周期
      safe_days: record?.safe_days, //交货周期
      minimum_delivery_quantity: record?.minimum_delivery_quantity || -1, //起送量 因拦截器会过滤null undefined ''等值，所以与后端约定这里传-1 置空
      item_id: record?.item_id, // *********商品id
      store_id: record?.store_id, //*********门店ID,示例值(207200000001)
      supplier_id: record?.supplier_id, //******供应商ID,示例值(207200000001)
      rebate_ratio: record?.rebate_ratio, //返点比例
      warning_days: record?.warning_days ? record?.warning_days : '',
      minimum_delivery_money: record?.minimum_delivery_money || '', //起送金额
      default_unit_type: record?.default_unit_type || void 0,
    };
    const res = await updateStoreItemSupplier(data);
    if (res.code === 0) {
      message.success('更新成功');
    }
  };
  const [productForm] = XlbBasicForm.useForm();
  const producerChange = async (list: any[], record: any, index: any) => {
    const data = {
      ...record,
      basic_price: record?.basic_price == '****' ? null : record.basic_price,
      producer_supplier_ids: list?.map((v: any) => v.id) || [],
      producer_supplier_name: list?.map((v: any) => v.name).join(',') || '',
      executive_standards:
        list?.map((v: any) => {
          return {
            id: v.id,
            name: v.executive_standard,
          };
        }) || [],
    };
    const res = await updateStoreItemSupplier(data);
    if (res.code === 0) {
      message.success('更新成功');
      index?.fetchData?.();
    }
  };
  //条件遍历
  const spghgxRender = (item: any) => {
    switch (item.code) {
      case 'default_unit_type':
        item.render = (value: any, record: any) => {
          if (record._click) {
            return (
              <XlbSelect
                size="small"
                style={{ width: '100%', display: 'block' }}
                defaultValue={record.default_unit_type}
                onBlur={(e) => inputBlur(e, record)}
                allowClear={false}
                onChange={(e) => (record.default_unit_type = e)}
                onClick={(e) => e.stopPropagation()}
              >
                <XlbSelect.Option key="BASIC" value={'BASIC'}>
                  基本单位
                </XlbSelect.Option>
                <XlbSelect.Option key="PURCHASE" value={'PURCHASE'}>
                  采购单位
                </XlbSelect.Option>
              </XlbSelect>
            );
          } else {
            return (
              <div className="info">
                {value === 'BASIC'
                  ? '基本单位'
                  : value === 'PURCHASE'
                    ? '采购单位'
                    : ''}
              </div>
            );
          }
        };
        break;

      case 'item_type':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {goodsType.find((e) => e.value === value)?.label}
            </div>
          );
        };
        break;
      case 'supplier_type':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {value === 'TRADER' ? '贸易商' : '生产商'}
            </div>
          );
        };
        break;
      case 'default_unit_type':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {value === 'BASIC'
                ? '基本单位'
                : value === 'PURCHASE'
                  ? '采购单位'
                  : ''}
            </div>
          );
        };
        break;

      case 'supplier_name':
        item.render = (value: any, record: any, index: number) => {
          return (
            <XlbCopyBoard text={value}>
              <span
                className="link cursors"
                onClick={(e) => {
                  e.stopPropagation();
                  navigate(
                    '/xlb_erp/supplier/item',
                    {
                      ...record,
                      from_page: '商品供货关系',
                      id: record.supplier_id,
                      refresh: refresh,
                    },
                    'xlb_erp',
                    true,
                  );
                }}
              >
                {value}
              </span>
            </XlbCopyBoard>
          );
        };
        break;
      case 'item_code':
        item.render = (value: any, record: any, index: number) => {
          return (
            <XlbCopyBoard text={value}>
              {hasAuth(['商品档案', '查询']) ? (
                <span
                  className="link cursors"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(
                      '/xlb_erp/goodsFiles/item',
                      {
                        record: {
                          ...record,
                          id: record?.item_id,
                          type: 'edit',
                        },
                      },
                      'xlb_erp',
                      true,
                    );
                    // go('/xlb_erp/goodsFiles/item', {
                    //   record: {
                    //     ...record,
                    //     id: record?.item_id,
                    //     type: 'edit'
                    //   }
                    // })
                  }}
                >
                  {value}
                </span>
              ) : (
                <span>{value}</span>
              )}
            </XlbCopyBoard>
          );
        };
        break;
      case 'logistics_mode':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {value === 1
                ? '统配'
                : value === 2
                  ? '直配'
                  : value === 3
                    ? '直供'
                    : ''}
            </div>
          );
        };
        break;
      case 'producer_supplier_name':
        item.render = (value: any, record: any, index: number) => {
          const producer_value =
            record.supplier_type === 'TRADER' ||
            record.supplier_type === 'PRODUCER'
              ? record.producer_res_dtos
                  ?.map((v: any) => v.producer_supplier_name)
                  .join(',') || null
              : null;
          if (record?._click) {
            productForm.setFieldsValue({
              producer_supplier_ids:
                record.producer_res_dtos?.map(
                  (v: any) => v.producer_supplier_id,
                ) || [],
            });
            return (
              <XlbBasicForm
                key={`${record?.store_id}_${record?.supplier_id}_${record?.item_id}`}
                form={productForm}
              >
                <XlbBasicForm.Item name="producer_supplier_ids" noStyle>
                  <XlbInputDialog
                    style={{ width: 130 }}
                    allowClear={false}
                    removeIcon={''}
                    dialogParams={{
                      type: 'producerandexecutivestandard',
                      dataType: 'lists',
                      isLeftColumn: false,
                      isMultiple: true,
                      data: {
                        supplier_id: record.supplier_id,
                        item_id: record.item_id,
                        store_id: record.store_id,
                      },
                    }}
                    handleOnChange={(_ids: number[], list: any[]) =>
                      producerChange(list, record, index)
                    }
                  />
                </XlbBasicForm.Item>
              </XlbBasicForm>
            );
          } else {
            return (
              <XlbCopyBoard text={producer_value}>
                <XlbTooltip title={producer_value}>
                  <div className="info">{producer_value}</div>
                </XlbTooltip>
              </XlbCopyBoard>
            );
          }
        };
        break;
      case 'rebate_ratio':
        item.render = (value: any, record: any, index: number) => {
          const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
          // const formatValue = Number(value).toFixed(4) // 保留四位小数
          // const percentageValue = (Number(formatValue) * 100).toFixed(2) + '%' // 转换为百分比并保留两位小数
          if (
            hasAuth(['商品供货关系/采购价', '查询']) &&
            record._click &&
            hasAuth(['商品供货关系/采购价', '编辑'])
          ) {
            return (
              <XlbInput
                key={record[item.code]}
                className="full-box"
                defaultValue={
                  reg1.test(record[item.code])
                    ? Number(record[item.code]).toFixed(4)
                    : '0.00%'
                }
                onFocus={(e) => e.target.select()}
                onChange={(e) =>
                  inputChange(e, item.code, record, record.purchase_ratio)
                }
                onBlur={(e) => inputBlur(e, record)}
                style={{ textAlign: 'right' }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            );
          } else {
            return (
              <div className="info">
                {hasAuth(['商品供货关系/采购价', '查询'])
                  ? reg1.test(value)
                    ? (Number(Number(value).toFixed(4)) * 100).toFixed(2) + '%'
                    : '-'
                  : '****'}
              </div>
            );
          }
        };
        break;
      case 'purchase_price':
        item.render = (value: any, record: any, index: number) => {
          const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
          if (
            hasAuth(['商品供货关系/采购价', '查询']) &&
            record._click &&
            hasAuth(['商品供货关系/采购价', '编辑'])
          ) {
            return (
              <XlbInput
                key={record[item.code]}
                className="full-box"
                // defaultValue={reg1.test(record[item.code]) ? toFixed(record[item.code],'PRICE') : '0.000'}
                defaultValue={
                  reg1.test(record[item.code])
                    ? Number(record[item.code]).toFixed(4)
                    : '0.000'
                }
                onFocus={(e) => e.target.select()}
                onChange={(e) =>
                  inputChange(e, item.code, record, record.purchase_ratio)
                }
                onBlur={(e) => inputBlur(e, record)}
                style={{ textAlign: 'right' }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            );
          } else {
            return (
              <div className="info">
                {hasAuth(['商品供货关系/采购价', '查询'])
                  ? reg1.test(value)
                    ? Number(value).toFixed(4)
                    : '0.0000'
                  : '****'}
              </div>
            );
          }
        };
        break;
      case 'basic_price':
        item.render = (value: any, record: any, index: number) => {
          const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
          if (
            hasAuth(['商品供货关系/采购价', '查询']) &&
            record._click &&
            hasAuth(['商品供货关系/采购价', '编辑'])
          ) {
            return (
              <XlbInput
                key={record[item.code]}
                className="full-box"
                defaultValue={
                  reg1.test(record[item.code])
                    ? Number(record[item.code]).toFixed(4)
                    : '0.000'
                }
                onFocus={(e) => e.target.select()}
                onChange={(e) =>
                  inputChange(e, item.code, record, record.purchase_ratio)
                }
                onBlur={(e) => inputBlur(e, record)}
                style={{ textAlign: 'right' }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            );
          } else {
            return (
              <div className="info">
                {hasAuth(['商品供货关系/采购价', '查询'])
                  ? reg1.test(value)
                    ? Number(value).toFixed(4)
                    : '0.0000'
                  : '****'}
              </div>
            );
          }
        };
        break;
      case 'input_tax_rate':
        item.render = (value: any, record: any, index: number) => {
          if (record._click) {
            return (
              <XlbSelect
                size="small"
                style={{ width: '100%', display: 'block' }}
                defaultValue={record.input_tax_rate}
                onBlur={(e) => inputBlur(e, record)}
                allowClear
                onChange={(e) => (record.input_tax_rate = e)}
                onClick={(e) => e.stopPropagation()}
              >
                {baseparamRef.current?.map((baseItem: any) => {
                  return (
                    <XlbSelect.Option value={baseItem} key={baseItem}>
                      {baseItem}
                    </XlbSelect.Option>
                  );
                })}
              </XlbSelect>
            );
          } else {
            return <div className="info">{record.input_tax_rate}</div>;
          }
        };
        break;
      case 'minimum_delivery_money':
        item.render = (value: any, record: any) => {
          if (record._click && hasAuth(['商品供货关系', '编辑'])) {
            return (
              <XlbInputNumber
                key={value}
                defaultValue={value}
                onFocus={(e) => e.target.select()}
                onBlur={(e) => {
                  Promise.resolve().then(() => {
                    record.minimum_delivery_money = e.target.value;
                    inputBlur(e, { ...record });
                  });
                }}
                onClick={(e) => e.stopPropagation()}
                min={1}
                precision={0}
              />
            );
          } else {
            return <div className="info">{value}</div>;
          }
        };
        break;
      case 'safe_days':
      case 'purchase_period':
      case 'minimum_delivery_quantity':
      case 'warning_days':
        item.render = (value: any, record: any, index: number) => {
          if (record._click && hasAuth(['商品供货关系', '编辑'])) {
            return (
              <XlbInput
                key={record[item.code]}
                className="full-box"
                defaultValue={record[item.code]}
                onFocus={(e) => e.target.select()}
                onChange={(e) => inputChange(e, item.code, record, 1)}
                onBlur={(e) => inputBlur(e, record)}
                style={{ textAlign: 'right' }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            );
          } else {
            return <div className="info">{value}</div>;
          }
        };
        break;
      case 'stop_sale':
      case 'main':
      case 'stop_purchase':
        item.render = (value: any, record: any, index: number) => {
          const showColor = value ? 'success' : 'danger';
          return <div className={`${showColor}`}>{value ? '是' : '否'}</div>;
        };
        break;
    }

    return item;
  };

  // operate
  const addItem = async (fetchData: any) => {
    await XlbTipsModal({
      title: '新增供货关系',
      keyboard: false,
      className: styles['add-modal-goods'],
      width: 800,
      tips: (
        <Index
          ref={childRef}
          cRef={childRef}
          getData={fetchData}
          BaseparamList={BaseparamList}
          enableOrganization={enable_organization}
        />
      ),
      isConfirm: true,
      isCancel: true,
      onOkBeforeFunction: async () => {
        return await childRef.current.changeVal(true);
      },
    });
  };
  const progressItem = async (taskId: any, success_num: any) => {
    XlbProgress({
      progressParams: { task_id: taskId },
      title: '导入进度',
      pollUrl: '/erp/hxl.erp.storeitemsupplier.importprogress.check',
      cancelUrl: '/erp/hxl.erp.storeitemsupplier.import.cancel',
      canInterrupt: true,
      actionName: '商品供货关系导入',
      onProgressDone: (val: string) => {
        if (val == 'success') {
          message.success('导入成功,共导入' + success_num + '条数据');
          refresh();
        } else if (val == 'stop') {
          refresh();
        }
      },
    });
  };
  const commonImportItem = (e: any, isLeadTime?: boolean) => {
    //导入后弹窗
    if (e?.state) {
      if (isLeadTime) {
        message.success('导入成功,共导入' + e?.success_num + '条数据');
        refresh();
      } else {
        progressItem(e?.task_id, e?.success_num);
      }
    }
  };
  const importItem = (e: any, isLeadTime?: boolean) => {
    //走老逻辑
    if (e.type == 0) {
      commonImportItem(e, isLeadTime);
    } else {
      //判定供应商主档-纳税人资质是否=一般人纳税，若是，校验进项税率是否等于商品档案进项税率，若不相等，则弹窗提示
      XlbTipsModal({
        title: '提示',
        tips: `${e.tips},是否继续导入？`,
        isCancel: true,
        onOkBeforeFunction: async () => {
          //再调一下导入接口正常导入
          e.formData.append('check_tax_rate', false.toString());
          const userInfo = LStorage.get('userInfo');
          const companyId = userInfo?.company_id;
          const data = await fetch(
            process.env.ERP_URL +
              replaceUrl('/erp/hxl.erp.storeitemsupplier.import', companyId),
            {
              method: 'POST',
              body: e.formData,
              headers: {
                'Access-Token': LStorage.get('access_token'),
                'Api-Version': '1.5.0',
                'Company-Id': companyId,
              },
            },
          );
          const res = await data.json();
          if (res?.code === 0) {
            commonImportItem(res?.data, isLeadTime);
          }
          return res?.code === 0;
        },
      });
    }
  };
  const handleImport = async (isPurchaseperiod = false) => {
    const formatUrl = isPurchaseperiod ? 'purchaseperiod.' : '';
    await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp/hxl.erp.storeitemsupplier.${formatUrl}import`,
      templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.storeitemsupplier.${formatUrl}download`,
      templateName: isPurchaseperiod ? '交货周期' : '商品供货关系',
      callback: (res, _modal, customData) => {
        importItem(
          { ...res?.data, formData: customData?.formData },
          isPurchaseperiod,
        );
      },
    });
  };
  const [exportLoading, setExportLoading] = useState(false);
  const exportItem = async (requestForm: any) => {
    const data = { ...requestForm };
    setExportLoading(true);
    const res = await exportPage(
      '/erp/hxl.erp.storeitemsupplier.export',
      data,
      { responseType: 'blob' },
    );
    setExportLoading(false);
    const download = new Download();
    download.filename = '商品供货关系.xlsx';
    download.xlsx(res?.data);
  };
  const getIds = (selectRow: any[]) =>
    selectRow.map((item) => ({
      item_id: item.item_id,
      store_id: item.store_id,
      supplier_id: item.supplier_id,
    }));
  // 设为主供应商
  const setMainSupplier = async (selectRow: any[]) => {
    const data = {
      ids: getIds(selectRow),
    };
    const res = await batchUpdateStoreItemSupplierMain(data);
    if (res.code === 0) {
      setSelectRowKeysRef.current?.([]);
      message.success('设置成功');
      refresh();
    }
  };
  const deleteItem = async (selectRow: any[]) => {
    await XlbTipsModal({
      tips: `已选择${selectRow.length}条供货关系,是否确认删除!`,
      isConfirm: true,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const data = {
          ids: getIds(selectRow),
        };
        const res = await batchDeleteStoreItemSupplier(data);
        if (res.code === 0) {
          message.success('删除成功');
          refresh();
          return res.code === 0;
        }
        message.error('删除失败');
        return res.code === 0;
      },
    });
  };
  const getHistoryData = async (selectRow: any[]) => {
    const _id = {
      item_id: selectRow?.[0]?.item_id,
      store_id: selectRow?.[0]?.store_id,
      supplier_id: selectRow?.[0]?.supplier_id,
      page_size: 999999,
      page_number: 0,
    };
    try {
      await NiceModal.show(NiceModal.create(HistoryModal), { params: _id });
    } catch (error) {
      console.error('🚀 ~ getHistoryData ~ error:', error);
    }
  };

  // 批量修改操作
  // 导入门店
  const importStores = async () => {
    await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.storename.import`,
      templateUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      params: {
        checkOrg: enable_organization,
      },
      callback: (res: any) => {
        if (res.code !== 0) return;
        batchForm.setFieldsValue({
          org_id: enable_organization ? res?.data?.org_ids : [],
          store_ids: res?.data?.store_ids || [],
        });
      },
    });
  };
  // 导入商品
  const importShort = async () => {
    await XlbImportModal({
      importUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.ERP_URL}/erp-mdm/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        batchForm.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
        });
      },
    });
  };

  // 供应商选择
  const supplierChange = async (ids: number[]) => {
    batchForm.setFieldsValue({
      producer_supplier_ids: undefined,
    });
    const data = {
      supplier_id: ids?.[0],
    };
    const res = await getProSupplier(data);
    if (res.code === 0) {
      batchForm.setFieldsValue({
        producerArry: res.data,
      });
    }
  };
  // 组织选择
  const orgHandleChange = async (ids: number[], list: any[]) => {
    const store_ids = list?.map((v: any) => v.store_id) || [];
    batchForm.setFieldsValue({ store_ids });
  };
  // 门店选择
  const storeBeforeChoose = (_ids: number[], list: any[]) => {
    let flag = true;
    // 判断是否选择同一组织门店
    list.reduce((pre: any, cur: any) => {
      if (
        pre?.org_id &&
        pre?.org_parent_id !== cur?.org_parent_id &&
        pre?.org_id !== cur?.org_parent_id &&
        pre?.org_parent_id !== cur?.org_id
      ) {
        flag = false;
      }
      return cur;
    }, {});
    if (enable_organization && !flag) {
      XlbTipsModal({ tips: '请选择同一二级组织下的门店', zIndex: 2001 });
      return false;
    }
    const org_id = list.find((v: any) => v.org_id)?.org_id;
    batchForm.setFieldsValue({ org_id: org_id ? [org_id] : [] });
    return true;
  };
  const batchChange = async () => {
    const res = await XlbTipsModal({
      title: '批量修改',
      keyboard: false,
      width: 530,
      className: styles['batch-opt'],
      tips: (
        <div className={styles.box}>
          <XlbBasicForm
            form={batchForm}
            labelCol={{ span: 5 }}
            initialValues={{
              purchase_period: 0,
              safe_days: 0,
              input_tax_rate: taxRates?.length ? taxRates[0] : undefined,
            }}
            className="batch-change-form"
            layout="inline"
          >
            <div
              style={{
                position: 'relative',
                width: '100%',
                border: '1px solid #E5E6EA',
                borderRadius: 4,
                padding: '20px 10px 4px 4px',
                marginTop: 20,
              }}
            >
              <span
                style={{
                  position: 'absolute',
                  top: -12,
                  left: 30,
                  background: '#fff',
                  padding: '0 12px',
                }}
              >
                修改范围
              </span>
              <XlbBasicForm.Item
                name="supplier_ids"
                label="指定供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
                className="w"
              >
                <XlbInputDialog
                  width={256}
                  dialogParams={{
                    type: 'supplier',
                    isMultiple: false,
                    data: {
                      isNotShowParentSupplier: true,
                      supplier_identities: ['CHILD', 'NORMAL'],
                    },
                  }}
                  onChange={supplierChange}
                />
              </XlbBasicForm.Item>
              {enable_organization ? (
                <XlbBasicForm.Item name="org_id" label="组织" className="w">
                  <XlbInputDialog
                    width={256}
                    treeModalConfig={{
                      title: '选择组织',
                      url: '/erp-mdm/hxl.erp.org.tree',
                      dataType: 'lists',
                      checkable: false, // 是否多选
                      primaryKey: 'id',
                    }}
                    onChange={orgHandleChange}
                  />
                </XlbBasicForm.Item>
              ) : null}
              <XlbBasicForm.Item
                className="w select-with-button"
                label="指定门店"
              >
                <div className="v-flex">
                  <XlbBasicForm.Item name="store_ids" noStyle>
                    <XlbInputDialog
                      width={256}
                      dependencies={['org_id']}
                      dialogParams={(data: any) => ({
                        type: 'store',
                        dataType: 'lists',
                        isMultiple: true,
                        data: {
                          org_ids: data?.org_id,
                          enable_organization: false,
                          status: true,
                        },
                        onOkBeforeFunction: storeBeforeChoose,
                      })}
                      fieldNames={{
                        idKey: 'id',
                        nameKey: 'store_name',
                      }}
                    />
                  </XlbBasicForm.Item>
                  <XlbButton
                    type="text"
                    style={{ marginLeft: -5 }}
                    onClick={() => importStores()}
                  >
                    导入
                  </XlbButton>
                </div>
              </XlbBasicForm.Item>
              <XlbBasicForm.Item
                className="w select-with-button"
                label="指定商品"
              >
                <div className="v-flex">
                  <XlbBasicForm.Item name="item_ids" noStyle>
                    <XlbInputDialog
                      width={256}
                      dialogParams={{
                        type: 'goods',
                        dataType: 'lists',
                        isMultiple: true,
                        nullable: true,
                      }}
                    />
                  </XlbBasicForm.Item>
                  <XlbButton
                    type="text"
                    style={{ marginLeft: -5 }}
                    onClick={() => importShort()}
                  >
                    导入
                  </XlbButton>
                </div>
              </XlbBasicForm.Item>
            </div>
            <div
              style={{
                position: 'relative',
                border: '1px solid #E5E6EA',
                borderRadius: 4,
                padding: '20px 10px 0px 8px',
                marginTop: 20,
                marginBottom: 20,
              }}
            >
              <span
                style={{
                  position: 'absolute',
                  top: -12,
                  left: 30,
                  background: '#fff',
                  padding: '0 12px',
                }}
              >
                修改内容
              </span>

              <XlbBasicForm.Item name="type" style={{ width: '100%' }}>
                <XlbCheckbox.Group style={{ width: '100%', columnGap: 20 }}>
                  <div className="row-flex select-with-checkbox">
                    <XlbCheckbox style={{ width: 94 }} value={1}>
                      交付周期:
                    </XlbCheckbox>
                    <XlbBasicForm.Item name="purchase_period">
                      <XlbInputNumber
                        suffix="天"
                        size="small"
                        width={121}
                        step={0}
                        min={0}
                        controls={false}
                      />
                    </XlbBasicForm.Item>
                  </div>
                  <div className="v-flex select-with-checkbox">
                    <XlbCheckbox style={{ width: 120 }} value={2}>
                      进项税率:
                    </XlbCheckbox>
                    <XlbBasicForm.Item name="input_tax_rate">
                      <XlbSelect
                        style={{ width: 121 }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        {taxRates.map((val: number, index: number) => (
                          <XlbSelect.Option key={val + '_' + index} value={val}>
                            {val} %
                          </XlbSelect.Option>
                        ))}
                      </XlbSelect>
                    </XlbBasicForm.Item>
                  </div>
                  <div className="v-flex select-with-checkbox">
                    <XlbCheckbox style={{ width: 94 }} value={3}>
                      安全天数:
                    </XlbCheckbox>
                    <XlbBasicForm.Item name="safe_days">
                      <XlbInputNumber
                        suffix="天"
                        size="small"
                        width={121}
                        step={0}
                        min={0}
                        controls={false}
                      />
                    </XlbBasicForm.Item>
                  </div>
                  <div className="v-flex select-with-checkbox">
                    <XlbCheckbox style={{ width: 120 }} value={4}>
                      生产商:
                    </XlbCheckbox>
                    <XlbBasicForm.Item shouldUpdate noStyle>
                      {({ getFieldValue }) => {
                        const temp = getFieldValue('producerArry') || [];
                        return (
                          <XlbBasicForm.Item name="producer_supplier_ids">
                            <XlbSelect
                              mode="multiple"
                              width={121}
                              placeholder="请选择"
                            >
                              {temp.map((item: any) => (
                                <XlbSelect.Option key={item.id} value={item.id}>
                                  {item.name}
                                </XlbSelect.Option>
                              ))}
                            </XlbSelect>
                          </XlbBasicForm.Item>
                        );
                      }}
                    </XlbBasicForm.Item>
                  </div>
                  <div className="v-flex select-with-checkbox">
                    <XlbCheckbox style={{ width: 94 }} value={5}>
                      返点比例:
                    </XlbCheckbox>
                    <XlbBasicForm.Item name="rebate_ratio">
                      <XlbInputNumber
                        placeholder="请输入"
                        width={121}
                        onFocus={(e) => e.target.select()}
                        max={100}
                        min={0}
                        precision={2}
                        suffix="%"
                      />
                    </XlbBasicForm.Item>
                  </div>

                  <div className="v-flex select-with-checkbox">
                    <XlbCheckbox style={{ width: 120 }} value={6}>
                      下单默认单位:
                    </XlbCheckbox>
                    <XlbBasicForm.Item name="default_unit_type">
                      <XlbSelect
                        placeholder="请选择"
                        style={{ width: 121 }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <XlbSelect.Option key="BASIC" value={'BASIC'}>
                          基本单位
                        </XlbSelect.Option>
                        <XlbSelect.Option key="PURCHASE" value={'PURCHASE'}>
                          采购单位
                        </XlbSelect.Option>
                      </XlbSelect>
                    </XlbBasicForm.Item>
                  </div>
                </XlbCheckbox.Group>
              </XlbBasicForm.Item>
            </div>
          </XlbBasicForm>
        </div>
      ),
      onOkBeforeFunction: async () => {
        let values: any;
        try {
          values = await batchForm.validateFields();
        } catch (err) {}
        if (!values) return false;
        const {
          type,
          purchase_period,
          safe_days,
          input_tax_rate,
          producer_supplier_ids,
          rebate_ratio,
          org_id,
          supplier_ids,
          store_ids,
          item_ids,
          default_unit_type,
        } = values;
        if (!type?.length) {
          message.error('请选择要修改的数据');
          return false;
        }
        const data: any = {
          org_id: org_id?.[0],
          supplier_id: supplier_ids?.[0],
          store_ids,
          default_unit_type,
          item_ids,
        };
        if (type?.includes(1)) {
          data.purchase_period = purchase_period;
        }
        if (type?.includes(2)) {
          data.input_tax_rate = input_tax_rate;
        }
        if (type?.includes(3)) {
          data.safe_days = safe_days;
        }
        if (type?.includes(4)) {
          data.producer_supplier_ids = producer_supplier_ids;
        }
        if (type?.includes(5)) {
          data.rebate_ratio = (rebate_ratio / 100)?.toFixed(4);
          if (!rebate_ratio) {
            message.warning('请输入返点比例');
            return;
          }
        }
        if (type?.includes(6)) {
          data.default_unit_type = default_unit_type;
          if (!default_unit_type) {
            message.warning('请选择下单默认单位');
            return;
          }
        }
        const batchRes = await batchStoreItemSupplier(data);
        if (batchRes.code === 0) {
          message.success('操作成功');
          refresh();
        }
        // 这三项重置后会被自动填充，待修复
        batchForm.setFieldsValue({
          supplier_ids: [],
          store_ids: [],
          item_ids: [],
        });
        batchForm.resetFields();
        return true;
      },
      isCancel: true,
      onCancel: () => batchForm.resetFields(),
    });
  };

  itemArr?.map((v) => spghgxRender(v));

  // form修改
  const onValuesChange = (changedValues: any, allValues: any) => {
    if (Object.keys(changedValues).includes('org_ids')) {
      form.setFieldsValue({ store_ids: [] });
      (formList.find((i) => i.name === 'store_ids')!
        .dialogParams as any)!.data.org_ids = changedValues.org_ids;
      setFormList([...formList]);
    }
  };

  // 默认逻辑
  const getOrgList = async () => {
    itemArr.find((i) => i.code === 'org_name')!.hidden = !enable_organization;
    setItemArr([...itemArr]);
  };
  useEffect(() => {
    getOrgList();
  }, [enable_organization]);
  useEffect(() => {
    const spghgxLog = LStorage.get('supplierRelationshipManagement');
    if (spghgxLog) form.setFieldsValue({ ...spghgxLog });
    baseparam();
  }, []);

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.storeitemsupplier.page'}
      tableColumn={itemArr}
      immediatePost={false}
      prevPost={prevPost}
      afterPost={afterPost}
    >
      <ToolBtn showColumnsSetting>
        {(contextData: ContextState<any>) => {
          const {
            fetchData,
            loading,
            dataSource,
            requestForm,
            selectRowKeys,
            selectRow,
            setSelectRowKeys,
          } = contextData;
          setSelectRowKeysRef.current = setSelectRowKeys;
          refresh = () => {
            if (!form.getFieldValue('store_ids')?.length) return;
            fetchData();
          };
          return (
            <XlbButton.Group>
              {hasAuth(['商品供货关系', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    if (!form.getFieldValue('store_ids')?.length) {
                      message.error('请选择门店');
                      return;
                    }
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo"></XlbIcon>}
                />
              )}
              {hasAuth(['商品供货关系', '导入']) && (
                <XlbDropdownButton
                  label="导入"
                  placement="bottomLeft"
                  dropList={[{ label: '导入' }, { label: '交货周期导入' }]}
                  dropdownItemClick={(_index: number, data: any) => {
                    switch (data.label) {
                      case '导入':
                        handleImport();
                        break;
                      case '交货周期导入':
                        handleImport(true);
                        break;
                    }
                  }}
                />
              )}

              {hasAuth(['商品供货关系', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={exportLoading}
                  onClick={() => exportItem(requestForm)}
                  icon={<XlbIcon name="daochu"></XlbIcon>}
                  disabled={!dataSource?.length}
                />
              )}
              {hasAuth(['商品供货关系', '编辑']) && (
                <XlbButton
                  label="复制"
                  type="primary"
                  onClick={() => NiceModal.show(Copy, { refresh })}
                  icon={<XlbIcon name="fuzhi"></XlbIcon>}
                />
              )}
              {hasAuth(['商品供货关系', '编辑']) && (
                <XlbButton
                  label="新增供货关系"
                  type="primary"
                  onClick={() => addItem(refresh)}
                  icon={<XlbIcon name="jia"></XlbIcon>}
                />
              )}
              {hasAuth(['商品供货关系', '编辑']) && (
                <XlbButton
                  label="设为主供应商"
                  type="primary"
                  onClick={() => setMainSupplier(selectRow || [])}
                  icon={<EditOutlined />}
                  disabled={!selectRowKeys?.length}
                />
              )}
              {hasAuth(['商品供货关系', '删除']) && (
                <XlbButton
                  label="删除"
                  type="primary"
                  onClick={() => deleteItem(selectRow || [])}
                  disabled={!selectRowKeys?.length}
                  icon={<XlbIcon name="shanchu"></XlbIcon>}
                />
              )}
              {hasAuth(['商品供货关系', '编辑']) && (
                <XlbButton
                  label="批量修改"
                  type="primary"
                  onClick={batchChange}
                  icon={<XlbIcon name="piliang"></XlbIcon>}
                />
              )}
              {hasAuth(['商品供货关系', '编辑']) && (
                <XlbButton
                  disabled={selectRowKeys?.length !== 1}
                  label="修改记录"
                  type="primary"
                  onClick={() => getHistoryData(selectRow || [])}
                  icon={<AuditOutlined />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>

      <SearchForm>
        <XlbForm
          formList={formlist}
          form={form}
          isHideDate
          getFormRecord={() => refresh()}
          onValuesChange={onValuesChange}
        />
        <XlbBasicForm form={form}>
          <XlbBasicForm.Item name="dx">
            <XlbCheckbox.Group>
              {filterArr.map((item) => (
                <XlbCheckbox value={item.value} key={item.value}>
                  {item.label}
                </XlbCheckbox>
              ))}
            </XlbCheckbox.Group>
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </SearchForm>

      <Table key="uid" primaryKey="uid" selectMode="multiple" />
    </XlbPageContainer>
  );
};
export default SupplierRelationshipManagement;
