import {XlbFetch as  ErpRequest } from '@xlb/utils'

export default {
  // 读取数据
  getItem: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.replenishtemplate.read', data)
  },
  // 新增
  add: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.replenishtemplate.save', data)
  },

  // 删除 
  delete: async (data: { id: number }) => {
    return await ErpRequest.post('/erp/hxl.erp.replenishtemplate.delete', data)
  },

  // 修改
  update: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.replenishtemplate.update', data)
  },

  // 商品分页查询
  goodsItem: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.replenishtemplate.item.page', data)
  },
  
  //导出
  exportAll: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.replenishtemplate.item.export', data)
  },

  // 查询修改记录
  getHistory: async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.replenishtemplatelog.page', data)
  },
}