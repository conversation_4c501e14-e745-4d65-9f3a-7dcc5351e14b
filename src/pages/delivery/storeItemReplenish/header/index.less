.form_box {
  position: relative;
  padding: 3px 0 3px 0;
}
.inputtt {
  display: inline-block;
  :global .ant-input {
    min-height: 20px;
  }
  span {
    position: relative;
    top: 5px;
  }
}
.table_box {
  :global .art-table-body {
    min-height: calc(100vh - 180px);
  }
}
.table_fold_box {
  :global .art-table-body {
    min-height: calc(100vh - 170px);
  }
}
.button_box {
  padding: 6px 0 0 0;
  border-bottom: 1px solid @color_line2;
}
.formCheckbox {
  // display: inline-block;
  position: absolute;
  top: 50px;
  left: 1054px;
}
.ddiv {
  :global .art-modal-body {
    padding: 15px !important;
  }
  :global .ant-input-number-sm {
    width: 100%;
    input {
      text-align: right;
    }
  }
  :global .ant-input-affix-wrapper-sm {
    width: 100%;
    padding: 0 7px 0 0 !important;
  }
}
.storeItemReplenishContainer {
  :global .xlbOldPageContianer:not(.xlbOldPageContianer-two-children) .xlb-pageContainer-toolBtn {
    margin-top: 0;
  }
  :global .xlb-pro-form.ant-form-inline > .ant-form-item {
    margin-bottom: 0 !important;
  }
  :global .xlb_table {
    .xlb-ant-form-item {
      margin-bottom: 0 !important;
    }
  }
}
