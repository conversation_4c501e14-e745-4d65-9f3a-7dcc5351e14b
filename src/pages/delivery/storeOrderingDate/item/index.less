.form_container_wrapper {
  width: 100%;
  overflow-x: auto;
  .form_container_storeOrderingDate {
    margin: 5px 0 5px 0;
    max-width: 1920px;
    width: 100%;
    :global .ant-form {
      // display: flex;
      // flex-direction: column;

      // .ant-form-item {
      //   width: 300px;
      // }
      .ant-form-item-label {
        width: 120px !important;
      }
    }
    :global {
      .xlb-ant-form-inline {
        display: unset !important;
      }
      .xlb-ant-form-item-label {
        flex-shrink: 0 !important;
      }
      .xlb-input-dialog {
        width: 100% !important;
      }
      .-item-explain-error {
        color: #f53f3f !important;
      }
    }
  }
}
@media (max-width: 991.98px) {
  .form_container_wrapper {
    width: 845px; /* 小于 992px 时保持最小宽度，触发滚动条 */
    min-width: 845px;
  }
}