import { hasAuth } from '@/utils';
import { SearchOutlined, UploadOutlined } from '@ant-design/icons';
import { ProForm } from '@ant-design/pro-components';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbPageContainer,
  XlbTabs,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { SearchListItem } from '@xlb/components/dist/lowcodes/XlbProForm/components/ProFormList';
import { cloneDeep } from 'lodash-es';
import { Fragment, useLayoutEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

const Goodsway = () => {
  const ref = useRef<HTMLDivElement[]>([]);
  const [activeKey, setActiveKey] = useState('plSum');
  const [formListState, setFormListState] = useState<any>({});
  const pageContainerRef = useRef<Record<any, XlbPageContainerRef>>({});
  const [form] = XlbBasicForm.useForm();
  const formContainerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const tabsRef = useRef<HTMLDivElement>(null);

  useLayoutEffect(() => {
    setTimeout(() => {
      const containerHeight =
        containerRef.current?.getBoundingClientRect().height;
      const formContainerHeight =
        formContainerRef.current?.getBoundingClientRect().height;
      if (containerHeight && formContainerHeight) {
        const height = containerHeight - formContainerHeight;
        if (tabsRef.current) {
          tabsRef.current.setAttribute('style', `height: ${height}px`);
        }
      }
    }, 0);
  }, [activeKey]);

  const [components, setComponents] = useState<HTMLDivElement[]>();

  const itemsTable = [
    {
      label: '损益汇总',
      key: 'plSum',
      children: {
        url: '',
        tableColumn: [],
        formList: [
          {
            width: 392,
            type: 'compactDatePicker',
            label: '日期选择',
            hidden: false,
            name: 'compactDatePicker',
            allowClear: false,
          },
        ],
      },
    },
    {
      label: '损益明细',
      key: 'plDetail',
      children: {
        url: '',
        formList: [
          {
            label: '载具名称',
            name: 'basket_ids',
            type: 'inputDialog',
            allowClear: true,
            dialogParams: {
              type: 'basket',
              dataType: 'lists',
              isLeftColumn: false,
              isMultiple: true,
              primaryKey: 'id',
            },
          },
        ],
        tableColumn: [],
      },
    },
  ];

  useLayoutEffect(() => {
    setComponents(ref.current);
  }, []);

  return (
    <div className="xlb-pro-page-container-with-memo" ref={containerRef}>
      <div ref={formContainerRef}>
        <XlbTabs
          className={'xlb-tabs-memo'}
          activeKey={activeKey}
          tabPosition="bottom"
          renderTabBar={() => <></>}
          items={itemsTable?.map((item, i) => {
            const { key, label, children } = item;
            const { formList = [], tableColumn = [] } = children || {};
            // const tableColumns = tableColumn;
            return {
              key: key,
              label: label,
              forceRender: true,
              children: (
                <Fragment>
                  <div
                    ref={(dom) => {
                      if (dom) {
                        ref.current[i] = dom;
                      }
                    }}
                  ></div>
                  <XlbForm
                    formList={formList}
                    form={form}
                    isHideDate={true}
                    ref={(dom) => {
                      if (dom) {
                        // @ts-ignore
                        formRef.current[key] = dom;
                      }
                    }}
                    onFinish={() => {
                      pageContainerRef.current[key]?.fetchData();
                      return Promise.resolve(true);
                    }}
                    // onValuesChange={(changeValues, formValues) => {
                    //   if (typeof tableColumn === 'function') {
                    //     const column = tableColumn(formValues);
                    //     pageContainerRef.current[key]?.setColumns(column);
                    //   }
                    // }}
                  />
                  <ProForm.Item noStyle hidden>
                    <XlbButton htmlType={'submit'}></XlbButton>
                  </ProForm.Item>
                  {/* <XlbForm
                    // style={!proFormVisible ? { display: 'none' } : {}}
                    from={'search'}
                    className="xlb-pro-pageContainer-form"
                    formList={
                      Object.keys(formListState).length === 0
                        ? (formList as SearchListItem[])
                        : (formListState[i] ?? formList) // ?? formList防止首次取不到加载空
                    }
                    ref={(dom) => {
                      if (dom) {
                        // @ts-ignore
                        formRef.current[key] = dom;
                      }
                    }}
                    onFinish={() => {
                      pageContainerRef.current[key]?.fetchData();
                      return Promise.resolve(true);
                    }}
                    // initialValues={initialValues}
                    onValuesChange={(changeValues, formValues) => {
                      if (typeof tableColumn === 'function') {
                        const column = tableColumn(formValues);
                        pageContainerRef.current[key]?.setColumns(column);
                      }
                    }}
                  >
                    {children?.searchFieldProps?.children}
                    <Form.Item noStyle hidden>
                      <XlbButton htmlType={'submit'}></XlbButton>
                    </ProForm.Item>
                  </XlbForm> */}
                </Fragment>
              ),
            };
          })}
        />
      </div>
      <div className="xlb-pro-page-container-tabs" ref={tabsRef}>
        <XlbTabs
          activeKey={activeKey}
          onChange={(e) => {
            setActiveKey(e);
            // onTabsChange?.(e);
            // const oldForm = formRef.current[activeKey as string];
            // const newForm = formRef.current[e];
            // if (typeof setFormValues === 'function') {
            //   const data = setFormValues(
            //     oldForm.getFieldsValue(),
            //     newForm.getFieldsValue(),
            //   );
            //   newForm.setFieldsValue(data);
            //   return;
            // }
            // // onTabsChange?.(e);
            // newForm.setFieldsValue(oldForm.getFieldsValue());
          }}
          items={itemsTable?.map((i) => {
            const { children = {}, key, label } = i;
            const { url = '', tabColumns = [], formList = [] } = children;
            return {
              key: key,
              label: label,
              children: (
                <XlbPageContainer
                  ref={(dom) => {
                    if (dom) {
                      pageContainerRef.current[key] = dom;
                    }
                  }}
                  url={url as string}
                  tableColumn={tabColumns}
                  immediatePost={true}
                  //   changeColumnAndResetDataSource={
                  //     changeColumnAndResetDataSource
                  //   }
                  //   prevPost={() => {
                  //     const currentForm = formRef.current[key];
                  //     const formValue = currentForm.getFieldsValue(true);
                  //     return { ...formValue, ...leftSelect };
                  //   }}
                  //   afterPost={(data) => {
                  //     if (typeof tableColumn === 'function') {
                  //       const currentForm = formRef.current[key];
                  //       const formValue = currentForm.getFieldsValue(true);
                  //       const column = tableColumn?.(formValue, data);
                  //       pageContainerRef.current[key].setColumns(column);
                  //     }

                  //     return afterPost?.(data) ?? data;
                  //   }}
                  //   footerDataSource={footerDataSource}
                >
                  {components?.[i]
                    ? createPortal(
                        <XlbPageContainer.ToolBtn
                          activeKey={activeKey}
                          showColumnsSetting={true}
                          formSaveOrigin
                          originFormList={
                            cloneDeep(formList) as SearchListItem[]
                          }
                          formList={
                            Object.keys(formListState).length === 0
                              ? (formList as SearchListItem[])
                              : (formListState[i] as SearchListItem[])
                          }
                          onFormChange={(forms) => {
                            setFormListState((prevState: any) => {
                              let preCopy = cloneDeep(prevState);
                              preCopy[i] = forms;
                              return preCopy;
                            });
                          }}
                        >
                          {(context) => {
                            const {
                              fetchData,
                              loading,
                              requestForm,
                              selectRow,
                              selectRowKeys,
                              columns,
                              dataSource,
                            } = context;
                            return (
                              <XlbButton.Group>
                                {hasAuth(['物资统计', '查询']) && (
                                  <XlbButton
                                    label={'查询'}
                                    disabled={loading}
                                    type="primary"
                                    onClick={async () => {
                                      fetchData();
                                    }}
                                    icon={<SearchOutlined />}
                                  />
                                )}
                                {hasAuth(['物资统计', '导出']) && (
                                  <XlbButton
                                    type="primary"
                                    label={'导出'}
                                    disabled={!dataSource?.length || loading}
                                    // onClick={(e: any) => exportItem(e)}
                                    icon={<UploadOutlined />}
                                  />
                                )}
                              </XlbButton.Group>
                            );
                          }}
                        </XlbPageContainer.ToolBtn>,
                        // @ts-ignore
                        components?.[i],
                      )
                    : null}
                  <XlbPageContainer.Table
                    // {...(tableProps as XlbTableProps)}
                    selectMode={'single'}
                  />
                </XlbPageContainer>
              ),
            };
          })}
        />
      </div>
    </div>
  );
};
export default Goodsway;
