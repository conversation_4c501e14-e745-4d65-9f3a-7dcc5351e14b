import { appName as wujieAppName } from '@/wujie/utils';
import { history, useAliveController } from '@umijs/max';
import { useEffect } from 'react';

/**
 * wujie子应用的hooks
 * @param prefix 当是乾坤应用迁移过来需要将xlb_tms截掉
 */
export const useSubWujie = (prefix?: RegExp) => {
  const { getCachingNodes, dropScope, refreshScope } = useAliveController();

  const routerJump = (path: string, state: any) => {
    console.log('path1111111111111', path);
    setTimeout(() => {
      console.log('path2222222222222', path);
      /**
       * 此项目xlb_tms路由前缀在公共 跳转需要截取真实路由
       */
      history.push(prefix ? path.replace(prefix, '') : path, state);
    }, 100);
  };

  // 清除tab时候刷新keepalive scope
  const refreshKeepAlive = (currentTabItem: any) => {
    const { wujiePath, aliveName } = currentTabItem ?? {};
    const pathRouter = aliveName ?? `/${wujiePath.replace(/\+/g, '/')}`;
    dropScope(pathRouter);
  };

  const handleTabRefresh = (historyTab) => {
    refreshScope(historyTab?.path);
  };

  useEffect(() => {
    window.$wujie?.bus.$on(
      `${wujieAppName}-UnmountKeepAlive`,
      refreshKeepAlive,
    );
    window.$wujie?.bus.$on(`${wujieAppName}-router-change`, routerJump);
    window.$wujie?.bus.$on(`${wujieAppName}-refreshScope`, handleTabRefresh);

    window.$wujie?.bus.$emit('onMountWujie');
    return () => {
      window.$wujie?.bus.$off(`${wujieAppName}-router-change`, routerJump);
      window.$wujie?.bus.$off(
        `${wujieAppName}-UnmountKeepAlive`,
        refreshKeepAlive,
      );
      window.$wujie?.bus.$off(`${wujieAppName}-refreshScope`, handleTabRefresh);
    };
  }, []);
};
