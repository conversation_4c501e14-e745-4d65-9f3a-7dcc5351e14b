import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbMessage,
  XlbPageContainer,
  XlbSelect,
  XlbTabs,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useMemo, useRef, useState, type FC } from 'react';
import {
  CustumerPriceTable,
  formListData,
  goodsType,
  StorePriceTable,
  types,
} from './data';
import { updateWholeSalePrice } from './server';
import Copy from './components/copy/copy';
import History from './components/history/history';
import BatchChange from './components/batchChange/batchChange';
import styles from './index.less';

const Index: FC<{ title: string }> = () => {
  const [form1] = XlbBasicForm.useForm();
  const [form2] = XlbBasicForm.useForm();
  const { enable_organization } = useBaseParams((state: any) => state);
  const [formList1, setFormList1] = useState<any[]>(
    formListData?.filter((v) => {
      if (v.name === 'org_ids') {
        return enable_organization;
      }
      if (v.name === 'client_id') {
        return false;
      }
      return true;
    }),
  );
  const [formList2, setFormList2] = useState<any[]>(
    formListData?.filter((v) => {
      if (v.name === 'org_ids') {
        return enable_organization;
      }
      if (v.name === 'client_id') {
        return true;
      }
      return true;
    }),
  );
  const pageRef1 = useRef<any>(null);
  const pageRef2 = useRef<any>(null);
  const whoPageRef = useRef<any>(null);
  const [tabKey, setTabKey] = useState('StorePrice');
  const prevPost1 = async () => {
    const { client_id, panelValue } = form1.getFieldsValue(true);
    // 必填校验，如果必填校验不通过，则不进行请求
    try {
      await form1.validateFields();
      return {
        ...form1.getFieldsValue(),
        client_id: undefined,
        stop_purchase: panelValue?.find((v: any) => v.value == 'stop_purchase')
          ?.itemKey,
        stop_wholesale: panelValue?.find(
          (v: any) => v.value == 'stop_wholesale',
        )?.itemKey,
      };
    } catch (error) {
      return false;
    }
  };
  const prevPost2 = async () => {
    const { client_id, panelValue } = form2.getFieldsValue(true);
    // 必填校验，如果必填校验不通过，则不进行请求
    try {
      await form2.validateFields();
      return {
        ...form2.getFieldsValue(),
        client_id: client_id?.length > 0 ? client_id[0] : undefined,
        stop_purchase: panelValue?.find((v: any) => v.value == 'stop_purchase')
          ?.itemKey,
        stop_wholesale: panelValue?.find(
          (v: any) => v.value == 'stop_wholesale',
        )?.itemKey,
      };
    } catch (error) {
      return false;
    }
  };
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'store_name':
        item.render = (value: any, record: any, index: number) => {
          return <div className="overwidth cursors">{value}</div>;
        };
        break;
      case 'item_type':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {goodsType.find((e) => e.value == value)?.label}
            </div>
          );
        };
        break;
      case 'purchase_price':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['批发价/档案采购价', '查询']) && value !== '****'
                ? Number(value).toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'type2':
      case 'type3':
      case 'type4':
      case 'type':
        item.render = (value: any, record: any, index: any) => {
          const typeKey = item.code; // 动态获取字段 (type, type2, etc.)
          const priceKey = `price${typeKey.slice(-1) === 'e' ? '' : typeKey.slice(-1)}`; // 对应 price, price2, etc.
          const valueKey = `value${typeKey.slice(-1) === 'e' ? '' : typeKey.slice(-1)}`; // 对应 value, value2, etc.
          return record._edit && hasAuth(['批发价/批发价', '编辑']) ? (
            <div onClick={(e) => e.stopPropagation()}>
              <XlbSelect
                style={{ width: '100%' }}
                defaultValue={value}
                onChange={(e) =>
                  selectChange(e, record, index, typeKey, priceKey, valueKey)
                }
                options={types}
              ></XlbSelect>
            </div>
          ) : (
            <div className="info overwidth">
              {types.find((e) => e.value === value)?.label}
            </div>
          );
        };
        break;
      case 'value':
      case 'value2':
      case 'value3':
      case 'value4':
        item.render = (value: any, record: any, index: any) => {
          const valueKey = item.code;
          const priceKey = `price${valueKey.slice(-1) === 'e' ? '' : valueKey.slice(-1)}`;
          const typeKey = `type${valueKey.slice(-1) === 'e' ? '' : valueKey.slice(-1)}`;

          return record._edit &&
            record.typeKey !== 'NULL' &&
            record.typeKey !== null &&
            hasAuth(['批发价/批发价', '编辑']) ? (
            <div onClick={(e) => e.stopPropagation()}>
              <XlbInput
                key={record[valueKey]}
                className="full-box"
                defaultValue={Number(value).toFixed(4)}
                onFocus={(e) => e.target.select()}
                onBlur={(e) =>
                  whoPageRef?.current === 'CustumerPrice'
                    ? onFocusCustumerPrice(e, record, index.index)
                    : outFocus(
                        e,
                        record,
                        index.index,
                        valueKey,
                        typeKey,
                        priceKey,
                      )
                }
                onChange={(e) =>
                  whoPageRef?.current === 'CustumerPrice'
                    ? inputChangeCustumerPrice(e, record, index.index)
                    : inputChange(
                        e,
                        record,
                        index.index,
                        valueKey,
                        priceKey,
                        typeKey,
                      )
                }
              />
            </div>
          ) : (
            <div className="info overwidth">
              {hasAuth(['批发价/批发价', '查询']) && value !== '****'
                ? record.typeKey === 'NULL' || record.typeKey === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'item_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/档案批发价', '查询']) && value !== '****'
                ? record.item_price === 'NULL' || record.item_price === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'item_price2':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/档案批发价', '查询']) && value !== '****'
                ? record.item_price2 === 'NULL' || record.item_price2 === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'item_price3':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/档案批发价', '查询']) && value !== '****'
                ? record.item_price3 === 'NULL' || record.item_price3 === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'item_price4':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/档案批发价', '查询']) && value !== '****'
                ? record.item_price4 === 'NULL' || record.item_price4 === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'price2':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/批发价', '查询']) && value !== '****'
                ? record.type2 === 'NULL' || record.type2 === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'price3':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/批发价', '查询']) && value !== '****'
                ? record.type3 === 'NULL' || record.type3 === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'price4':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/批发价', '查询']) && value !== '****'
                ? record.type4 === 'NULL' || record.type4 === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="overwidth cursors">
              {hasAuth(['批发价/批发价', '查询']) && value !== '****'
                ? record.type === 'NULL' || record.type === null
                  ? '——'
                  : Number(value)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
    }
    return item;
  };
  const selectChange = async (
    e: any, // 下拉选择值
    record: any, // 当前行的数据
    page: any, // 当前行索引
    typeKey: string, // 更新的 type 字段 (e.g., type, type2)
    priceKey: string, // 更新的 price 字段 (e.g., price, price2)
    valueKey: string, // 更新的 value 字段 (e.g., value, value2)
  ) => {
    const index = page?.index;
    const rowDataSource =
      whoPageRef?.current === 'CustumerPrice'
        ? pageRef2.current?.dataSource
        : pageRef1.current?.dataSource;
    // 更新类型和值
    rowDataSource[index][typeKey] = e;
    switch (e) {
      case 'NULL':
        rowDataSource[index][priceKey] = 0;
        rowDataSource[index][valueKey] = 0;
        break;
      case 'RATIO':
        rowDataSource[index][priceKey] = (
          record.purchase_price *
          (1 + rowDataSource[index][valueKey] / 100)
        ).toFixed(4);
        break;
      case 'MONEY':
        rowDataSource[index][priceKey] = (
          record.purchase_price + Number(rowDataSource[index][valueKey])
        ).toFixed(4);
        break;
      case 'FIXED_MONEY':
        rowDataSource[index][priceKey] = Number(
          rowDataSource[index][valueKey],
        ).toFixed(4);
        break;
      default:
        break;
    }

    // 数据更新参数
    const data = {
      client_id: record.client_id,
      store_id: record.store_id,
      item_id: record.item_id,
      unit_type: form1.getFieldValue('unit_type'),
    };
    const data2 = {
      client_id: record.client_id,
      store_id: record.store_id,
      item_id: record.item_id,
      type: e,
      value: record.value,
      unit_type: form2.getFieldValue('unit_type'),
    };

    const additionalKeys = [
      'type',
      'type2',
      'type3',
      'type4',
      'value',
      'value2',
      'value3',
      'value4',
    ];
    additionalKeys.forEach((key) => {
      data[key as keyof typeof data] = rowDataSource[index][key];
      if (key == 'type') {
        data[key as keyof typeof data] = rowDataSource[index][key] || 'NULL';
      }
    });

    // 调用后端接口更新
    const res = await updateWholeSalePrice(
      whoPageRef?.current === 'CustumerPrice' ? data2 : data,
    );
    if (res.code === 0) {
      XlbMessage.success('更新成功');
      page.fetchData();
    }
  };
  const inputChangeCustumerPrice = (e: any, record: any, index: number) => {
    const rowDataCustumer = pageRef2.current?.dataSource;
    rowDataCustumer[index].value = Number(e.target.value);
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    if (record.type === 'RATIO' && reg1.test(e.target.value)) {
      rowDataCustumer[index].price = (
        record.purchase_price *
        (1 + Number(e.target.value) / 100)
      ).toFixed(4);
    } else if (record.type === 'MONEY' && reg1.test(e.target.value)) {
      rowDataCustumer[index].price = (
        Number(record.purchase_price) + Number(e.target.value)
      ).toFixed(4);
    } else if (record.type === 'FIXED_MONEY' && reg1.test(e.target.value)) {
      rowDataCustumer[index].price = Number(e.target.value).toFixed(4);
    }
  };
  const inputChange = (
    e: any,
    record: any,
    index: number,
    valueKey: string,
    priceKey: string,
    typeKey: string,
  ) => {
    const inputValue = Number(e.target.value);
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    const isValidInput = reg1.test(e.target.value);
    const rowData = pageRef1.current?.dataSource;
    // 更新对应的 value 字段
    rowData[index][valueKey] = inputValue;
    // 根据动态 typeKey 更新对应的 price
    const recordType = record[typeKey]; // 动态获取 type 字段
    if (recordType === 'RATIO' && isValidInput) {
      rowData[index][priceKey] = (
        record.purchase_price *
        (1 + inputValue / 100)
      ).toFixed(4);
    } else if (recordType === 'MONEY' && isValidInput) {
      rowData[index][priceKey] = (
        Number(record.purchase_price) + inputValue
      ).toFixed(4);
    } else if (recordType === 'FIXED_MONEY' && isValidInput) {
      rowData[index][priceKey] = inputValue.toFixed(4);
    }
  };
  // input失去焦点
  const onFocusCustumerPrice = async (e: any, record: any, index: number) => {
    const rowDataCustumer = pageRef2.current?.dataSource;
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    if (!reg1.test(e.target.value) || Number(e.target.value) >= 999999999) {
      XlbTipsModal({
        tips: '数值请输入大于等于零且小于等于999999999的数字',
      });
      rowDataCustumer[index].value = '0.0000';
      return;
    }
    const data = {
      client_id: record.client_id,
      store_id: record.store_id,
      item_id: record.item_id,
      value: Number(e.target.value),
      type: record.type,
      unit_type: form2.getFieldValue('unit_type'),
    };
    const res = await updateWholeSalePrice(data);
    if (res.code === 0) {
      XlbMessage.success('更新成功');
      pageRef2.current?.fetchData();
    }
  };
  const outFocus = async (
    e: any,
    record: any,
    index: any,
    valueKey: string,
    typeKey: string,
    priceKey: string,
  ) => {
    const inputValue = Number(e.target.value);
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/; // 正则：确保是有效的数字
    const rowData = pageRef1.current?.dataSource;
    // 校验输入值
    if (
      !reg1.test(e.target.value) ||
      inputValue < 0 ||
      inputValue >= 999999999
    ) {
      XlbTipsModal({
        tips: '数值请输入大于等于零且小于等于999999999的数字',
      });
      rowData[index][valueKey] = 0; // 重置为 0
      return;
    }
    // 更新 value 字段
    rowData[index][valueKey] = inputValue;
    // 根据动态的 typeKey 计算对应的 price 字段
    const recordType = record[typeKey]; // 动态获取 type 字段
    if (recordType === 'RATIO') {
      rowData[index][priceKey] = (
        record.purchase_price *
        (1 + inputValue / 100)
      ).toFixed(4);
    } else if (recordType === 'MONEY') {
      rowData[index][priceKey] = (
        Number(record.purchase_price) + inputValue
      ).toFixed(4);
    } else if (recordType === 'FIXED_MONEY') {
      rowData[index][priceKey] = inputValue.toFixed(4);
    }
    // 准备提交的数据
    const data = {
      client_id: record.client_id,
      store_id: record.store_id,
      item_id: record.item_id,
      unit_type: form1.getFieldValue('unit_type'),
    };
    const additionalKeys = [
      'type',
      'type2',
      'type3',
      'type4',
      'value',
      'value2',
      'value3',
      'value4',
    ];
    additionalKeys.forEach((key) => {
      if (key === valueKey) {
        data[key as keyof typeof data] = inputValue; // 对应的 valueKey 使用 inputValue
      } else {
        data[key as keyof typeof data] = rowData[index][key]; // 其他字段从 rowData 中获取
        if (key == 'type') {
          data[key as keyof typeof data] = rowData[index][key] || 'NULL';
        }
      }
    });
    // 调用 API 更新
    const res = await updateWholeSalePrice(data);
    if (res.code === 0) {
      XlbMessage.success('更新成功');
      pageRef1.current?.fetchData();
    }
  };
  const handleExport = async (e: any, type: string) => {
    const requestForm =
      type === 'StorePrice' ? await prevPost1() : await prevPost2();
    if (!requestForm) return;
    const res = await XlbFetch.post(
      process.env.BASE_URL +
        `/erp/hxl.erp.storewholesaleprice.export`,
      { ...requestForm },
    );
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success(res?.data);
    }
  };

  const MyTable = useMemo(() => {
    return (
      <XlbTabs
        defaultActiveKey={tabKey}
        activeKey={tabKey}
        onTabClick={(e) => {
          whoPageRef.current = e;
          if (e === 'CustumerPrice') {
            form2.setFieldsValue(form1.getFieldsValue(true));
          } else {
            form1.setFieldsValue(form2.getFieldsValue(true));
          }
          setTabKey(e);
        }}
        items={[
          {
            label: '门店批发价',
            key: 'StorePrice',
          },
          {
            label: '客户批发价',
            key: 'CustumerPrice',
          },
        ]}
      />
    );
  }, [tabKey]);

  return (
    <div
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden',
      }}
      className={styles.wholesale_container}
    >
      <div
        style={{
          height: '100%',
          width: '100%',
          position: tabKey === 'StorePrice' ? 'relative' : 'absolute',
          top: 0,
          left: tabKey === 'StorePrice' ? 0 : '100vw',
        }}
      >
        <XlbPageContainer // 查询
          url={'/erp/hxl.erp.storewholesaleprice.page?pageType=StorePrice'}
          changeColumnAndResetDataSource={false}
          ref={pageRef1}
          tableColumn={StorePriceTable?.filter((v) => {
            if (v.code === 'org_name') {
              return enable_organization;
            }
            return true;
          })?.map((v) => {
            if (v.code === 'purchase_price') {
              v.name = enable_organization ? '组织采购价' : '档案采购价';
            } else if (v.code === 'item_price') {
              v.name = enable_organization ? '组织批发价1' : '档案批发价1';
            } else if (v.code === 'item_price2') {
              v.name = enable_organization ? '组织批发价2' : '档案批发价2';
            } else if (v.code === 'item_price3') {
              v.name = enable_organization ? '组织批发价3' : '档案批发价3';
            } else if (v.code === 'item_price4') {
              v.name = enable_organization ? '组织批发价4' : '档案批发价4';
            }
            return tableRender(v);
          })}
          prevPost={prevPost1}
          immediatePost={false}
        >
          <XlbPageContainer.ToolBtn
            formSaveOrigin
            showColumnsSetting
            originFormList={formList1}
            formList={formList1}
            onFormChange={(forms) => {
              setFormList1(forms);
            }}
          >
            {(context: any) => {
              const { loading, fetchData, selectRow, dataSource } = context;
              return (
                <XlbButton.Group>
                  {hasAuth(['批发价', '查询']) && (
                    <XlbButton
                      label={'查询'}
                      disabled={loading}
                      type="primary"
                      onClick={() => fetchData()}
                      icon={<XlbIcon name="sousuo" />}
                    />
                  )}
                  {hasAuth(['批发价', '导入']) && (
                    <XlbButton
                      label="导入"
                      type="primary"
                      onClick={() => {
                        XlbImportModal({
                          importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storewholesaleprice.import`,
                          templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storewholesalepricetemplate.download`,
                          templateName: '下载模板',
                          callback: (res) => {
                            if (res.code === 0) {
                              fetchData();
                            }
                          },
                        });
                      }}
                      icon={<XlbIcon name="daoru" />}
                    />
                  )}
                  {hasAuth(['批发价', '导出']) && (
                    <XlbButton
                      label="导出"
                      type="primary"
                      disabled={!dataSource.length}
                      onClick={(e) => handleExport(e, 'StorePrice')}
                      icon={<XlbIcon name="daochu" />}
                    />
                  )}
                  {hasAuth(['批发价', '编辑']) ? (
                    <XlbButton
                      label="批量修改"
                      disabled={loading}
                      type="primary"
                      onClick={() => {
                        NiceModal.show(NiceModal.create(BatchChange), {
                          fetchData: pageRef1?.current?.fetchData,
                          currentTab: 'StorePrice',
                        });
                      }}
                      icon={<XlbIcon name="piliang" />}
                    />
                  ) : null}
                  {hasAuth(['批发价', '编辑']) ? (
                    <XlbButton
                      label="复制"
                      disabled={loading}
                      type="primary"
                      onClick={() => {
                        NiceModal.show(NiceModal.create(Copy), {
                          fetchData: pageRef1?.current?.fetchData,
                          currentTab: 'StorePrice',
                        });
                      }}
                      icon={<XlbIcon name="fuzhi" />}
                    />
                  ) : null}
                  {hasAuth(['批发价', '编辑']) ? (
                    <XlbButton
                      label="修改记录"
                      disabled={!selectRow.length}
                      type="primary"
                      onClick={() => {
                        NiceModal.show(NiceModal.create(History), {
                          id: selectRow,
                          unitType: form1.getFieldValue('unit_type'),
                        });
                      }}
                      icon={<XlbIcon name="shenqing" />}
                    />
                  ) : null}
                </XlbButton.Group>
              );
            }}
          </XlbPageContainer.ToolBtn>
          <XlbPageContainer.SearchForm>
            <XlbForm
              formList={formList1}
              form={form1}
              initialValues={{
                unit_type: 'WHOLESALE',
              }}
              isHideDate={true}
            />
          </XlbPageContainer.SearchForm>
          {/* )} */}
          <XlbPageContainer.ToolBtnNoStyle>
            {(context: any) => {
              return MyTable;
            }}
          </XlbPageContainer.ToolBtnNoStyle>

          <XlbPageContainer.Table selectMode="single" primaryKey={'id'} />
        </XlbPageContainer>
      </div>
      <div
        style={{
          height: '100%',
          width: '100%',
          position: tabKey === 'CustumerPrice' ? 'relative' : 'absolute',
          top: 0,
          left: tabKey === 'CustumerPrice' ? 0 : '100vw',
        }}
      >
        <XlbPageContainer // 查询
          url={'/erp/hxl.erp.storewholesaleprice.page?pageType=CustumerPrice'}
          changeColumnAndResetDataSource={false}
          ref={pageRef2}
          tableColumn={CustumerPriceTable?.filter((v) => {
            if (v.code === 'org_name') {
              return enable_organization;
            }
            return true;
          })?.map((v) => {
            if (v.code === 'purchase_price') {
              v.name = enable_organization ? '组织采购价' : '档案采购价';
            } else if (v.code === 'item_price') {
              v.name = enable_organization ? '组织批发价' : '档案批发价';
            }
            return tableRender(v);
          })}
          prevPost={prevPost2}
          immediatePost={false}
        >
          <XlbPageContainer.ToolBtn
            formSaveOrigin
            showColumnsSetting
            originFormList={formList2}
            formList={formList2}
            onFormChange={(forms) => {
              setFormList2(forms);
            }}
          >
            {(context: any) => {
              const { loading, fetchData, selectRow, dataSource } = context;
              return (
                <XlbButton.Group>
                  {hasAuth(['批发价', '查询']) && (
                    <XlbButton
                      label={'查询'}
                      disabled={loading}
                      type="primary"
                      onClick={() => fetchData()}
                      icon={<XlbIcon name="sousuo" />}
                    />
                  )}
                  {hasAuth(['批发价', '导入']) && (
                    <XlbButton
                      label="导入"
                      type="primary"
                      onClick={() => {
                        XlbImportModal({
                          importUrl: `${process.env.BASE_URL}/erp/hxl.erp.clientwholesaleprice.import`,
                          templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.clientwholesalepricetemplate.download`,
                          templateName: '下载模板',
                          callback: (res) => {
                            if (res.code === 0) {
                              fetchData();
                            }
                          },
                        });
                      }}
                      icon={<XlbIcon name="daoru" />}
                    />
                  )}
                  {hasAuth(['批发价', '导出']) && (
                    <XlbButton
                      label="导出"
                      type="primary"
                      disabled={!dataSource.length}
                      onClick={(e) => handleExport(e, 'CustumerPrice')}
                      icon={<XlbIcon name="daochu" />}
                    />
                  )}
                  {hasAuth(['批发价', '编辑']) ? (
                    <XlbButton
                      label="批量修改"
                      disabled={loading}
                      type="primary"
                      onClick={() => {
                        NiceModal.show(NiceModal.create(BatchChange), {
                          fetchData: pageRef2?.current?.fetchData,
                          currentTab: 'CustumerPrice',
                        });
                      }}
                      icon={<XlbIcon name="piliang" />}
                    />
                  ) : null}
                  {hasAuth(['批发价', '编辑']) ? (
                    <XlbButton
                      label="复制"
                      disabled={loading}
                      type="primary"
                      onClick={() => {
                        NiceModal.show(NiceModal.create(Copy), {
                          fetchData: pageRef2?.current?.fetchData,
                          currentTab: 'CustumerPrice',
                        });
                      }}
                      icon={<XlbIcon name="fuzhi" />}
                    />
                  ) : null}
                  {hasAuth(['批发价', '编辑']) ? (
                    <XlbButton
                      label="修改记录"
                      disabled={!selectRow.length}
                      type="primary"
                      onClick={() => {
                        NiceModal.show(NiceModal.create(History), {
                          id: selectRow,
                          unitType: form2.getFieldValue('unit_type'),
                        });
                      }}
                      icon={<XlbIcon name="shenqing" />}
                    />
                  ) : null}
                </XlbButton.Group>
              );
            }}
          </XlbPageContainer.ToolBtn>
          <XlbPageContainer.SearchForm>
            <XlbForm
              formList={formList2}
              form={form2}
              initialValues={{
                unit_type: 'WHOLESALE',
              }}
              isHideDate={true}
            />
          </XlbPageContainer.SearchForm>
          {/* )} */}
          <XlbPageContainer.ToolBtnNoStyle>
            {(context: any) => {
              return MyTable;
            }}
          </XlbPageContainer.ToolBtnNoStyle>

          <XlbPageContainer.Table selectMode="single" primaryKey={'id'} />
        </XlbPageContainer>
      </div>
    </div>
  );
};

export default Index;
