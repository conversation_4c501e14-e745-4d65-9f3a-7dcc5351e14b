import { BatchChangeTimeModal } from '@/components/XlbErpBatchTimeModal';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import dateManipulation from '@/utils/dateMainpulation';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer, XlbPopover,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTipsModal,
} from '@xlb/components';
import styles from './index.less'
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch } from '@xlb/utils';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import { basicFormList, StateType, tableColumn } from './data';
import Item from './item';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Index = () => {
  const { enable_cargo_owner, enable_organization } = useBaseParams(
    (state) => state,
  );
  const [form] = XlbBasicForm.useForm<any>();
  const [formList, setFormList] = useState<any>(
    cloneDeep(
      basicFormList?.filter((e) => {
        if (e.name == 'cargo_owner_ids') {
          return enable_cargo_owner;
        }
        if (e.name == 'org_ids') {
          return enable_organization;
        }
        return true;
      }),
    ),
  );
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);

  const [record, setRecord] = useState<any>();
  const requestFormRef = useRef<any>(null);

  const checkData = () => {
    const formData = form.getFieldsValue();
    const { compactDatePicker } = form.getFieldsValue(true);
    const data = {
      ...formData,
      audit_date:
        form.getFieldValue('time_type') === 'audit_date'
          ? compactDatePicker
          : null,
      create_date:
        form.getFieldValue('time_type') === 'create_date'
          ? compactDatePicker
          : null,
      operate_date:
        form.getFieldValue('time_type') === 'operate_date'
          ? [
              compactDatePicker?.[0] + ' 00:00:00',
              compactDatePicker?.[1] + ' 23:59:59',
            ]
          : null,
      // org_ids: enable_organization ? form.getFieldValue('org_ids') : null
    };
    return data;
  };
  const prevPost = () => {
    return checkData();
  };

  const TableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any) => {
          return (
            <div
              className="link cursors"
              style={{color: record?.reverse_fid ? 'red' : '#3D66FE'}}
              onClick={(e) => {
                e.stopPropagation();
                setRecord(record);
                pageModalRef.current?.setOpen(true);
              }}
            >
              {value}
            </div>
          );
        };
        break;
      case 'money':
        item.render = (value: any, record: any) => {
          return hasAuth(['领用进出单/成本价', '查询']) && value !== '****'
            ? Number(value || 0)?.toFixed(2)
            : '****';
        };
        break;
      case 'state':
        item.render = (value: any, record: any, index: number) => {
          let color = '';
          switch (value) {
            case 'AUDIT':
              color = '#ff8400';
              break;
            case 'APPROVE_REFUSE':
            case 'HANDLE_REFUSE':
              color = '#FF0000';
              break;
            case 'HANDLE':
            case 'HANDLE_ING':
              color = '#3D66FE';
              break;
            case 'APPROVE':
              color = '#008000';
              break;
          }
          return (
            <div className="info" style={{ color: color }}>
              {StateType.find((e) => e.value === value)?.label}
            </div>
          );
        };
        break;
      case 'flag':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{value ? '发货' : '退货'}</div>;
        };
        break;
      case 'operate_date':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">{value ? dateManipulation(value) : ''}</div>
          );
        };
        break;
    }

    return item;
  };

  const batchdelete = async (selectRow: any) => {
    const fidsList = selectRow.map((item) => item.fid);
    await XlbTipsModal({
      tips: `是否确认删除${fidsList?.join(',')}单据？`,
      onOk: async () => {
        const data = {
          fids: selectRow.map((item) => item.fid),
        };

        const res = await XlbFetch.post(
          '/erp/hxl.erp.requisitioninoutorder.batchdelete',
          data,
        );
        if (res?.code == 0) {
          XlbMessage.success('删除成功');
          pageConatainerRef.current?.fetchData();
        }
        return true;
      },
    });
  };
  const copyFid = async (selectRow: any) => {
    await XlbTipsModal({
      tips: `是否确认复制${selectRow?.[0].fid}单据`,
      onOkBeforeFunction: async () => {
        const data = {
          fid: selectRow?.[0].fid,
        };
        const res = await XlbFetch.post(
          '/erp/hxl.erp.requisitioninoutorder.copy',
          data,
        );
        if (res?.code == 0) {
          XlbMessage.success('复制成功');
          pageConatainerRef.current?.fetchData();
        }
        return true;
      },
    });
  };
  const reverseFid = async (selectRow: any) => {
    await XlbTipsModal({
      tips: (<div style={{display: 'flex'}}>
        {`是否确认冲红${selectRow?.[0].fid}`}
        <XlbPopover
          placement="rightTop"
          overlayClassName={styles.popoverWrap}
          content={selectRow?.map((t: any) => (
            <div key={t?.fid}>{t?.fid}</div>
          ))}
          trigger="hover"
        >
          <XlbButton type="link">{`(${selectRow?.length})`}</XlbButton>
        </XlbPopover>
        单据
      </div>),
      onOkBeforeFunction: async () => {
        const data = {
          fids: selectRow?.map((item: any) => item.fid),
        };
        const res = await XlbFetch.post(
          '/erp/hxl.erp.requisitioninoutorder.batchreverse',
          data,
        );
        if (res?.code == 0) {
          if (res?.data?.fail_num) {
            XlbTipsModal({
              tips: (<div>
                {res.data?.messages?.map((item: any) => (<div>{item}</div>))}
              </div>),
            })
          } else {
            XlbMessage.success('冲红成功');
          }
          pageConatainerRef.current?.fetchData();
        }
        return true;
      },
    });
  };

  const batchTime = (selectRow: any) => {
    NiceModal.show(BatchChangeTimeModal, {
      idsList: selectRow.map((item: any) => item.fid),
      fetchData: pageConatainerRef?.current?.fetchData,
      url: '/erp/hxl.erp.order.date.change',
      idKey: 'fid_list',
      timeKey: 'change_date',
      type: 'requisition_in_out',
      title: '时间修改',
    });
  };

  const batchaudit = async (selectRow: any) => {
    const bool = await XlbTipsModal({
      tips: `已选择${selectRow.length}条单据，是否确认批量审核？`,
      onOkBeforeFunction: async () => {
        const data = {
          fids: selectRow.map((item) => item.fid),
        };
        if (selectRow.find((item) => item.state != 'INIT')) {
          XlbTipsModal({
            tips: '存在非制单单据，请重新选择！',
          });
          return false;
        }
        const res = await XlbFetch.post(
          '/erp/hxl.erp.requisitioninoutorder.batchaudit',
          data,
        );
        if (res?.code == 0) {
          if (res?.data?.fail_num) {
            XlbTipsModal({
              tips: (
                <div style={{ display: 'flex', flexDirection: 'column' }}>
                  <span>{res?.data?.messages?.join(',')}</span>
                </div>
              ),
            });
            return true;
          }
          if (!res?.data?.fail_num) {
            XlbMessage.success('批量审核成功');
          }
          pageConatainerRef.current?.fetchData();
        }
        return true;
      },
    });
  };
  const exportItem = async (e: any) => {
    const data = {
      ...prevPost(),
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.requisitioninoutorder.export',
      data,
    );
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出受理成功,请到下载中心查看');
    }
  };
  const exportItemDetails = async (e: any, selectRow: any) => {
    const data = {
      fids: selectRow?.map((item) => item.fid),
    };
    const res = await XlbFetch.post(
      '/erp/hxl.erp.requisitioninoutorder.detail.batchexport',
      data,
    );
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出受理成功,请到下载中心查看');
    }
  };
  useEffect(() => {
    form.setFieldsValue({
      requisition_org_id: 1234567,
      time_type: 'create_date',
      compactDatePicker: [
        dayjs().format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD'),
      ],
    });
  }, []);
  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        tableColumn={tableColumn
          ?.filter((v: any) => {
            if (v.code === 'org_id') {
              return enable_organization;
            }
            if (v.code === 'cargo_owner_id') {
              return enable_cargo_owner;
            }
            return true;
          })
          ?.map((item) => TableRender(item))}
        url={'/erp/hxl.erp.requisitioninoutorder.page'}
        prevPost={prevPost}
      >
        <ToolBtn showColumnsSetting>
          {({ fetchData, loading, dataSource, selectRow, requestForm }) => {
            requestFormRef.current = requestForm;
            return (
              <XlbButton.Group>
                <XlbButton
                  label="查询"
                  type="primary"
                  disabled={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name={'sousuo'} />}
                />
                <XlbButton
                  label="新增"
                  type="primary"
                  disabled={loading}
                  onClick={() => {
                    setRecord({ fid: 1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<XlbIcon name={'jia'} />}
                />
                <XlbButton
                  label="删除"
                  type="primary"
                  disabled={loading || !selectRow?.length}
                  onClick={() => {
                    batchdelete(selectRow);
                  }}
                  icon={<XlbIcon name={'shanchu'} />}
                />
                {hasAuth(['领用进出单', '导出']) && (
                  <XlbDropdownButton
                    label="导出"
                    dropList={[
                      {
                        label: '导出',
                        disabled: !dataSource?.length,
                      },
                      {
                        label: '导出明细',
                        disabled: !selectRow?.length,
                      },
                    ]}
                    dropdownItemClick={(value: number, item, e) => {
                      switch (item?.label) {
                        case '导出':
                          exportItem(e);
                          break;
                        case '导出明细':
                          exportItemDetails(e, selectRow);
                          break;
                      }
                    }}
                  />
                )}
                {hasAuth(['领用进出单', '编辑']) && (
                  <XlbButton
                    label="复制"
                    type="primary"
                    disabled={loading || selectRow?.length !== 1}
                    onClick={() => {
                      copyFid(selectRow);
                    }}
                    icon={<XlbIcon name={'fuzhi'} />}
                  />
                )}
                {hasAuth(['领用进出单', '审核']) && (
                  <XlbButton
                    label="批量审核"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    onClick={() => {
                      batchaudit(selectRow);
                    }}
                    icon={<XlbIcon name={'shenhe'} />}
                  />
                )}
                {hasAuth(['领用进出单', '冲红复制']) && (
                  <XlbButton
                    label="批量冲红"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    onClick={() => {
                      reverseFid(selectRow);
                    }}
                    icon={<XlbIcon name={'fuzhi'} />}
                  />
                )}
                {hasAuth(['领用进出单/日期修改', '编辑']) && (
                  <XlbButton
                    label="日期修改"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    onClick={() => {
                      batchTime(selectRow);
                    }}
                    icon={<XlbIcon name="shenqing" />}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm formList={formList} form={form} isHideDate={true} />
        </SearchForm>
        <Table key={'id'} selectMode="multiple"></Table>
      </XlbPageContainer>
    </>
  );
};

export default Index;