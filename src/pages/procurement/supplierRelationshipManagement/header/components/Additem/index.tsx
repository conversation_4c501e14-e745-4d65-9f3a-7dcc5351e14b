import { hasAuth } from '@/utils';
import { LStorage } from '@/utils/storage';
import {
  XlbBasicForm,
  XlbBlueBar,
  XlbInputDialog,
  XlbInputNumber,
  XlbRadio,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { message } from 'antd';
import { isEmpty } from 'lodash';
import { useEffect, useImperativeHandle, useState } from 'react';
import { batchUpdateStoreItemSupplier } from '../../../server';

const Index = (props: any) => {
  const { cRef, getData, BaseparamList, enableOrganization } = props;
  const [form] = XlbBasicForm.useForm();
  const [taxpayerType, setTaxpayerType] = useState<any>(null);
  const [inputTaxRate, setInputTaxRate] = useState<any>(null);
  const [taxList, setTaxList] = useState<any>(BaseparamList || []);
  const [initData, setInitData] = useState<any>({
    basic_price: '0.0000',
    input_tax_rate: undefined,
    item_ids: [],
    main: false,
    purchase_period: '0',
    store_ids: [],
    supplier_ids: [],
    unit_type: 'PURCHASE',
    default_unit_type: 'PURCHASE',
    minimum_delivery_quantity: '',
    minimum_delivery_money: '',
    rebate_ratio: '',
    warning_days: '2',
  });
  useImperativeHandle(cRef, () => ({
    // 暴露给父组件的方法
    changeVal: async (e: any) => {
      if (!e) {
        initData.producer_supplier_ids = null;
        initData.basic_price = '0.0000';
        initData.input_tax_rate = '';
        initData.item_ids = [];
        initData.main = false;
        initData.purchase_period = '0';
        initData.supplier_ids = [];
        initData.supplier_names = '';
        initData.unit_type = 'PURCHASE';
        initData.default_unit_type = 'PURCHASE';
        initData.minimum_delivery_quantity = '';
        initData.minimum_delivery_money = '';
        initData.rebate_ratio = '';
        initData.warning_days = '2';
        localStorage.itemData = JSON.stringify(initData);
        if (localStorage.userInfo && JSON.parse(localStorage.userInfo).value) {
          form.setFieldsValue({
            // store_ids: JSON.parse(localStorage.userInfo).value.user_type,
            // store_names: JSON.parse(localStorage.userInfo).value.user_type_desc,
            store_ids: [...LStorage.get('userInfo').store_id],
            supplier_names: '',
            item_names: '',
            producer_supplier_ids: [],
            producer_supplier_names: '',
            supplier_ids: [],
            item_ids: [],
            store_names: LStorage.get('userInfo').store_name,
            basic_price: '0.0000',
            unit_type: 'PURCHASE',
            default_unit_type: 'PURCHASE',
            purchase_period: '0',
            input_tax_rate: undefined,
            minimum_delivery_quantity: '',
            minimum_delivery_money: '',
            rebate_ratio: '',
            main: false,
            warning_days: '2',
          });
          initData.store_ids = [...LStorage.get('userInfo').store_id];
          initData.store_names = LStorage.get('userInfo').store_name;
        }
        return true;
      }

      return await form
        .validateFields()
        .then(async () => {
          const itemData = JSON.parse(localStorage.itemData);
          if (
            itemData.store_ids.length > 1 &&
            itemData.supplier_ids.length > 1 &&
            itemData.item_ids.length > 1
          ) {
            XlbTipsModal({
              title: '提示',
              tips: `门店、供应商、商品档案，必须存在单选！`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
          if (!itemData.store_ids?.length) {
            XlbTipsModal({
              title: '提示',
              tips: `请先选择门店!`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
          if (!itemData.supplier_ids?.length) {
            XlbTipsModal({
              title: '提示',
              tips: `请先选择供应商!`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
          if (!itemData.item_ids?.length) {
            XlbTipsModal({
              title: '提示',
              tips: `请先选择商品档案!`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
          const regPos = /^[0-9]+.?[0-9]*/;
          if (
            (!regPos.test(itemData.basic_price) ||
              itemData.basic_price > 999999999 ||
              itemData.basic_price.length == 0) &&
            itemData.basic_price != '0.0000'
          ) {
            XlbTipsModal({
              title: '提示',
              tips: `约定单价应输入大于等于0小于等于999999999的数字!`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
          // 正则大于等于零的整数
          const reg = /^(0|[1-9][0-9]*)$/;
          if (
            !reg.test(itemData.purchase_period) ||
            itemData.purchase_period > 999999999
          ) {
            XlbTipsModal({
              title: '提示',
              tips: `交货周期请输入大于等于0并且小于等于999999999的整数!`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
          if (
            !isEmpty(itemData.warning_days) &&
            (!reg.test(itemData.warning_days) ||
              itemData.warning_days > 999999999 ||
              itemData.warning_days < 2)
          ) {
            XlbTipsModal({
              title: '提示',
              tips: `预警天数请输入大于等于1并且小于等于999999999的整数!`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
          if (itemData.unit_type.length != 0) {
            const data = {
              warning_days: itemData.warning_days || '',
              org_id: itemData.org_id,
              producer_supplier_ids: itemData.producer_supplier_ids,
              producer_supplier_names: itemData.producer_supplier_names,
              basic_price: itemData.basic_price, //基本单价
              input_tax_rate: itemData.input_tax_rate, //进项税率
              purchase_period: itemData.purchase_period, //交货周期
              item_ids: itemData.item_ids, // *********商品id
              store_ids: itemData.store_ids, //*********门店ID
              supplier_ids: itemData.supplier_ids, //******供应商ID
              unit_type: itemData.unit_type,
              default_unit_type: itemData.default_unit_type,
              main: itemData.main,
              minimum_delivery_quantity:
                itemData.minimum_delivery_quantity ||
                form.getFieldValue('minimum_delivery_quantity'), // 起送量
              minimum_delivery_money: itemData.minimum_delivery_money, // 起送金额
              rebate_ratio:
                ((itemData.rebate_ratio || 0) / 100)?.toFixed(4) ||
                ((form.getFieldValue('rebate_ratio') || 0) / 100)?.toFixed(4), // 返点比例
            };
            const res = await batchUpdateStoreItemSupplier(data);
            if (res.code === 0) {
              message.success('操作成功');
            } else {
              message.error(res.message);
              return false;
            }
            if (
              localStorage.userInfo &&
              JSON.parse(localStorage.userInfo).value
            ) {
              form.setFieldsValue({
                store_ids: LStorage.get('userInfo').store_id,
                store_names: LStorage.get('userInfo').store_name,
                basic_price: '0.0000',
                unit_type: 'PURCHASE',
                default_unit_type: 'PURCHASE',
                purchase_period: '0',
                input_tax_rate: undefined,
                main: false,
                minimum_delivery_quantity: '',
                minimum_delivery_money: '',
                rebate_ratio: '',
                warning_days: '2',
              });
            }
            getData();
            return true;
          } else {
            XlbTipsModal({
              title: '提示',
              tips: `请选择单位!`,
              isConfirm: true,
              isCancel: false,
            });
            return false;
          }
        })
        .catch(() => false);
    },
  }));

  useEffect(() => {
    if (taxpayerType !== null && inputTaxRate !== null) {
      if (taxpayerType == 0) {
        const defaultTaxList = [...taxList];
        defaultTaxList.push(inputTaxRate);
        console.log('defaultTaxList', defaultTaxList);
        form.setFieldsValue({
          input_tax_rate: inputTaxRate,
        });
        setTaxList(defaultTaxList);
      }
    } else {
      form.setFieldsValue({
        input_tax_rate: null,
      });
      setTaxList(BaseparamList);
    }
  }, [taxpayerType, inputTaxRate]);

  // 表单项改变执行方法
  // 通用方法
  const setLocalStoreData = (data: any) => {
    const oo = { ...JSON.parse(localStorage.itemData) };
    Object.assign(oo, data);
    localStorage.itemData = JSON.stringify(oo);
  };
  // 组织选择
  const orgHandleChange = async (ids: number[], list: any[]) => {
    const store_ids = list?.map((v: any) => v.store_id) || [];
    form.setFieldsValue({ store_ids });
    setLocalStoreData({ org_id: ids?.[0], store_ids });
  };
  // 门店选择
  const storeBeforeChoose = (_ids: number[], list: any[]) => {
    let flag = true;
    // 判断是否选择同一组织门店
    list.reduce((pre: any, cur: any) => {
      if (
        pre?.org_id &&
        pre?.org_parent_id !== cur?.org_parent_id &&
        pre?.org_id !== cur?.org_parent_id &&
        pre?.org_parent_id !== cur?.org_id
      ) {
        flag = false;
      }
      return cur;
    }, {});
    if (enableOrganization && !flag) {
      XlbTipsModal({ tips: '请选择同一二级组织下的门店', zIndex: 2001 });
      return false;
    }
    return true;
  };
  const storeChange = (ids: number[], list: any[]) => {
    setLocalStoreData({
      store_ids: ids || [],
      org_id: list?.find((v) => v.org_id)?.org_id,
    });
  };
  // 供应商选择
  const [supplierType, setSupplierType] = useState<string>('');
  const supplierChange = (ids: any, list: any[]) => {
    setTaxpayerType(list?.length ? list[0]?.taxpayer_type : null);
    form.setFieldsValue({
      sale_modes: list?.length ? [list?.[0]?.collaboration_mode] : [],
    });
    setSupplierType(list?.[0]?.supplier_type);
    if (list?.[0].supplier_type === 'PRODUCER' || !list) {
      form.setFieldsValue({
        producer_supplier_ids: list?.[0]?.id ? [list?.[0].id] : [],
      });
    } else {
      form.setFieldsValue({ producer_supplier_ids: null });
    }
    setLocalStoreData({ supplier_ids: ids || [] });
    // 换供应商清空商品
    form.setFieldsValue({ item_ids: [] });
  };
  // 生产商选择
  const producerChange = (ids: number[]) => {
    setLocalStoreData({ producer_supplier_ids: ids || [] });
  };
  // 商品档案选择
  const itemChange = (ids: any, list: any[]) => {
    //选择单个商品时，判定供应商主档-纳税人资质是否=一般人纳税（0=般纳税人；1=小规模纳税人），若是，则进项税率默认取值商品档案进项税率
    setInputTaxRate(list?.length == 1 ? list[0]?.input_tax_rate : null);
    setLocalStoreData({ item_ids: ids || [] });
  };
  type FormItem =
    | 'basic_price'
    | 'purchase_period'
    | 'input_tax_rate'
    | 'unit_type'
    | 'main'
    | 'minimum_delivery_quantity'
    | 'rebate_ratio'
    | 'warning_days'
    | 'default_unit_type'
    | 'minimum_delivery_money';
  const inputChange = (key: FormItem) => {
    localStorage.itemData = JSON.stringify({
      ...JSON.parse(localStorage.itemData),
      [key]: form.getFieldValue(key),
    });
  };

  useEffect(() => {
    if (localStorage.userInfo && JSON.parse(localStorage.userInfo).value) {
      form.setFieldsValue({
        // store_ids: JSON.parse(localStorage.userInfo).value.user_type,
        // store_names: JSON.parse(localStorage.userInfo).value.user_type_desc,
        store_ids: LStorage.get('userInfo').store_id
          ? [LStorage.get('userInfo').store_id]
          : [],
        store_names: LStorage.get('userInfo').store_name,
        basic_price: '0.0000',
        unit_type: 'PURCHASE',
        default_unit_type: 'PURCHASE',
        purchase_period: '0',
        input_tax_rate: undefined,
        main: false,
        minimum_delivery_quantity: '',
        minimum_delivery_money: '',
        rebate_ratio: '',
        warning_days: '2',
      });
      initData.store_ids.push(...form.getFieldValue('store_ids'));
    }
    localStorage.itemData = JSON.stringify(initData);
  }, []);

  return (
    <div>
      <XlbBlueBar.SubTitle
        style={{ margin: 10 }}
        title={'基本信息'}
      ></XlbBlueBar.SubTitle>

      <XlbBasicForm form={form} layout="inline" autoComplete="off">
        {enableOrganization ? (
          <XlbBasicForm.Item name="org_id" label="组织">
            <XlbInputDialog
              width={256}
              treeModalConfig={{
                title: '选择组织',
                url: '/erp-mdm/hxl.erp.org.tree',
                dataType: 'lists',
                checkable: false, // 是否多选
                primaryKey: 'id',
              }}
              handleOnChange={orgHandleChange}
            />
          </XlbBasicForm.Item>
        ) : null}
        <XlbBasicForm.Item
          name="store_ids"
          label="门店"
          rules={[{ required: true, message: '请选择门店' }]}
        >
          <XlbInputDialog
            width={256}
            dependencies={['org_id']}
            dialogParams={(data: any) => ({
              type: 'store',
              dataType: 'lists',
              isMultiple: true,
              data: {
                org_ids: data?.org_id,
                status: true,
              },
              onOkBeforeFunction: storeBeforeChoose,
            })}
            onChange={storeChange}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name',
            }}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          name="supplier_ids"
          label={'供应商'}
          rules={[{ required: true, message: '请选择供应商' }]}
        >
          <XlbInputDialog
            width={256}
            dialogParams={{
              type: 'supplier',
              isMultiple: false,
              data: {
                isNotShowParentSupplier: true,
                supplier_identities: ['CHILD', 'NORMAL'],
              },
            }}
            onChange={supplierChange}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item name="main" label={'主供应商'}>
          <XlbRadio.Group
            style={{ width: 256 }}
            onChange={() => inputChange('main')}
          >
            <XlbRadio key="true" value={true}>
              是
            </XlbRadio>
            <XlbRadio key="false" value={false}>
              否
            </XlbRadio>
          </XlbRadio.Group>
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          name="producer_supplier_ids"
          label={'生产商'}
          dependencies={['supplier_ids']}
        >
          <XlbInputDialog
            width={256}
            dependencies={['supplier_ids']}
            disabled={
              supplierType === 'PRODUCER' ||
              !form.getFieldValue('supplier_ids')?.length
            }
            dialogParams={(data: any) => ({
              type: 'producer',
              dataType: 'lists',
              isLeftColumn: false,
              isMultiple: true,
              data: { supplier_id: data?.supplier_ids?.[0] },
            })}
            onChange={producerChange}
          />
        </XlbBasicForm.Item>
      </XlbBasicForm>
      <XlbBlueBar.SubTitle
        style={{ margin: 10 }}
        title={'商品信息'}
      ></XlbBlueBar.SubTitle>

      <XlbBasicForm form={form} layout="inline" autoComplete="off">
        <XlbBasicForm.Item
          name="item_ids"
          label={'商品档案'}
          dependencies={['supplier_ids']}
          rules={[{ required: true, message: '请选择商品档案' }]}
        >
          <XlbInputDialog
            width={256}
            dependencies={['sale_modes', 'store_ids']}
            disabled={!form.getFieldValue('supplier_ids')?.length}
            dialogParams={(data: any) => ({
              type: 'goods',
              dataType: 'lists',
              isMultiple: true,
              nullable: true,
              isLeftColumn: false,
              data: {
                item_types: ['STANDARD', 'COMPONENT', 'MULTIPLESPEC'],
                sale_modes: data?.sale_modes,
                store_ids: data?.store_ids,
              },
            })}
            onChange={itemChange}
          />
        </XlbBasicForm.Item>

        {hasAuth(['商品供货关系/采购价', '查询']) && (
          <XlbBasicForm.Item
            name="basic_price"
            label={'约定单价'}
            rules={[{ required: true, message: '约定单价不能为空' }]}
          >
            <XlbInputNumber
              width={256}
              onFocus={(e) => e.target.select()}
              precision={4}
              min={0}
              onChange={() => inputChange('basic_price')}
            />
          </XlbBasicForm.Item>
        )}

        <XlbBasicForm.Item name="unit_type" label={'单位'}>
          <XlbSelect
            style={{ width: 256 }}
            onChange={() => inputChange('unit_type')}
          >
            <XlbSelect.Option key="BASIC" value={'BASIC'}>
              基本单位
            </XlbSelect.Option>
            <XlbSelect.Option key="PURCHASE" value={'PURCHASE'}>
              采购单位
            </XlbSelect.Option>
          </XlbSelect>
        </XlbBasicForm.Item>
        <XlbBasicForm.Item name="default_unit_type" label={'下单默认单位'}>
          <XlbSelect
            style={{ width: 256 }}
            onChange={() => inputChange('default_unit_type')}
          >
            <XlbSelect.Option key="BASIC" value={'BASIC'}>
              基本单位
            </XlbSelect.Option>
            <XlbSelect.Option key="PURCHASE" value={'PURCHASE'}>
              采购单位
            </XlbSelect.Option>
          </XlbSelect>
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          name="purchase_period"
          label={'交货周期'}
          rules={[{ required: true, message: '交货周期不能为空' }]}
        >
          <XlbInputNumber
            style={{ width: 256 }}
            onFocus={(e) => e.target.select()}
            onChange={() => inputChange('purchase_period')}
            suffix="天"
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item
          name="input_tax_rate"
          label={'进项税率'}
          rules={[{ required: true, message: '进项税率不能为空' }]}
        >
          {/*<Input style={{width: 256, height: 26}} onChange={()=>inputChange('input_tax_rate')}/>*/}
          <XlbSelect
            placeholder="请选择"
            style={{ width: 256 }}
            allowClear
            onChange={() => inputChange('input_tax_rate')}
          >
            {taxList?.map((item: any) => {
              return (
                <XlbSelect.Option value={item} key={item}>
                  {`${item}%`}
                </XlbSelect.Option>
              );
            })}
          </XlbSelect>
        </XlbBasicForm.Item>

        <XlbBasicForm.Item name="minimum_delivery_quantity" label={'起送量'}>
          <XlbInputNumber
            placeholder="请输入"
            min={0}
            style={{ width: 256 }}
            onFocus={(e) => e.target.select()}
            onChange={() => inputChange('minimum_delivery_quantity')}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item name="minimum_delivery_money" label="起送金额">
          <XlbInputNumber
            placeholder="请输入"
            style={{ width: 256 }}
            onFocus={(e) => e.target.select()}
            onChange={() => inputChange('minimum_delivery_money')}
            min={1}
            precision={0}
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item name="rebate_ratio" label={'返点比例'}>
          <XlbInputNumber
            style={{ width: 256 }}
            onFocus={(e) => e.target.select()}
            onChange={() => inputChange('rebate_ratio')}
            max={100}
            min={0}
            placeholder="请输入"
            precision={2}
            suffix="%"
          />
        </XlbBasicForm.Item>
        <XlbBasicForm.Item name="warning_days" label={'预警天数'}>
          <XlbInputNumber
            suffix="天"
            placeholder="请输入"
            precision={0}
            min={1}
            max={999999999}
            style={{ width: 256 }}
            onFocus={(e) => e.target.select()}
            onChange={() => inputChange('warning_days')}
          />
        </XlbBasicForm.Item>
      </XlbBasicForm>
    </div>
  );
};
export default Index;
