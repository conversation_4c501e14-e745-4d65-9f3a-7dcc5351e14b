import { searchFormType, selectType } from '@/data/common/type'
import { XlbTableColumnProps } from '@xlb/components-stage'
import styles from './index.less'

export const menuNameMapper: Record<string, string> = {
  add: '新增分类'
  //   edit: '编辑分类',
  //   delete: '删除分类'
}

export const typeList = [
  { label: '商品明细', value: 0 },
  { label: '商品分类', value: 1 }
]

export const goodsType: selectType[] = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD'
  },
  {
    label: '组合商品',
    value: 'COMBINATION'
  },
  {
    label: '成分商品',
    value: 'COMPONENT'
  },
  {
    label: '制单组合',
    value: 'MAKEBILL'
  },
  {
    label: '分级商品',
    value: 'GRADING'
  }
]

export const formlist: searchFormType[] = [
  {
    label: '关键字',
    value: 'keyword',
    type: 'input',
    clear: true,
    check: true
  },
  {
    label: '范围类型',
    value: 'business_scope_type',
    type: 'select',
    clear: true,
    check: true,
    options: typeList
  }
]

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center'
  },
  {
    name: '组织',
    code: 'org_name',
    width: 160,
    features: { sortable: true, details: true }
  },
  {
    name: '商品代码',
    code: 'code',
    width: 160,
    features: { sortable: true, details: true }
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'name',
    width: 300,
    features: { sortable: true },
    render: (value, record) => {
      return (
        <div className="v-flex">
          {record?.open_news_cycle && <span className={'new_tag'}>新</span>}
          <span>{value}</span>
        </div>
      )
    }
  },
  {
    name: '商品分类',
    code: 'item_category',
    width: 140,
    features: { sortable: true },
    render: (value: any) => <div>{value?.name}</div>
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '速记码',
    code: 'shorthand_code',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '基本单位',
    code: 'unit',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 140,
    features: { sortable: true },
    render: (value: any) => {
      return <div>{goodsType?.find((v) => v.value === value)?.label}</div>
    }
  }
]
