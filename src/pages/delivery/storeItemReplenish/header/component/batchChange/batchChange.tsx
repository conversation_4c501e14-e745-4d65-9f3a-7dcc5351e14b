import React, { Fragment, useEffect, useState } from 'react'
import { Form, Input, message, Modal, Select, Space } from 'antd'
import style from './batchChange.less'
import { batch, Units } from '../../../data'
import { batchUpdate } from '../../../server'
import {
  XlbModal,
  XlbTipsModal,
  XlbCheckbox,
  XlbInputNumber,
  XlbRadio,
  XlbSelect,
  XlbInputDialog
} from '@xlb/components'
import { LStorage } from '@/utils/storage'
import { XlbFetch as ErpRequest } from '@xlb/utils'
import { XlbButton, XlbImportModal } from '@xlb/components'

const { Option } = XlbSelect

const BatchChange = (props: any) => {
  const { visible, handleCancel, getData, enableOrganization } = props
  const [selectedObj, setSelectedObj] = useState<object>([]) // 已选择的

  const [form] = Form.useForm()
  const [loding, setloding] = useState<boolean>(false)

  const handleOk = async () => {
    if (!form.getFieldValue('store_ids')) {
      XlbTipsModal({
        tips:'请先选择门店'
      })
      return false
    }
    if (form.getFieldValue('modify_scope') === undefined) {
      XlbTipsModal({
        tips:'请先选择商品范围'
      })
      return false
    }
    if (form.getFieldValue('modify_scope') === 1 && !form.getFieldValue('item_category_ids')) {
      XlbTipsModal({
        tips:'请先选择商品类别'
      })
      return false
    }
    if (form.getFieldValue('modify_scope') === 2 && !form.getFieldValue('item_ids')) {
      XlbTipsModal({
        tips:'请先选择商品档案'
      })
      return false
    }
    if (!form.getFieldValue('checkValue')?.length) {
      XlbTipsModal({
        tips:'请先选择修改内容'
      })
      return false
    }

    const data: any = {}
    data['unit_type'] = form.getFieldValue('unit_type')
    data['store_ids'] = form.getFieldValue('store_ids') ? form.getFieldValue('store_ids') : null
    data['org_id'] =
      form.getFieldValue('org_id')?.length > 0 ? form.getFieldValue('org_id')[0] : null
    let arr: any = []

    if (
      form.getFieldValue('checkValue')?.length &&
      (form.getFieldValue('checkValue').indexOf('warn_day') !== -1 ||
        form.getFieldValue('checkValue').indexOf('point') !== -1 ||
        form.getFieldValue('checkValue').indexOf('upper_limit') !== -1 ||
        form.getFieldValue('checkValue').indexOf('quantity') !== -1 ||
        form.getFieldValue('checkValue').indexOf('base_stock_quantity') !== -1)
    ) {
      try {
        await form.validateFields()
      } catch (err: any) {
        throw err
      }
      batch.forEach((item: any) => {
        data[item.value] =
          form.getFieldValue(item.value) == 0 ? null : form.getFieldValue(item.value)
        if (data[item.value] != null) {
          let regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/
          if (
            !regPos.test(data[item.value]) ||
            data[item.value] < 0 ||
            data[item.value] > 999999999.999
          ) {
            arr.push(`【${item.label}】`)
            form.setFieldValue(item.value, '0.000')
          }
        }
      })
    }

    if (
      form.getFieldValue('checkValue')?.length &&
      form.getFieldValue('checkValue').indexOf('purchase_by') !== -1
    ) {
      data['purchase_by'] = form.getFieldValue('purchase_by')
    }

    if (arr.length) {
      XlbTipsModal({
        tips:`${arr.join('、')}请输入>=0并且<=999999999.999的数字`
      })
      return false
    }
    const modify_scope = form.getFieldValue('modify_scope')
    if (modify_scope === 1) {
      data['item_category_ids'] = form.getFieldValue('item_category_ids')
        ? form.getFieldValue('item_category_ids')
        : []
    } else if (modify_scope === 2) {
      data['item_ids'] = form.getFieldValue('item_ids') ? form.getFieldValue('item_ids') : []
    } else if (modify_scope === 0) {
      data['item_ids'] = []
    }
    setloding(true)
    const res = await batchUpdate(data)
    setloding(false)
    if (res.code === 0) {
      message.success('操作成功')
      getData(1)
      handleCancel()
      form.resetFields()
    }
  }
  // 导入门店
  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storename.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      params: {
        checkOrg: enableOrganization
      },
      callback: (res: any) => {
        if (res.code !== 0) return
        form.setFieldsValue({
          org_id: enableOrganization ? res?.data?.org_ids?.join(',') : undefined,
          store_ids: res?.data?.store_ids ? res?.data?.store_ids : []
        })
      }
    })
  }
  // 导入商品
  const importShort = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.items.batchimport`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.item.shorttemplate.download`,
      templateName: '商品导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return
        setSelectedObj(res?.data?.items)
        form.setFieldsValue({
          item_ids: res?.data?.items?.map((v: any) => v.id),
          item_names: res?.data?.items?.map((v: any) => v.name)?.join(',')
        })
      }
    })
  }

  // 商品分类选择确认
  const selectSubmit = async (obj: any) => {
    const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.page', {
      data: { ids: obj }
    })
    if (res.code === 0) {
      form.setFieldsValue({
        store_ids: res.data?.content?.map((v: any) => v.store_id)
      })
    }
  }
  useEffect(() => {
    form.setFieldsValue({
      store_ids:
        LStorage.get('userInfo')?.query_stores &&
        LStorage.get('userInfo')?.query_stores.length === 1
          ? [LStorage.get('userInfo')?.query_stores[0]?.id]
          : undefined,
      unit_type: 'PURCHASE',
      point: '0.000',
      upper_limit: '0.000',
      quantity: '0.000',
      warn_day: '0',
      base_stock_quantity: '0.000'
    })
    setSelectedObj([]) //清空弹窗中已选择的数据
  }, [visible])

  // @ts-ignore
  return (
    <XlbModal
      title={'批量修改'}
      keyboard={false}
      centered
      visible={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={() => {
        handleCancel()
        form.resetFields()
      }}
      isCancel={true}
      width={450}
      confirmLoading={loding}
    >
      <Form form={form}>
        <div className={style.box}>
          <p className={style.title}>修改门店</p>
          {enableOrganization ? (
            <div>
              <Form.Item name="org_id" label="组织" style={{ margin: '0 0 0 44px' }} colon={false}>
                <XlbInputDialog
                  treeModalConfig={{
                    title: '选择组织',
                    url: '/erp-mdm/hxl.erp.org.tree',
                    dataType: 'lists',
                    checkable: false, // 是否多选
                    primaryKey: 'id',
                    width: 360 // 模态框宽度
                  }}
                  onChange={selectSubmit}
                  width={150}
                />
              </Form.Item>
            </div>
          ) : null}
          <span style={{ display: 'flex', alignItems: 'center' }}>
            <Form.Item
              name="store_ids"
              label="门店"
              style={{ margin: '0 10px 0 44px' }}
              colon={false}
            >
              <XlbInputDialog
                dialogParams={{
                  type: 'store',
                  isMultiple: true,
                  data: {
                    org_ids: form.getFieldValue('org_id') ? form.getFieldValue('org_id') : [],
                    status: true
                  },
                  onOkBeforeFunction: (_val: any, data: any[]) => {
                    let flag = true
                    data?.reduce((pre, cur) => {
                      if (Object.keys(pre)?.length && pre?.org_id !== cur?.org_id) {
                        flag = false
                      }
                      return cur
                    }, {})
                    if (!flag) {
                      XlbTipsModal({ tips: '请选择同一组织下的门店', zIndex: 2111 })
                    } else {
                      form.setFieldsValue({
                        org_id: [data.find((v: any) => v.org_id)?.org_id]
                      })
                    }
                    return flag
                  }
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name'
                }}
                width={150}
              />
            </Form.Item>
            <XlbButton size="small" onClick={() => importStores()}>
              导入
            </XlbButton>
          </span>
        </div>
        <div className={style.box}>
          <p className={style.title}>选择商品</p>
          <Form.Item name="modify_scope">
            <XlbRadio.Group>
              <Space direction="vertical">
                <XlbRadio value={0}>全部商品</XlbRadio>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <XlbRadio value={1}>商品类别</XlbRadio>
                  <Form.Item name="item_category_ids">
                    <XlbInputDialog
                      treeModalConfig={{
                        title: '选择商品分类', // 标题
                        url: '/erp-mdm/hxl.erp.category.find', // 请求地址
                        dataType: 'lists',
                        checkable: true, // 是否多选
                        primaryKey: 'id',
                        data: {
                          enabled: true
                        },
                        width: 360 // 模态框宽度
                      }}
                      width={150}
                    />
                  </Form.Item>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <XlbRadio value={2}>商品档案</XlbRadio>
                  <Form.Item name="item_ids" style={{ marginRight: 10 }}>
                    <XlbInputDialog
                      dialogParams={{
                        type: 'goods',
                        dataType: 'lists',
                        isMultiple: true
                      }}
                      fieldNames={{
                        idKey: 'id',
                        nameKey: 'name'
                      }}
                      width={150}
                      readOnly={false}
                    />
                  </Form.Item>
                  <XlbButton size="small" onClick={() => importShort()}>
                    导入
                  </XlbButton>
                </div>
              </Space>
            </XlbRadio.Group>
          </Form.Item>
        </div>
        <div className={style.box}>
          <p className={style.title}>修改数据</p>
          <div
            style={{
              width: '100%',
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'center',
              margin: '0 30px 0 36px'
            }}
          >
            <span>修改单位</span>
            <Form.Item name="unit_type" colon={false} style={{ marginLeft: 28 }}>
              <XlbSelect size="small" style={{ width: 150, margin: '0 10px 5px 40px' }}>
                {Units.map((item) => (
                  <Option value={item.value}>{item.label}</Option>
                ))}
              </XlbSelect>
            </Form.Item>
          </div>
          <Form.Item name="checkValue">
            <XlbCheckbox.Group
              style={{
                width: '100%',
                display: 'flex',
                flexWrap: 'wrap',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <Fragment>
                <XlbCheckbox value={'point'}>补货订购点</XlbCheckbox>
                <Form.Item
                  name={'point'}
                  rules={[
                    {
                      pattern: /^[+0]?(0|([1-9]\d*))(\.\d+)?$/,
                      max: 999999999.999,
                      message: '请输入>=0并且<=999999999.999的数字'
                    }
                  ]}
                >
                  <XlbInputNumber
                    controls={false}
                    size="small"
                    step={0.001}
                    min={0}
                    width={150}
                    max={999999999.999}
                    onFocus={(e) => e.target.select()}
                  />
                </Form.Item>
              </Fragment>
              <Fragment>
                <XlbCheckbox value={'upper_limit'}>库存上限</XlbCheckbox>
                <Form.Item
                  name={'upper_limit'}
                  key={'b'}
                  rules={[
                    {
                      pattern: /^[+0]?(0|([1-9]\d*))(\.\d+)?$/,
                      max: 999999999.999,
                      message: '请输入>=0并且<=999999999.999的数字'
                    }
                  ]}
                >
                  <XlbInputNumber
                    controls={false}
                    size="small"
                    step={0.001}
                    min={0}
                    width={150}
                    max={999999999.999}
                    onFocus={(e) => e.target.select()}
                  />
                </Form.Item>
              </Fragment>
              <Fragment>
                <XlbCheckbox value={'quantity'}>补货订购量</XlbCheckbox>
                <Form.Item
                  name={'quantity'}
                  key={'c'}
                  rules={[
                    {
                      pattern: /^[+0]?(0|([1-9]\d*))(\.\d+)?$/,
                      max: 999999999.999,
                      message: '请输入>=0并且<=999999999.999的数字'
                    }
                  ]}
                >
                  <XlbInputNumber
                    controls={false}
                    size="small"
                    step={0.001}
                    min={0}
                    width={150}
                    max={999999999.999}
                    onFocus={(e) => e.target.select()}
                  />
                </Form.Item>
              </Fragment>
              <Fragment>
                <XlbCheckbox value={'base_stock_quantity'}>基础库存</XlbCheckbox>
                <Form.Item
                  name={'base_stock_quantity'}
                  key={'d'}
                  rules={[
                    {
                      pattern: /^[+0]?(0|([1-9]\d*))(\.\d+)?$/,
                      max: 999999999.999,
                      message: '请输入>=0并且<=999999999.999的数字'
                    }
                  ]}
                >
                  <XlbInputNumber
                    controls={false}
                    size="small"
                    step={0.001}
                    min={0}
                    width={150}
                    max={999999999.999}
                    onFocus={(e) => e.target.select()}
                  />
                </Form.Item>
              </Fragment>
              {LStorage.get('userInfo')?.store?.enable_delivery_center ? (
                <Fragment>
                  <XlbCheckbox value={'warn_day'}>预警周转天数</XlbCheckbox>
                  <Form.Item
                    name={'warn_day'}
                    key={'e'}
                    rules={[
                      { pattern: /^\+?[0-9]*$/, max: 999999999, message: '仅支持录入>=0的整数' }
                    ]}
                  >
                    <XlbInputNumber
                      controls={false}
                      width={150}
                      step={1}
                      min={0}
                      max={999999999}
                      size="small"
                      onFocus={(e) => e.target.select()}
                    />
                  </Form.Item>
                </Fragment>
              ) : null}
            </XlbCheckbox.Group>
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  )
}
export default BatchChange
