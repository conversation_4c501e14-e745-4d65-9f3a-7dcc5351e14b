import NiceModal from '@ebay/nice-modal-react';
import { XlbInputNumber, type XlbTableColumnProps } from '@xlb/components';
import { activeModal } from './component/activeModal';
import './index.less';

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '补货门店',
    code: 'store_name',
    width: 172,
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 172,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 117,
  },
  {
    name: '商品条码',
    code: 'item_barcode',
    width: 142,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 312,

    render: (text: any, record: any) => {
      return (
        <div className="v-flex">
          {Boolean(record?.promotions?.length) && (
            <span
              className="show-active"
              onClick={(e) => {
                e.stopPropagation();
                NiceModal.show(activeModal, { activeList: record?.promotions });
              }}
            >
              活动
            </span>
          )}
          <div className="xlb-table-overwidth">{text}</div>
        </div>
      );
    },
  },
  {
    name: '二级分类名称',
    code: 'level_two_category_name',
    width: 121,
  },
  {
    name: '采购规格',
    code: 'item_purchase_spec',
    width: 121,
  },
  {
    name: '品牌',
    code: 'item_brand',
    width: 116,
    hidden: true,
  },
  {
    name: '供应商编码',
    code: 'supplier_code',
    width: 172,
  },
  {
    name: '供应商名称',
    code: 'supplier_id',
    width: 172,
  },
  {
    name: '保质期',
    code: 'item_period',
    width: 117,
  },
  {
    name: '理想订货量',
    code: 'source_advise_qty',
    width: 120,
    align: 'right',
    features: { format: 'QUANTITY' },
  },
  {
    name: '建议订货量',
    code: 'advise_qty',
    width: 120,
    align: 'right',
  },
  {
    name: '实际订货量',
    code: 'order_qty',
    width: 120,
    align: 'right',

    render: (text: any, record: any) => {
      if (record?._click) {
        return (
          <XlbInputNumber
            defaultValue={text}
            min={0}
            precision={0}
            autoComplete={'off'}
            onKeyDown={(e) => {
              if (e.key === '.' || e.key === ',') {
                e.preventDefault(); // 阻止输入小数点
              }
            }}
            formatter={(value) => `${value}`.replace(/[.,]/g, '')} // 移除小数点
            parser={(value) => value?.replace(/[.,]/g, '') || ''} // 解析时也移除
            controls={false}
            onChange={(e: any) => (record.order_qty = e)}
          />
        );
      }
      return record.order_qty;
    },
  },
  {
    name: '搭赠数量',
    code: 'present_qty',
    width: 110,
    align: 'right',
    features: { format: 'QUANTITY' },
  },
  {
    name: '可用库存',
    code: 'avl_stock_qty',
    width: 110,
    align: 'right',
  },
  {
    name: '在订库存',
    code: 'on_order_qty',
    width: 110,
    align: 'right',
  },
  {
    name: '可用库存DIFC',
    code: 'avl_stock_qty_difc',
    width: 120,
    align: 'right',
  },
  {
    name: '预计可用天数',
    code: 'avl_days',
    width: 120,
    align: 'right',
  },
  {
    name: '主供应商交货周期',
    code: 'supplier_lead_time',
    width: 152,
    align: 'right',
  },
  {
    name: '近28天批发销售数量',
    code: 'ws_sales28d',
    width: 172,
    align: 'right',
  },
  {
    name: '会员日均销',
    code: 'member_daily_avg_sales',
    width: 118,
    hidden: true,
  },
  {
    name: '历史均销',
    code: 'history_avg_sales',
    width: 110,
    hidden: true,
  },
  {
    name: '有销量门店数',
    code: 'active_store_qty',
    width: 118,
  },
  {
    name: '有销量新增门店数',
    code: 'new_stores_count',
    width: 138,
  },
  {
    name: '经营门店数',
    code: 'operate_store_num',
    width: 118,
  },
  {
    name: '搭赠规则',
    code: 'present_rule',
    width: 110,
  },
  {
    name: '到货日期',
    code: 'receive_date',
    width: 110,
  },
  {
    name: '订货日期',
    code: 'order_date',
    width: 110,
  },
  {
    name: '门店交货周期',
    code: 'store_lead_time',
    width: 120,
  },
  {
    name: '安全库存',
    code: 'safe_stock_qty',
    width: 110,
  },
  {
    name: '订货频次（天/次）',
    code: 'order_freq',
    width: 152,
  },
  {
    name: '起订量类型',
    code: 'min_order_type',
    width: 120,
  },
  {
    name: '起订量',
    code: 'min_order_qty',
    width: 100,
  },
  {
    name: '前5周出库量',
    code: 'out_qty5w',
    width: 124,
  },
  {
    name: '前4周出库量',
    code: 'out_qty4w',
    width: 124,
  },
  {
    name: '前3周出库量',
    code: 'out_qty3w',
    width: 124,
  },
  {
    name: '前2周出库量',
    code: 'out_qty2w',
    width: 124,
  },
  {
    name: '前1周出库量',
    code: 'out_qty1w',
    width: 124,
  },
  {
    name: '当前周出库量',
    code: 'out_qty_cw',
    width: 124,
  },
  {
    name: '当前周销量',
    code: 'sale_qty_cw',
    width: 120,
  },
  {
    name: '前5周销量',
    code: 'sale_qty5w',
    width: 120,
    hidden: true,
  },
  {
    name: '前4周销量',
    code: 'sale_qty4w',
    width: 120,
    hidden: true,
  },
  {
    name: '前3周销量',
    code: 'sale_qty3w',
    width: 120,
    hidden: true,
  },
  {
    name: '前2周销量',
    code: 'sale_qty2w',
    width: 120,
    hidden: true,
  },
  {
    name: '前1周销量',
    code: 'sale_qty1w',
    width: 120,
    hidden: true,
  },
  {
    name: '当前周预测出库量',
    code: 'forecast_out_qty',
    width: 148,
  },
  {
    name: '后1周预测出库量',
    code: 'forecast_out_qty_n1w',
    width: 148,
  },
  {
    name: '后2周预测出库量',
    code: 'forecast_out_qty_n2w',
    width: 148,
  },
  {
    name: '后3周预测出库量',
    code: 'forecast_out_qty_n3w',
    width: 148,
  },
  {
    name: '后4周预测出库量',
    code: 'forecast_out_qty_n4w',
    width: 148,
  },
  {
    name: '调拨在途数量',
    code: 'transfer_in_transit_qty',
    width: 128,
  },
  {
    name: '总库存DIFC',
    code: 'total_avl_stock_qty',
    width: 128,
  },
];
