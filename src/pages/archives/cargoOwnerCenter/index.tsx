import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbModal,
  XlbPageContainer,
  XlbSelect,
  XlbTable,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import { MouseEvent, useEffect, useState } from 'react';
import './';
import AddItem from './components/addItem';
import copyItem from './components/copyItem';
import modifyRecord from './components/modifyRecord';
import { cargoList, detailColumn, formList, tableList } from './data';
import './index.less';
import Api from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const Index = () => {
  const [form] = XlbBasicForm.useForm<any>();
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [data, setData] = useState([]);
  const [keyWord, setKeyWord] = useState('');
  const [owner_type, set_owner_type] = useState(null);
  let refresh = () => {};

  const prevPost = () => {
    const formData = form.getFieldsValue(true);
    return {
      ...formData,
    };
  };
  const handleView = (
    t = [],
    e: MouseEvent<HTMLSpanElement, globalThis.MouseEvent>,
  ) => {
    e.stopPropagation();
    const v = JSON.parse(JSON.stringify(t));
    let first_item = [];
    v.forEach((_, index) => {
      if (_.default_cargo_owner) {
        first_item = v.splice(index, 1);
      }
      _.key = Math.random();
    }); //  id 有重复 增加 key 字段

    setVisible(true);
    setDataSource([...first_item, ...v] || []);
    setData([...first_item, ...v] || []);
  };
  const addOrEdit = () => {
    NiceModal.show(AddItem, { fetchData: refresh });
  };
  const synchronize = async () => {
    const res = await Api.syncData({});
    if (res.code === 0) {
      if (res.data?.result_msg) {
        message.warning(res.data?.result_msg);
      } else {
        message.success('同步成功');
      }
      refresh();
    }
  };
  const handleEdit = async (
    record: any,
    e: MouseEvent<HTMLDivElement, globalThis.MouseEvent>,
  ) => {
    e.stopPropagation();
    e.preventDefault();

    console.log(record, 'SSSSSSSSS');
    const list = await XlbBasicData({
      type: 'cargoOwnerEdit',
      isMultiple: true,
      dataType: 'lists',
      isLeftColumn: false,
      // selectedList: record?.cargo_owner_res_info_list?.length
      //   ? record?.cargo_owner_res_info_list
      //   : [],
      data: {
        status: 'ENABLED',
      },
      onOkBeforeFunction: (ids) => {
        if (!ids?.length) {
          message.error('货主范围不能为空');
          return false;
        }
        return true;
      },
    });
    console.log(list, 'handleList');
    const data = [
      ...(record?.cargo_owner_res_info_list || []),
      ...(list || []),
    ];
    if (data?.length && list?.length) {
      const res = await Api.updateInfo({
        store_id: record?.store_id,
        id: record?.id,
        cargo_owner_ids: data?.length ? data?.map((t) => t.id) : [],
      });
      if (res?.code === 0) {
        refresh();
        message.success('更新成功');
      }
    }
  };

  /**
   *
   */
  const Render = (item: any) => {
    switch (item.code) {
      case 'store_code':
        item.render = (value: string, record: any) => {
          return (
            <div
              className="overwidth link"
              onClick={(e) => handleEdit(record, e)}
            >
              {value}
            </div>
          );
        };
        break;
      case 'cargo_owner_range':
        item.render = (text: any, record: any) => {
          const cargo_owner_res_info_list =
            record.cargo_owner_res_info_list || [];
          const len = cargo_owner_res_info_list?.length;
          let default_cargo_owner_name = '';
          const is_default_cargo_owner = Boolean(
            cargo_owner_res_info_list.find((_) => _.default_cargo_owner),
          );
          if (len) {
            const first =
              cargo_owner_res_info_list.find((_) => _.default_cargo_owner) ||
              cargo_owner_res_info_list[0];
            default_cargo_owner_name = first.source_name;
          }
          return (
            <div className="flex">
              <span
                style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}
                className=""
              >
                {default_cargo_owner_name}
              </span>
              {len ? (
                <span
                  onClick={(e) => {
                    handleView(cargo_owner_res_info_list, e);
                  }}
                  className="cursors"
                  style={{ color: '#3D66FE', width: 40 }}
                >
                  （{len || ''}）
                </span>
              ) : (
                ''
              )}
            </div>
          );
        };
        break;
      // default:
      //   return (item.render = (value: string) => (
      //     <div className="info overwidth">{value}</div>
      //   ));
    }
  };

  const exportItem = async (e: any, requestForm: any) => {
    const data = await XlbFetch.post(
      process.env.BASE_URL + '/erp-mdm/hxl.erp.delivery.cargo.owner.conf.export',
      { ...requestForm },
    );
    if (data.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success(data?.data);
    }
  };

  tableList.map((v) => Render(v));

  const copy = () => {
    NiceModal.show(copyItem, { fetchData: refresh });
  };
  // 批量删除
  const batchDelete = async (selects: any[]) => {
    console.log(selects, 'batchDelete');
    const res = await Api.batchDelete({ ids: selects });
    if (res?.code === 0) {
      message.success('删除成功');
      refresh();
    }
  };

  const handleRecord = () => {
    NiceModal.show(modifyRecord);
  };

  const imports = async () => {
    XlbImportModal({
      subTitle: '多次导入相同门店即为导入更新',
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`,
      templateName: '配送中心货主配置导入',
      callback: (res) => {
        if (res.code === 0) {
          refresh();
        }
      },
    });
  };
  useEffect(() => {
    if (!visible) {
      setKeyWord('');
      set_owner_type(null);
    }
  }, [visible]);

  useEffect(() => {
    console.log(keyWord, owner_type);
    let result: any[] = [];
    let result2: any[] = [];
    if (owner_type) {
      dataSource.forEach((_) => {
        if (_?.owner_type === owner_type) {
          result.push(_);
        }
      });
    }
    if (keyWord) {
      (owner_type ? result : dataSource).forEach((_) => {
        if (
          _.owner_code.includes(keyWord.trim()) ||
          _.source_name.includes(keyWord.trim()) ||
          _.source_code.includes(keyWord.trim())
        ) {
          result2.push(_);
        }
      });
    }
    if (owner_type) {
      setData(result);
    }
    if ((owner_type && keyWord) || (!owner_type && keyWord)) {
      setData(result2);
    }
    if (!owner_type && !keyWord) {
      setData(dataSource);
    }
  }, [keyWord, owner_type]);
  return (
    <XlbPageContainer
      url={'/erp-mdm/hxl.erp.delivery.cargo.owner.conf.page'}
      tableColumn={tableList}
      prevPost={prevPost}
      immediatePost={true}
    >
      <ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource = [],
          selectRowKeys = [],
          requestForm,
        }) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              <XlbButton
                key="query"
                label="查询"
                type="primary"
                disabled={loading}
                onClick={() => {
                  fetchData?.();
                }}
                icon={<span className="iconfont icon-sousuo" />}
              />
              {hasAuth(['配送中心货主配置', '编辑']) && (
                <XlbButton
                  label="同步"
                  type="primary"
                  onClick={() => synchronize()}
                  icon={<XlbIcon name="edit" />}
                />
              )}
              {hasAuth(['配送中心货主配置', '编辑']) && (
                <XlbButton
                  label="新增"
                  type="primary"
                  onClick={() => addOrEdit()}
                  icon={<XlbIcon name="jia" />}
                />
              )}
              {/* {hasAuth(['配送中心货主配置', '删除']) && (
                <XlbButton
                  label="批量删除"
                  type="primary"
                  icon={<XlbIcon name="shanchu" />}
                  disabled={!selectRowKeys.length}
                  onClick={() => batchDelete(selectRowKeys)}
                />
              )} */}
              {/* {hasAuth(['配送中心货主配置', '导入']) && (
                  <XlbDropdownButton
                    // @ts-ignore
                    trigger="click"
                    dropList={[{ label: '导入' }, { label: '导入更新' }]}
                    dropdownItemClick={async (value: number) => {
                      switch (value) {
                        case 0:
                          await XlbImportModal({
                            importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.delivery.cargo.owner.conf.import`,
                            templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.delivery.cargo.owner.conf.template.download`,
                            templateName: '导入模板',
                          });

                          break;
                        case 1:
                          await XlbImportModal({
                            importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.item.update.import`,
                            templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.itemsupdatetemplate.download`,
                            templateName: '导入更新模板',
                          });
                          break;
                      }
                      fetchData();
                    }}
                    label={'导入'}
                  ></XlbDropdownButton>
                )} */}
              {hasAuth(['配送中心货主配置', '导入']) && (
                <XlbButton
                  label="导入"
                  type="primary"
                  onClick={imports}
                  icon={<XlbIcon name="daoru" />}
                />
              )}
              {hasAuth(['配送中心货主配置', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  disabled={!dataSource.length}
                  onClick={(e) => exportItem(e, requestForm)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
              {hasAuth(['配送中心货主配置', '编辑']) && (
                <XlbButton
                  label="复制"
                  type="primary"
                  onClick={() => copy()}
                  icon={<XlbIcon name="fuzhi" />}
                />
              )}
              {hasAuth(['配送中心货主配置', '查询']) && (
                <XlbButton
                  label="修改记录"
                  type="primary"
                  onClick={handleRecord}
                  icon={<XlbIcon name="shenqing" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <SearchForm>
        <XlbForm formList={formList} form={form} isHideDate={true} />
      </SearchForm>
      <Table primaryKey="id" selectMode="multiple" />
      <XlbModal
        className="modal-huozhufanwei"
        title="货主范围"
        width={800}
        onOk={() => {
          setVisible(false);
        }}
        onCancel={() => {
          setVisible(false);
        }}
        isCancel={false}
        isConfirm={false}
        open={visible}
      >
        <div style={{ padding: 10 }}>
          <header style={{ display: 'flex', gap: 10, marginBottom: 10 }}>
            <div>
              关键字：{' '}
              <XlbInput onChange={(e) => setKeyWord(e.target.value)}></XlbInput>
            </div>

            <div>
              货主类型：
              <XlbSelect onChange={(e) => set_owner_type(e)}>
                {cargoList.map((_) => {
                  return (
                    <XlbSelect.Option key={_.value} value={_.value}>
                      {_.label}
                    </XlbSelect.Option>
                  );
                })}
              </XlbSelect>
            </div>
          </header>
          <XlbTable
            total={data.length || 0}
            style={{ height: 300 }}
            columns={detailColumn}
            primaryKey="key"
            dataSource={data}
            key={data.length}
          ></XlbTable>
        </div>
      </XlbModal>
    </XlbPageContainer>
  );
};
export default Index;
