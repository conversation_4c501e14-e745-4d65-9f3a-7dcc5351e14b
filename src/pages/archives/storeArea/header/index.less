.form_box {
  padding: 3px 0 3px 0;
}

.table_box {
  :global .art-table-body {
    min-height: calc(100vh - 180px);
  }
}
.table_fold_box {
  :global .art-table-body {
    min-height: calc(100vh - 170px);
  }
}
.layoutBg {
  background-color: #fff;
}

.leftGoods {
  float: left;
  width: 200px;
  height: 100%;
  overflow: hidden;
  background: #fff;
  border-right: 1px solid #ccc;
}
.button_box {
  padding: 6px 0 0 0;
  border-bottom: 1px solid @color_line2;
}
.goodsFiles {
  .ant-layout-sider {
    background-color: #fff;
  }
  .ant-layout {
    background-color: #fff;
  }
}

.context {
  height: calc(100vh - 78px);

  :global .xlb-ant-spin-nested-loading {
    height: 100%;
  }
  :global .xlb-ant-spin-container {
    height: 100%;
  }
  :global .xlb-detail-page {
    height: 100%;
  }
  :global .ant-form,
  :global .ant-form > .xlb-ant-space {
    height: 100%;
  }
  :global .xlb-ant-space-item:last-child {
    display: flex;
    flex: 1;
    flex-direction: column;
    :global .xlb_table {
      flex: 1 !important;
    }
  }
}
.storeGroupContainer {
  :global {
    .xlb-pro-pageContainer{
      padding-top: 12px !important;
    }
  }
}
