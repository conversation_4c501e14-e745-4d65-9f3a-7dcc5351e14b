import { columnWidthEnum } from '@/data/common/constant';
import { hasAuth } from '@/utils';
import { ProForm } from '@ant-design/pro-form';
import {
  XlbBaseUpload,
  XlbIcon,
  XlbInput,
  XlbInputNumber,
  XlbSelect,
  XlbTableColumnProps,
  XlbTooltip,
} from '@xlb/components';
import type { ArtColumn } from 'ali-react-table';
import Decimal from 'decimal.js';

const handleKeyDown = (e: any) => {
  if (e.key === 'Enter') {
    e.preventDefault(); // 阻止 Enter 键的默认行为
  }
};

const getBasicQuantity = (ratio: any, quantity: any) => {
  return new Decimal(quantity ?? 0).mul(new Decimal(ratio ?? 0)).toNumber();
};

const getBasicPrice = (ratio: any, price: any) => {
  return new Decimal(price ?? 0).div(new Decimal(ratio ?? 0)).toNumber();
};

const getMoney = (price: any, quantity: any) => {
  return new Decimal(price ?? 0).mul(new Decimal(quantity ?? 0)).toNumber();
};

const getPrice = (ratio: any, basicPrice: any) => {
  return new Decimal(basicPrice ?? 0).mul(new Decimal(ratio ?? 0)).toNumber();
};

const getQuantity = (ratio: any, basicQuantity: any) => {
  return new Decimal(basicQuantity ?? 0)
    .div(new Decimal(ratio ?? 0))
    .toNumber();
};

// 单价 / （（税率/100）+ 1））
const getNoTaxPrice = (input_tax_rate: any, price: any) => {
  return new Decimal(price ?? 0)
    .div(
      new Decimal(input_tax_rate ?? 0)
        .div(new Decimal(100))
        .add(new Decimal(1)),
    )
    .toNumber();
};

// 金额 / （（税率/100）+ 1））
const getNoTaxMoney = (input_tax_rate: any, money: any) => {
  return new Decimal(money ?? 0)
    .div(
      new Decimal(input_tax_rate ?? 0)
        .div(new Decimal(100))
        .add(new Decimal(1)),
    )
    .toNumber();
};

// 更新基本单价、金额、金额（去税）
const changePrice = (form: any, e: any, row: any, index: any) => {
  const basicPrice = getBasicPrice(row.ratio, e);
  form.setFieldValue(['details', index['index'], 'basic_price'], basicPrice);

  const money = getMoney(e, row.quantity);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

const changeRatio = (form: any, e: any, row: any, index: any) => {
  let unitName = row.unit;
  row.units.forEach((item: any) => {
    if (item.value === e) {
      unitName = item.label;
    }
  });

  form.setFieldValue(['details', index['index'], 'unit'], unitName);
  form.setFieldValue(['details', index['index'], 'ratio'], e);

  const price = getPrice(e, row.basic_price);
  form.setFieldValue(['details', index['index'], 'price'], price);

  const quantity = getQuantity(e, row.basic_quantity);
  form.setFieldValue(['details', index['index'], 'quantity'], quantity);

  const money = getMoney(price, quantity);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

// 更新基本数量、金额
const changeQuantity = (form: any, e: any, row: any, index: any) => {
  const basicQuantity = getBasicQuantity(row.ratio, e);
  form.setFieldValue(
    ['details', index['index'], 'basic_quantity'],
    basicQuantity,
  );

  const money = getMoney(row.price, e);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

// 基本单价更新后，更新单价和金额
const changeBasicPrice = (form: any, e: any, row: any, index: any) => {
  const price = getPrice(row.ratio, e);
  form.setFieldValue(['details', index['index'], 'price'], price);

  const money = getMoney(price, row.quantity);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

// 基本数量更新后，更新数量和金额
const changeBasicQuantity = (form: any, e: any, row: any, index: any) => {
  const quantity = getQuantity(row.ratio, e);
  form.setFieldValue(['details', index['index'], 'quantity'], quantity);

  const money = getMoney(quantity, row.price);
  form.setFieldValue(['details', index['index'], 'money'], money);
};

export const renderProductDetailsTableList = (obj: any) => {
  return [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center',
    },
    {
      name: '商品代码',
      code: 'item_code',
      align: 'center',
      width: 120,
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: 220,
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 120,
    },
    {
      name: '采购规格',
      code: 'purchase_spec',
      width: 120,
    },
    {
      name: '单位',
      code: 'ratio',
      width: 120,
      render(text: any, row: any, index: any) {
        row.units = Array.from(
          new Set([
            JSON.stringify({
              label: row.delivery_unit,
              value: row.delivery_ratio,
            }),
            JSON.stringify({ label: row.basic_unit, value: 1 }),
            JSON.stringify({
              label: row.purchase_unit,
              value: row.purchase_ratio,
            }),
            JSON.stringify({ label: row.stock_unit, value: row.stock_ratio }),
            JSON.stringify({
              label: row.wholesale_unit,
              value: row.wholesale_ratio,
            }),
          ]),
        ).map((item) => {
          return JSON.parse(item);
        });

        return row._edit &&
          obj.state === 'INIT' &&
          obj.type === 'DIRECT_SUPPLY_RESERVE' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                return (
                  <ProForm.Item
                    noStyle
                    name={['details', index['index'], 'unit']}
                  >
                    <XlbSelect
                      width={80}
                      options={row.units || []}
                      defaultValue={row.purchase_unit || 1}
                      allowClear={false}
                      onChange={(e) => {
                        changeRatio(form, e, row, index);
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : (
          row.unit
        );
      },
    },
    {
      name: '数量',
      code: 'quantity',
      width: 120,
      align: 'right',
      render(text: any, row: any, index: any) {
        return row._edit &&
          obj.state === 'INIT' &&
          obj.type === 'DIRECT_SUPPLY_RESERVE' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                return (
                  <ProForm.Item
                    name={['details', index['index'], 'quantity']}
                    noStyle
                  >
                    <XlbInputNumber
                      onKeyDown={handleKeyDown}
                      min={0}
                      precision={4} // 设置小数点最多为 4 位
                      formatter={(value) => {
                        // 格式化显示的值，确保只能是数字和小数点
                        return `${value}`.replace(/[^0-9.]/g, '');
                      }}
                      parser={(value) => {
                        // 解析输入的值，确保只能是数字和小数点
                        return value ? value.replace(/[^0-9.]/g, '') : '';
                      }}
                      onChange={(e) => {
                        changeQuantity(form, e, row, index);
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : (
          text
        );
      },
    },
    {
      name: '实发数量',
      code: 'actual_delivered_quantity',
      width: 120,
      align: 'right',
      // render(text: any, row: any, index: any) {
      //   return row._edit && obj?.confirm_state === 'SUPPLIER_CONFIRM' && obj?.state === 'AUDIT' ? (
      //     <div onClick={(e) => e.stopPropagation()}>
      //       <ProForm.Item noStyle shouldUpdate>
      //         {(form) => {
      //           return (
      //             <ProForm.Item name={['details', index['index'], 'actual_delivered_quantity']} noStyle>
      //               <XlbInputNumber
      //                 onKeyDown={handleKeyDown}
      //                 min={0}
      //                 precision={4} // 设置小数点最多为 4 位
      //                 formatter={(value) => {
      //                   // 格式化显示的值，确保只能是数字和小数点
      //                   return `${value}`.replace(/[^0-9.]/g, '')
      //                 }}
      //                 parser={(value) => {
      //                   // 解析输入的值，确保只能是数字和小数点
      //                   return value ? value.replace(/[^0-9.]/g, '') : ''
      //                 }}
      //               />
      //             </ProForm.Item>
      //           )
      //         }}
      //       </ProForm.Item>
      //     </div>
      //   ) : (
      //     text
      //   )
      // }
    },
    {
      name: '实收数量',
      code: 'receive_quantity',
      width: 120,
      align: 'right',
      render(text: any, row: any, index: any) {
        return row._edit &&
          obj?.confirm_state === 'SUPPLIER_CONFIRM' &&
          obj?.state === 'AUDIT' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                return (
                  <ProForm.Item
                    name={['details', index['index'], 'receive_quantity']}
                    noStyle
                  >
                    <XlbInputNumber
                      onKeyDown={handleKeyDown}
                      min={0}
                      precision={4} // 设置小数点最多为 4 位
                      formatter={(value) => {
                        // 格式化显示的值，确保只能是数字和小数点
                        return `${value}`.replace(/[^0-9.]/g, '');
                      }}
                      parser={(value) => {
                        // 解析输入的值，确保只能是数字和小数点
                        return value ? value.replace(/[^0-9.]/g, '') : '';
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : (
          text
        );
      },
    },
    {
      name: '单价',
      code: 'price',
      width: 120,
      align: 'right',
      render(text: any, row: any, index: any) {
        return row._edit &&
          obj.state === 'INIT' &&
          obj.type === 'DIRECT_SUPPLY_RESERVE' &&
          !obj?.isAdd &&
          hasAuth(['门店订单/采购价', '编辑']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                return (
                  <ProForm.Item
                    name={['details', index['index'], 'price']}
                    noStyle
                  >
                    <XlbInputNumber
                      onKeyDown={handleKeyDown}
                      precision={4} // 设置小数点最多为 4 位
                      formatter={(value) => {
                        // 格式化显示的值，确保只能是数字和小数点
                        return `${value}`.replace(/[^0-9.]/g, '');
                      }}
                      parser={(value) => {
                        // 解析输入的值，确保只能是数字和小数点
                        return value ? value.replace(/[^0-9.]/g, '') : '';
                      }}
                      onChange={(e) => {
                        changePrice(form, e, row, index);
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : hasAuth(['门店订单/采购价', '查询']) ? (
          // 兼容text为null的情况
          text?.toFixed(4)
        ) : (
          '****'
        );
      },
    },
    {
      name: '金额',
      code: 'money',
      width: 120,
      align: 'right',
      render(text: any, row: any, index: any) {
        return hasAuth(['门店订单/采购价', '查询']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item
              noStyle
              shouldUpdate
              dependencies={['price', 'quantity']}
            >
              {({ getFieldValue }) => {
                const price = getFieldValue([
                  'details',
                  index['index'],
                  'price',
                ]);
                const quantity = getFieldValue([
                  'details',
                  index['index'],
                  'quantity',
                ]);
                const money = getMoney(price, quantity);
                return money?.toFixed(2);
              }}
            </ProForm.Item>
          </div>
        ) : (
          '****'
        );
      },
    },
    {
      name: '实收金额',
      code: 'receive_money',
      width: 120,
      align: 'right',
      render(text: any) {
        return hasAuth(['门店订单/采购价', '查询'])
          ? text == null || text == undefined || text == ''
            ? '-'
            : text
          : '****';
      },
    },
    {
      name: '进项税率',
      code: 'input_tax_rate',
      width: 120,
      align: 'right',
      // features: { format: 'COMPARE' }
      render(text: any, row: any, index: any) {
        // text保留两位小数
        return text ? text.toFixed(2) + '%' : '0.00%';
      },
    },
    {
      name: '单价（去税）',
      code: 'no_tax_price',
      width: 120,
      align: 'right',
      render(text: any, row: any, index: any) {
        return hasAuth(['门店订单/采购价', '查询']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate dependencies={['price']}>
              {({ getFieldValue }) => {
                const price = getFieldValue([
                  'details',
                  index['index'],
                  'price',
                ]);
                const no_tax_price = getNoTaxPrice(row.input_tax_rate, price);
                return no_tax_price?.toFixed(4);
              }}
            </ProForm.Item>
          </div>
        ) : (
          '****'
        );
      },
    },
    {
      name: '金额（去税）',
      code: 'no_tax_money',
      width: 120,
      align: 'right',
      render(text: any, row: any, index: any) {
        return hasAuth(['门店订单/采购价', '查询']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate dependencies={['price']}>
              {({ getFieldValue }) => {
                const money = getFieldValue([
                  'details',
                  index['index'],
                  'money',
                ]);
                const no_tax_money = getNoTaxMoney(row.input_tax_rate, money);
                return no_tax_money?.toFixed(2);
              }}
            </ProForm.Item>
          </div>
        ) : (
          '****'
        );
      },
    },
    {
      name: '换算率',
      code: 'ratio',
      width: 120,
      align: 'right',
      features: { format: 'MONEY' },
    },
    {
      name: '基本单位',
      code: 'basic_unit',
      width: 120,
    },
    {
      name: '基本数量',
      code: 'basic_quantity',
      width: 120,
      align: 'right',
      features: { format: 'QUANTITY' },
    },
    {
      name: '基本单价',
      code: 'basic_price',
      width: 120,
      align: 'right',
      features: { format: 'PRICE' },
      render(text: any, row: any, index: any) {
        return row._edit &&
          obj.state === 'INIT' &&
          obj.type === 'DIRECT_SUPPLY_RESERVE' &&
          !obj?.isAdd &&
          hasAuth(['门店订单/采购价', '编辑']) ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle shouldUpdate>
              {(form) => {
                return (
                  <ProForm.Item
                    name={['details', index['index'], 'basic_price']}
                    noStyle
                  >
                    <XlbInputNumber
                      onKeyDown={handleKeyDown}
                      precision={4} // 设置小数点最多为 4 位
                      formatter={(value) => {
                        // 格式化显示的值，确保只能是数字和小数点
                        return `${value}`.replace(/[^0-9.]/g, '');
                      }}
                      parser={(value) => {
                        // 解析输入的值，确保只能是数字和小数点
                        return value ? value.replace(/[^0-9.]/g, '') : '';
                      }}
                      onChange={(e) => {
                        changeBasicPrice(form, e, row, index);
                      }}
                    />
                  </ProForm.Item>
                );
              }}
            </ProForm.Item>
          </div>
        ) : hasAuth(['门店订单/采购价', '查询']) ? (
          text?.toFixed(4)
        ) : (
          '****'
        );
      },
    },
    {
      name: '保质期',
      code: 'period',
      width: 120,
    },
    {
      name: '备注',
      code: 'memo',
      width: 120,
      render(text: any, row: any, index: any) {
        return row._edit &&
          obj.state === 'INIT' &&
          obj.type === 'DIRECT_SUPPLY_RESERVE' ? (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item noStyle name={['details', index['index'], 'memo']}>
              <XlbInput width={80} />
            </ProForm.Item>
          </div>
        ) : (
          text
        );
      },
    },
    {
      name: '商品报告',
      code: 'item_report',
      width: 150,
      render(value: any, row: any, index: any): React.ReactNode {
        return (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item
              noStyle
              name={['details', index['index'], 'item_report']}
            >
              <XlbBaseUpload
                uploadText="附件"
                multiple={false}
                mode={obj.state === 'AUDIT' ? 'look' : 'textButton'}
                action="/erp/hxl.erp.storeorder.file.upload"
                listType={'picture'}
                accept={'image'}
                data={{
                  fid: row.fid || '',
                  type: 'REPORT',
                  item_id: row.item_id || '',
                }}
                maxCount={1}

                // fileList={uploadData}
              />
            </ProForm.Item>
          </div>
        );
      },
    },
    {
      name: '温度',
      code: 'temperature',
      width: 150,
      render(text: any, row: any, index: any): React.ReactNode {
        return row._edit &&
          obj.state === 'INIT' &&
          obj.type === 'DIRECT_SUPPLY_RESERVE' ? (
          <>
            <div onClick={(e) => e.stopPropagation()}>
              <ProForm.Item
                noStyle
                name={['details', index['index'], 'temperature']}
              >
                <XlbInputNumber
                  onKeyDown={handleKeyDown}
                  min={-100}
                  max={200}
                />
              </ProForm.Item>
            </div>
          </>
        ) : (
          row?.temperature
        );
      },
    },
    {
      name: '温度图片',
      code: 'image_urls',
      width: 150,
      render(value: any, row: any, index: any): React.ReactNode {
        return (
          <div onClick={(e) => e.stopPropagation()}>
            <ProForm.Item
              noStyle
              name={['details', index['index'], 'image_urls']}
            >
              <XlbBaseUpload
                uploadText="附件"
                multiple={false}
                mode={obj.state === 'AUDIT' ? 'look' : 'textButton'}
                action="/erp/hxl.erp.storeorder.file.upload"
                listType={'picture'}
                accept={'image'}
                data={{
                  fid: row.fid || '',
                  type: 'TEMPERATURE',
                  item_id: row.item_id || '',
                }}
                maxCount={1}
                // fileList={uploadData}
              />
            </ProForm.Item>
          </div>
        );
      },
    },
  ];
};
export const receiptStatus: any[] = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
  {
    label: '作废',
    value: 'INVALID',
    type: 'danger',
  },
  {
    label: '待支付',
    value: 'PAID',
    type: 'warning',
  },
];
export const fidType: any[] = [
  {
    label: '直供',
    value: 'DIRECT',
  },
  {
    label: '仓配',
    value: 'STOREHOUSE_DELIVERY',
  },
];
// 确认状态option
export const confirmStatus: any[] = [
  { label: '未确认', value: 'INIT', type: 'info' },
  { label: '供应商已确认', value: 'SUPPLIER_CONFIRM', type: 'warning' },
  { label: '已确认收货', value: 'STORE_CONFIRM', type: 'success' },
  { label: '已作废', value: 'INVALID', type: 'danger' },
];
export const Columns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    code: 'fid',
    width: 180,
    features: { sortable: true },
  },
  // {
  //   name: '配送日期',
  //   code: 'update_time',
  //   width: 130,
  //   features: { sortable: true, format: 'TIME' },
  //   render(text, record) {
  //     return text || '/'
  //   }
  // },
  //   {
  //   name: '有效期',
  //   code: 'valid_date',
  //   width: 130,
  //   features: { sortable: true, format: 'TIME' },
  // },
  // {
  //   name: '配送日期',
  //   code: 'update_time',
  //   width: 130,
  //   features: { sortable: true, format: 'TIME' },
  //   render(text, record) {
  //     return text || '/'
  //   }
  // },
  {
    name: '预计到货日期',
    code: 'estimated_arrival_date_str',
    width: 130,
  },
  {
    name: '收货门店',
    code: 'store_name',
    width: 150,
    features: { sortable: false },
  },
  // {
  //   name: '发货门店',
  //   code: 'out_store_id',
  //   width: 100,
  //   features: { sortable: true },
  //   render(text, record) {
  //     return record.out_store_name
  //   }
  // },
  {
    name: '供应商',
    code: 'supplier_id',
    width: 150,
    features: { sortable: true },
    render: (text, record) => {
      return record.supplier_name;
    },
  },
  {
    name: '单据类型',
    code: 'type',
    width: 150,
    features: { sortable: true },
    render(text, record) {
      return <div>{text == 'STOREHOUSE_DELIVERY' ? '仓配' : '直供'}</div>;
    },
  },
  {
    name: '下游单据',
    code: 'ref_order_type',
    width: 130,
  },
  {
    name: '下游单据号',
    code: 'ref_order_fid',
    width: 200,
  },
  {
    name: '单据金额(含税)',
    code: 'money',
    width: 150,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
    render(text, record) {
      return hasAuth(['门店订单/采购价', '查询']) ? text : '****';
    },
  },
  {
    name: '单据金额(去税)',
    code: 'no_tax_money',
    width: 150,
    features: { format: 'MONEY' },
    align: 'right',
    render(text, record) {
      return hasAuth(['门店订单/采购价', '查询'])
        ? record?.type == 'STOREHOUSE_DELIVERY'
          ? '-'
          : text
        : '****';
    },
  },
  {
    name: '实收金额',
    code: 'actually_received_total_money',
    width: 150,
    features: { sortable: true, format: 'MONEY' },
    align: 'right',
    render(text, record) {
      return hasAuth(['门店订单/采购价', '查询'])
        ? text == null || text == undefined || text == ''
          ? '-'
          : text
        : '****';
    },
  },
  {
    name: '数量',
    code: 'quantity',
    width: 150,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '实收数量',
    code: 'actually_received_quantity',
    width: 150,
    features: { sortable: true, format: 'QUANTITY' },
    align: 'right',
    render(text, record) {
      return text == null || text == undefined || text == '' ? '-' : text;
    },
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单据状态',
    code: 'state',
    width: 150,
    features: { sortable: true },
    render(text, record) {
      const item = receiptStatus.find((v) => v.value === text);
      return (
        <div className={`${item ? item.type : ''}`}>
          {item ? item.label : ''}
        </div>
      );
    },
  },
  // 确认状态
  {
    name: '确认状态',
    code: 'confirm_state',
    width: 150,
    features: { sortable: true },
    render(text, record) {
      const item = confirmStatus.find((v) => v.value === text);
      if (record?.type == 'STOREHOUSE_DELIVERY') {
        return <div>{'-'}</div>;
      }
      return (
        <div className={`${item ? item.type : ''}`}>
          {item ? item.label : ''}
        </div>
      );
    },
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '确认人',
    code: 'confirm_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '确认时间',
    code: 'confirm_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '作废人',
    code: 'invalid_by',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '作废时间',
    code: 'invalid_time',
    width: 150,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '留言备注',
    code: 'all_memo',
    width: 150,
    features: { sortable: true },
  },
];

export const productDetailsTableList: ArtColumn[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 120,
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 220,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 120,
  },
  // {
  //   name: '商品属性',
  //   code: 'store_item_attribute',
  //   width: 120
  // },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 120,
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 120,
  },
  {
    name: '数量',
    code: 'quantity',
    features: { format: 'QUANTITY' },
    align: 'right',
    width: 120,
  },
  {
    name: '单价',
    code: 'price',
    features: { format: 'PRICE' },
    align: 'right',
    width: 120,
    render(text, record) {
      return hasAuth(['门店订单/采购价', '查询']) ? text : '****';
    },
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { format: 'MONEY' },
    align: 'right',
    render(text, record) {
      return hasAuth(['门店订单/采购价', '查询']) ? text : '****';
    },
  },
  {
    name: '进项税率%',
    code: 'input_tax_rate',
    width: 120,
    align: 'right',
    features: { format: 'COMPARE' },
  },
  {
    name: '单价（去税）',
    code: 'no_tax_price',
    width: 120,
    features: { format: 'PRICE' },
    align: 'right',
    render(text, record) {
      return hasAuth(['门店订单/采购价', '查询']) ? text : '****';
    },
  },
  {
    name: '金额（去税）',
    code: 'no_tax_money',
    width: 120,
    features: { format: 'MONEY' },
    align: 'right',
    render(text, record) {
      return hasAuth(['门店订单/采购价', '审核']) ? text : '****';
    },
  },
  {
    name: '换算率',
    code: 'ratio',
    width: 120,
    align: 'right',
    features: { format: 'COMPARE' },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 120,
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    features: { format: 'QUANTITY' },
    align: 'right',
    width: 120,
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 120,
    features: { format: 'PRICE' },
    align: 'right',
    render(text, record) {
      return hasAuth(['门店订单/采购价', '查询']) ? text : '****';
    },
  },
  // {
  //   name: '生产日期',
  //   code: 'producing_date',
  //   width: 120
  // },
  // {
  //   name: '到期日',
  //   code: 'expire_date',
  //   width: 120
  // },
  {
    name: '保质期',
    code: 'period',
    width: 120,
  },
  {
    name: '备注',
    code: 'memo',
    width: 120,
  },
];
export const productSummaryTableList: ArtColumn[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'operation',
    align: 'center',
    width: 120,
  },
  {
    name: '商品条码',
    code: 'item_code',
    width: 120,
  },
  {
    name: '商品名称',
    code: 'item_code',
    width: 120,
  },
  {
    name: '商品属性',
    code: 'item_code',
    width: 120,
  },
  {
    name: '采购规格',
    code: 'item_code',
    width: 120,
  },
  {
    name: '采购单位',
    code: 'item_code',
    width: 120,
  },
  {
    name: '数量',
    code: 'item_code',
    features: { format: 'QUANTITY' },
    align: 'right',
    width: 120,
  },
  {
    name: '单价',
    code: 'item_code',
    features: { format: 'PRICE' },
    align: 'right',
    width: 120,
  },
  {
    name: '金额',
    code: 'item_code',
    features: { format: 'MONEY' },
    align: 'right',
    width: 120,
  },
  {
    name: '进项税率%',
    code: 'item_code',
    width: 120,
    features: { format: 'COMPARE' },
    align: 'right',
  },
  {
    name: '单价（去税）',
    code: 'item_code',
    width: 120,
    features: { format: 'PRICE' },
    align: 'right',
  },
  {
    name: '金额（去税）',
    code: 'item_code',
    width: 120,
    features: { format: 'MONEY' },
    align: 'right',
  },
  {
    name: '换算率',
    code: 'item_code',
    width: 120,
    features: { format: 'COMPARE' },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'item_code',
    width: 120,
  },
  {
    name: '基本数量',
    code: 'item_code',
    width: 120,
    features: { format: 'QUANTITY' },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'item_code',
    width: 120,
    features: { format: 'PRICE' },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'item_code',
    features: { format: 'TIME' },
    width: 120,
  },
  {
    name: '到期日',
    code: 'item_code',
    width: 120,
    features: { format: 'TIME' },
  },
  {
    name: '保质期',
    code: 'item_code',
    width: 120,
    features: { format: 'TIME' },
  },
  {
    name: '备注',
    code: 'item_code',
    width: 120,
  },
];

export const itemDirectTableList: ArtColumn[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 220,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '要货数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '实发数量',
    code: 'delivery_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '实发率',
  //   code: 'delivery_rate',
  //   width: 120,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  {
    name: '直配数量',
    code: 'basic_purchase_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];
export const deliveryState = [
  {
    label: '待收单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '已收单',
    value: 'AUDIT',
    type: 'danger',
  },
  {
    label: '配货中',
    value: 'PICKING',
    type: 'warning',
  },
  {
    label: '已配货',
    value: 'PICKED',
    type: 'danger',
  },
  {
    label: '已装车',
    value: 'LOADED',
    type: 'danger',
  },
  {
    label: '已发车',
    value: 'DELIVERY',
    type: 'danger',
  },
  {
    label: '已完成',
    value: 'FINISH',
    type: 'success',
  },
];
export const _itemTableListCopy: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '操作',
    code: '_operator',
    align: 'center',
    width: 80,
    lock: true,
  },
  {
    name: '缺货状态',
    code: 'less_status',
    align: 'center',
    features: { sortable: true },
    width: 100,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 220,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true, showShort: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '要货数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '缺货数量',
  //   code: 'less_quantity',
  //   width: 110,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  {
    name: '预发数量',
    code: 'actual_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '实发数量',
    code: 'delivery_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '实发率',
  //   code: 'delivery_rate',
  //   width: 120,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },

  {
    name: '预发基本数量',
    code: 'actual_basic_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '零售价',
  //   code: 'sale_price',
  //   width: 110,
  //   features: { sortable: true },
  //   align: 'right'
  // },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '体积(cm³)',
    code: 'volume',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '补货门店库存',
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>补货门店库存</span>
        <XlbTooltip
          title={
            <>
              <div>成分商品：库存、销量均按关联的制单组合商品展示</div>
              <div>多规格商品：库存、销量均按关联的主规格商品展示</div>
            </>
          }
        >
          <XlbIcon size={12} name="bangzhu"></XlbIcon>
        </XlbTooltip>
      </div>
    ),
    code: 'stock_quantity',
    width: 160,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '中心库存',
    code: 'center_stock_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调出数量',
    code: 'basic_out_quantity',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '原配送价',
    code: 'original_price',
    width: 100,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '配送特价单据号',
    code: 'special_fid',
    width: 210,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];
export const itemLogisticsMode = [
  {
    label: '寄售',
    value: 'CONSIGNMENT',
  },
  {
    label: '越库',
    value: 'SKIP_WAREHOUSE',
  },
  {
    label: '仓配',
    value: 'STOREHOUSE_DELIVERY',
  },
];

export const goodDataColumns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 170,
  },
  {
    name: '代码',
    code: 'item_code',
    width: 160,
  },
  {
    name: '调整原因',
    code: 'memo',
    width: 160,
  },
  {
    name: '调整前数量',
    code: 'quantity',
    width: 100,
  },
  {
    name: '调整后数量',
    code: 'new_quantity',
    width: 100,
    render(value: any, row: any, index: any) {
      return <div style={{ color: 'red' }}>{value}</div>;
    },
  },
];
