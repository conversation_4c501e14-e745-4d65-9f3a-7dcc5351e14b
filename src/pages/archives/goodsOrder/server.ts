// import request from '@/utils/request'
import {XlbFetch as ErpRequest } from '@xlb/utils'

// 实时更新
export const gettimechange = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.itemorderattribute.update', data)
}

// 实时更新
export const getTimeChangeSpecial = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.itemorderfeature.update', data)
}
// 实时更新
export const storeItemchange = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorderattribute.update', data)
}

// 批量修改
const batchUpdate = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.itemorderattribute.batchupdate', data)
}
// 批量修改门店
const storeBatchUpdate = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorderattribute.batchupdate', data)
}

// 批量修改--特性
const batchUpdateSpecial = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.itemorderfeature.batchupdate', data)
}

// 复制--特性
const copySpecial = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.itemorderfeature.copy', data)
}
// 复制--特性
const copyStore = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorderattribute.copy', data)
}

const deliveryUnitList = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemattr.log.find', { data });
};

export default {
  batchUpdate,
  batchUpdateSpecial,
  storeBatchUpdate,
  copyStore,
  deliveryUnitList,
  copySpecial
}