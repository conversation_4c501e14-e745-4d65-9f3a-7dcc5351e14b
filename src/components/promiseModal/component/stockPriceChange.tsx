import { columnWidthEnum } from '@/data/common/constant';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { stockPickChangeReadInfo } from '../server';

const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '调整门店',
  },
  {
    type: 'select',
    disabled: true,
    name: 'storehouse_name',
    label: '调整仓库',
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
  },
  {
    type: 'select',
    disabled: true,
    name: 'state',
    label: '单据状态',
    options: [
      { label: '制单', value: 'INIT' },
      { label: '审核', value: 'AUDIT' },
      { label: '已完成', value: 'FINISH' },
      { label: '已作废', value: 'CANCEL' },
    ],
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
  },
  {
    type: 'datePicker',
    disabled: true,
    name: 'operate_date',
    label: '调整日期',
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const columnsList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '原成本价',
    code: 'original_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '现成本价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '原基本成本价',
    code: 'original_basic_price',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '现基本成本价',
    code: 'basic_price',
    width: 130,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '成本差',
    code: 'money',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left',
  },
];

const StockPriceChange = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    columnsList.map((i: any) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == 'operation' ? true : i.hidden,
    })) as any[],
  );

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'quantity':
      case 'basic_quantity':
        item.render = (value: any, record: any, index: number) => {
          return <div>{value ? value.toFixed(3) : '0.000'}</div>;
        };
        break;
      case 'price':
      case 'basic_price':
      case 'original_price':
      case 'original_basic_price':
        item.render = (value: any, record: any, index: number) => {
          return <div>{value ? value.toFixed(4) : '0.000'}</div>;
        };
        break;
      case 'money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div>
              {value
                ? (
                    (record?.basic_price - record?.original_basic_price) *
                    record?.basic_quantity
                  ).toFixed(3)
                : '0.000'}
            </div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (fid) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = await stockPickChangeReadInfo({ fid: fid });
    if (res?.code == 0) {
      // 表单的值
      formModel.setFieldsValue({
        ...res?.data,
        operate_date: res.data.operate_date
          ? dayjs(res.data.operate_date)
          : undefined,
        store_order: res.data.request_orders?.map((v: any) => v.fid).join(','),
        create_time: res.data.create_time?.slice(0, 10),
        audit_time: res.data.audit_time?.slice(0, 10),
        updata_time: res.data.updata_time?.slice(0, 10),
      });
      // 列表的值
      setFidDataList(res?.data?.details);
      // 计算合计
      let rowData = res?.data?.details;
      footerData[0] = {
        index: '合计',
        money: rowData
          .reduce(
            (sum: any, v: any) => sum + Number(v?.newRow ? 0 : v.money),
            0,
          )
          .toFixed(2),
        quantity: rowData
          .reduce(
            (sum: any, v: any) => sum + Number(v?.newRow ? 0 : v.quantity),
            0,
          )
          .toFixed(3),
        basic_quantity: rowData
          .reduce(
            (sum: any, v: any) =>
              sum + Number(v?.newRow ? 0 : v.basic_quantity),
            0,
          )
          .toFixed(3),
      };
      setFooterData([...footerData]);
    }
    setTableLoading(false);
  }, 50);

  useEffect(() => {
    openRefOrder(fid);
    itemArrdetail.map((v) => tableRender(v));
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formList}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default StockPriceChange;
