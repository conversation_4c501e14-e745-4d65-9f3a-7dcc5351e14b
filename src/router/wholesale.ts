interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/xlb_erp/customerGoodsAttributes/index',
    component: '@/pages/wholesale/customerGoodsAttributes/index',
    title: '客户商品属性',
    subTitle: '业务设置',
    subMenu: 'wholesale',
    tabClass: 'customerGoodsAttributes',
  },
  {
    path: '/xlb_erp/wholeSaleAnalyze/index',
    component: '@/pages/wholesale/wholeSaleAnalyze/index',
    title: '批发分析',
    subTitle: '数据查询',
    subMenu: 'wholesale',
    tabClass: 'wholeSaleAnalyze',
  },
  {
    path: '/xlb_erp/wholesalePrice/index',
    component: '@/pages/wholesale/wholesalePrice/index',
    title: '批发价',
    subTitle: '业务设置',
    subMenu: 'wholesale',
    tabClass: 'wholesalePrice',
  },
  {
    path: '/xlb_erp/wholesalePriceAdjustment/index',
    component: '@/pages/wholesale/wholesalePriceAdjustment/header/index',
    title: '批发调价',
    subTitle: '业务设置',
    subMenu: 'wholesale',
    tabClass: 'wholesalePriceAdjustment',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/wholesaleOrgSetting/index',
    component: '@/pages/wholesale/wholesaleOrgSetting/index',
    title: '批发组织设置',
    subTitle: '业务设置',
    subMenu: 'wholesale',
    tabClass: 'wholesaleOrgSetting',
  },
  {
    path: '/xlb_erp/buyParam/index',
    component: '@/pages/wholesale/buyParam/header/index',
    title: '批发参数',
    subTitle: '业务设置',
    subMenu: 'wholesale',
    tabClass: 'buyParam',
  },
  {
    path: '/xlb_erp/wholeSaleDetail/index',
    component: '@/pages/wholesale/wholeSaleDetail/index',
    title: '批发商品明细',
    subTitle: '数据查询',
    subMenu: 'wholesale',
    tabClass: 'wholeSaleDetail',
  },
];

export { routeList as wholesaleRouteList };
