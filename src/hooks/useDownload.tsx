import Donwload from '@/utils/downloadBlobFile';
import { LStorage } from '@/utils/storage';
import { XlbFetch } from '@xlb/utils';

const useDownload = () => {
  const donwload = new Donwload();
  const downByProgress = async (e: React.MouseEvent) => {
    //  查询下载列表接口
    const res = await XlbFetch('/erp/hxl.erp.storedeliveryday.store.export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        operator: LStorage.get('userInfo')?.name,
        company_id: LStorage.get('userInfo')?.company_id,
        operator_store_id: LStorage.get('userInfo')
          ? LStorage.get('userInfo').store_id
          : '',
      },
    });
    if (res.code === 0) {
      LStorage.set('dcount', res.data);
    }
    donwload.downByProgress(e);
  };
  return {
    downByProgress,
  };
};

export default useDownload;
