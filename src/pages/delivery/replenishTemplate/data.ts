import { columnWidthEnum } from "@/data/common/constant";
import { XlbTableColumnProps } from '@xlb/components';


export const formList: any[] = [
    {
        label: '关键字',
        value: 'keyword',
        type: 'input',
        // placeholder:'名称',
        clear: true,
        check: true,
    },
    {
        label: '组织',
        value: 'org_ids',
        type: 'selects',
        check: true,
        clear: true,
        options: [],
        hidden: true,
    },
]

export const types = [
    {
        label: ' ',
        value: 'NULL'
    },
    {
        label: '按比例',
        value: 'RATIO'
    },
    {
        label: '按金额',
        value: 'MONEY'
    },
    {
        label: '固定金额',
        value: 'FIXED_MONEY'
    }
]


export const goodsType: any[] = [
    {
        label: '主规格商品',
        value: 'MAINSPEC'
    },
    {
        label: '多规格商品',
        value: 'MULTIPLESPEC'
    },
    {
        label: '标准商品',
        value: 'STANDARD',
    },
    {
        label: '组合商品',
        value: 'COMBINATION',
    },
    {
        label: '成分商品',
        value: 'COMPONENT',
    },
    {
        label: '制单组合',
        value: 'MAKEBILL',
    },
    {
        label: '分级商品',
        value: 'GRADING',
    }
]

export const tableColumn: XlbTableColumnProps<any>[] = [
    {
        name: '序号',
        code: '_index',
        width: 60,
        align: 'center'
    },
    {
        name: '补货模板名称',
        code: 'name',
        width: 160,
        features: { sortable: true }
    },
    {
        name: '组织',
        code: 'org_name',
        width: 140,
        features: { sortable: true },
    },
]


export const replenishTemplateArr: XlbTableColumnProps<any>[] = [
    {
        name: '序号',
        code: '_index',
        width: columnWidthEnum.INDEX,
        align: 'center'
    },
    {
        name: '商品代码',
        code: 'item_code',
        width: 110,
        features: { sortable: true }
    },
    {
        name: '商品条码',
        code: 'item_bar_code',
        width: 140,
        features: { sortable: true }
    },
    {
        name: '商品名称',
        code: 'item_name',
        width: 280,
        features: { sortable: true }
    },
    {
        name: '采购规格',
        code: 'purchase_spec',
        width: 160,
        features: { sortable: true }
    },
    {
        name: '商品类型',
        code: 'item_type',
        width: 100,
        features: { sortable: true }
    },
    {
        name: '单位',
        code: 'unit',
        width: 120,
        features: { sortable: true }
    },
    {
        name: '组织采购价',
        code: 'purchase_price',
        width: 140,
        align: 'right',
        features: { sortable: true }
    },
    {
        name: '配送价类型',
        code: 'type',
        width: 150,
        features: { sortable: true }
    },
    {
        name: '数值',
        code: 'value',
        width: 100,
        align: 'right',
        features: { sortable: true }
    },
    {
        name: '配送价',
        code: 'delivery_price',
        width: 100,
        align: 'right',
        features: { sortable: true }
    }
]
