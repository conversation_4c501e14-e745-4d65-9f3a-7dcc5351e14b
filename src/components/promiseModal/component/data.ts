import { columnWidthEnum } from "@/constants";
import { XlbTableColumnProps } from "@xlb/components";
export const ORDER_AUTH_MAP: Record<string, string> = {
  收货单: '采购收货单',
  采购收货单: '采购收货单',
  退货单: '采购退货单',
  采购退货单: '采购退货单',
  调出: '调出单',
  调出单: '调出单',
  调入: '调入单',
  调入单: '调入单',
  库存盘点: '库存盘点',
  库存盘点单: '库存盘点',
  库存调整: '库存调整',
  库存调整单: '库存调整',
  同店转仓: '同店转仓',
  同店转仓单: '同店转仓',
  库存成本调整: '库存成本调整',
  库存成本调整单: '库存成本调整',
  物资进出单: '物资进出单',
  批发销售单: '批发销售单',
  批发退货单: '批发退货单',
  领用进出单: '领用进出单',
};
export const itemTableListDetail: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center'
    },
    {
      name: '操作',
      code: 'operation',
      align: 'center',
      width: 60
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: columnWidthEnum.ITEM_CODE,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '商品条码',
      code: 'item_bar_code',
      width: columnWidthEnum.ITEM_BAR_CODE,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '商品名称',
      code: 'item_name',
      width: 280,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '采购规格',
      code: 'item_spec',
      width: columnWidthEnum.ITEM_SPEC,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '单位',
      code: 'unit',
      width: 80,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '数量',
      code: 'quantity',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '单价',
      code: 'price',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '金额',
      code: 'money',
      width: 120,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '销项税率(%)',
      code: 'tax_rate',
      width: 120,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '税费',
      code: 'tax_money',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '成本',
      code: 'cost_money',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '成本(去税)',
      code: 'no_tax_cost_money',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '单件皮重',
      code: 'tare',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '基本单位',
      code: 'basic_unit',
      width: 110,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '换算率',
      code: 'ratio',
      width: 90,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '基本数量',
      code: 'basic_quantity',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '基本单价',
      code: 'basic_price',
      width: 134,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '时点成本价(去税)',
      code: 'original_no_tax_cost_money',
      width: 160,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '零售单价',
      code: 'sale_price',
      width: 134,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '赠品单位',
      code: 'present_unit',
      width: 110,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '赠品数量',
      code: 'present_quantity',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '生产日期',
      code: 'producing_date',
      width: columnWidthEnum.DATE,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '到期日期',
      code: 'expire_date',
      width: columnWidthEnum.DATE,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '批次号',
      code: 'batch_number',
      width: 120,
      features: { sortable: true },
      align: 'left'
    },
    {
      name: '保质期',
      code: 'period',
      width: 90,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '库存数量',
      code: 'basic_stock_quantity',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '可用库存',
      code: 'basic_available_stock_quantity',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '原价',
      code: 'origin_price',
      width: 110,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '特价单据号',
      code: 'special_fid',
      width: 210,
      features: { sortable: true },
      align: 'right'
    },
    {
      name: '备注',
      code: 'memo',
      width: columnWidthEnum.MEMO,
      features: { sortable: true },
      align: 'left'
    }
  ]