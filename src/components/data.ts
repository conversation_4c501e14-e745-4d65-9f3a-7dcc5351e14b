import abort from '@/assets/images/xlb_erp/orderStatus/abort.png';
import approvalPass from '@/assets/images/xlb_erp/orderStatus/approvalPass.png';
import approvalReject from '@/assets/images/xlb_erp/orderStatus/approvalReject.png';
import cancel from '@/assets/images/xlb_erp/orderStatus/cancel.png';
import checking from '@/assets/images/xlb_erp/orderStatus/checking.png';
import effect from '@/assets/images/xlb_erp/orderStatus/effect.png';
import end from '@/assets/images/xlb_erp/orderStatus/end.png';
import endState from '@/assets/images/xlb_erp/orderStatus/endState.png';
import finish from '@/assets/images/xlb_erp/orderStatus/finish.png';
import handling from '@/assets/images/xlb_erp/orderStatus/handling.png';
import init from '@/assets/images/xlb_erp/orderStatus/init.png';
import partCancel from '@/assets/images/xlb_erp/orderStatus/partCancel.png';
import pass from '@/assets/images/xlb_erp/orderStatus/pass.png';
import reject from '@/assets/images/xlb_erp/orderStatus/reject.png';
import unPay from '@/assets/images/xlb_erp/orderStatus/unPay.png';
import expire from '@/assets/images/xlb_erp/orderStatus/expire.png';
// 表格样式简单配置
export const tableStyle = {
  '--header-row-height': '28px',
  '--row-height': '24px',
  '--header-bgcolor': '#fafafa',
  '--header-color': '#666',
  '--row-color': '#000',
  '--cell-padding': '0',
  fontSize: '13px',
};

// 单据状态对应图标
export const orderStatusIcons: any = {
  INIT: init,
  AUDIT: checking,
  APPROVE: approvalPass,
  APPROVE_REFUSE: approvalReject,
  HANDLE: pass,
  PASS: pass,
  DENY: reject,
  HANDLE_REFUSE: reject,
  HANDLE_ING: handling,
  REFUSE: reject,
  INVALID: cancel,
  HANDLE_ABORT: end,
  ABORT: endState,
  PART_INVALID: partCancel,
  PAID: unPay,
  FINISH: finish,
  TERMINATE: abort,
  EFFECT: effect,
  EXPIRE: expire,
};

// 单据类型
export const ORDER_TYPE = [
  {
    label: '普通订单',
    value: 'NORMAL',
  },
  {
    label: '直供订单',
    value: 'DIRECT_SUPPLY',
  },
];
