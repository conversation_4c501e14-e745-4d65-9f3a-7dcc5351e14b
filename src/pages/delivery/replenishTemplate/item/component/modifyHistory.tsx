import { XlbTable, XlbModal, XlbTableColumnProps } from '@xlb/components'
import { useEffect, useState } from 'react'
import Api from '../../server'

const ModifyHistory = (props: any) => {
  const { visible, setVisible, id } = props
  const [dataSource, setDataSource] = useState<any>([])
  const [pagin, setPagin] = useState({ pageSize: 200, pageNumber: 1, total: 0 })

  const getTableInfo = async (page_size?: number, page_number?: number) => {
    const res = await Api?.getHistory({
      id,
      page_size: page_size || 200,
      page_number: page_number || 0
    })
    if (res?.code === 0) {
      setDataSource(res?.data?.content || [])
      setPagin({
        ...pagin,
        total: res?.data?.total_elements
      })
    }
  }

  useEffect(() => {
    if (visible) {
      getTableInfo()
    }
  }, [visible])
  const tableList: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 60,
      align: 'center'
    },
    {
      name: '修改时间',
      code: 'create_time',
      width: 180
    },
    {
      name: '修改人',
      code: 'create_by',
      width: 110
      // features: { sortable: true }
    },
    {
      name: '操作明细',
      code: 'memo',
      width: 280
      // features: { sortable: true }
    }
  ]

  return (
    <XlbModal
      wrapClassName={'xlbDialog'}
      title={'修改记录'}
      keyboard={false}
      visible={visible}
      maskClosable={false}
      onOk={() => setVisible(false)}
      onCancel={() => setVisible(false)}
      width={750}
      centered
    >
      <XlbTable
        columns={tableList}
        dataSource={dataSource}
        // selectMode="single"
        style={{ height: 300 }}
        primaryKey="id"
        total={pagin.total}
        // pageNum={1}
        // pageSize={100}
        onPaginChange={(page: number, pageSize: number) => {
          console.log('🚀 ~ ModifyHistory ~ pageSize:', pageSize)
          console.log('🚀 ~ ModifyHistory ~ page:', page)
          setPagin({
            total: pagin.total,
            pageNumber: page || 1,
            pageSize
          })
          getTableInfo(pageSize, page - 1)
        }}
      />
    </XlbModal>
  )
}

export default ModifyHistory
