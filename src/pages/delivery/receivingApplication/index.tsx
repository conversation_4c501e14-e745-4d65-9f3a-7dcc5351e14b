import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import { useBaseParams } from '@/hooks/useBaseParams';
import {
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbIcon,
  XlbInput,
  XlbMessage,
  XlbPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { formList, tableColumn } from './data';
import Item from './item';
import {
  batchapprove,
  batchAudit,
  batchDelete,
  batchhandle,
  copy,
  detailbatchexport,
  exportData,
  invalid,
} from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Index = () => {
  const { enable_organization, enable_cargo_owner } = useBaseParams((state) => state);
  const [form] = XlbBasicForm.useForm<any>();
  const [invalidReasonForm] = XlbBasicForm.useForm<any>();
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);
  const [record, setRecord] = useState<any>();
  const checkData = () => {
    const formData = form.getFieldsValue();
    const { compactDatePicker } = form.getFieldsValue(true);
    const data = {
      ...formData,
      audit_date:
        form.getFieldValue('time_type') === 'audit_date'
          ? compactDatePicker
          : null,
      init_date:
        form.getFieldValue('time_type') === 'init_date'
          ? compactDatePicker
          : null,
      create_date:
        form.getFieldValue('time_type') === 'create_date'
          ? compactDatePicker
          : null,
      handle_date:
        form.getFieldValue('time_type') === 'handle_date'
          ? compactDatePicker
          : null,
      invalid_date:
        form.getFieldValue('time_type') === 'invalid_date'
          ? compactDatePicker
          : null,
    };
    return data;
  };
  const prevPost = () => {
    return checkData();
  };
  const handleBatchDelete = async (ids: any, selectRow: any) => {
    // 只删除制单状态的单据
    const initRow = selectRow.filter((v: any) => v.state != 'INIT');
    if (initRow?.length > 0) {
      XlbMessage.error('仅支持制单状态的单据');
      return;
    }
    await XlbTipsModal({
      title: '提示',
      tips: '已选择' + ids?.length + '条数据，确定删除吗？',
      onOk: async () => {
        const res = await batchDelete({ fids: ids });
        if (res?.code === 0) {
          XlbMessage.success('删除成功');
          pageConatainerRef?.current?.fetchData?.();
        }
      },
    });
  };
  const handleCopy = async (selectRow: any) => {
    await XlbTipsModal({
      title: '提示',
      isCancel: true,
      tips: '是否确认复制单据' + selectRow?.[0]?.fid + '？',
      onOk: async () => {
        const res = await copy({ fid: selectRow?.[0]?.fid });
        if (res?.code === 0) {
          pageConatainerRef?.current?.fetchData?.();
          XlbTipsModal({
            title: '提示',
            tips: `已生成新的单据${res?.data?.fid}`,
          });
        }
      },
    });
  };
  const handleAudit = async (ids: any, selectRow: any) => {
    const initRow = selectRow?.filter((v: any) => v.state == 'INIT');
    if (initRow?.length != ids?.length) {
      XlbMessage.error('仅支持制单状态的单据');
      return;
    }
    await XlbTipsModal({
      title: '提示',
      tips: '是否确认审核' + ids?.length + '条数据？',
      onOk: async () => {
        const res = await batchAudit({ fids: ids });
        if (res?.code === 0) {
          XlbMessage.success('审核成功');
          pageConatainerRef?.current?.fetchData?.();
        }
      },
    });
  };
  const handleHandle = async (ids: any, type: boolean) => {
    await XlbTipsModal({
      title: '提示',
      tips:
        '是否确认' +
        (type ? '处理通过' : '处理拒绝') +
        ids?.length +
        '条数据？',
      onOk: async () => {
        const res = await batchhandle({
          fids: ids,
          state: type ? 'PASS' : 'DENY',
        });
        if (res?.code === 0) {
          XlbMessage.success('处理成功');
          pageConatainerRef?.current?.fetchData?.();
        }
      },
    });
  };
  const handleApprove = async (ids: any, type: boolean) => {
    await XlbTipsModal({
      title: '提示',
      tips:
        '是否确认' +
        (type ? '批复通过' : '批复拒绝') +
        ids?.length +
        '条数据？',
      onOk: async () => {
        const res = await batchapprove({
          fids: ids,
          state: type ? 'APPROVE' : 'REFUSE',
        });
        if (res?.code === 0) {
          XlbMessage.success('批复成功');
          pageConatainerRef?.current?.fetchData?.();
        }
      },
    });
  };
  // cons  // 确认作废
  const handleSureCancel = async (selectRow: any) => {
    const flag = await XlbTipsModal({
      tips: (
        <span style={{ wordBreak: 'break-all' }}>
          是否确认作废单据{selectRow?.map((v: any) => v?.fid).join(',')}？
        </span>
      ),
    });
    invalidReasonForm.resetFields();
    if (!flag) return;
    await XlbTipsModal({
      title: '请输入作废原因！',
      isCancel: true,
      tips: (
        <div>
          <XlbBasicForm form={invalidReasonForm}>
            <XlbBasicForm.Item name={'invalid_reason'} label={'作废原因'}>
              <XlbInput.TextArea
                maxLength={50}
                style={{ width: 264, height: 100, resize: 'none' }}
              ></XlbInput.TextArea>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      ),
      onOk: async () => {
        const res = await invalid({
          fids: selectRow?.map((k: any) => k.fid),
          invalid_reason: invalidReasonForm.getFieldValue('invalid_reason'),
        });

        if (res?.code === 0 && !res?.data?.results?.length) {
          XlbMessage.success(`已作废${res?.data?.count}张单据！`);
        } else if (res?.code === 0 && res?.data?.results?.length) {
          XlbTipsModal({
            isCancel: false,
            isConfirm: true,
            tips: `已作废${res?.data?.count}张单据，以下单据不允许作废，系统自动过滤！`,
            tipsList: res?.data?.results,
          });
        }
      },
    });
  };
  const handleExport = async (ids: any, e: any, type: string) => {
    const data = prevPost();
    let res: any;
    if (type === 'detail') {
      res = await detailbatchexport({ fids: ids });
    } else {
      res = await exportData(data);
    }
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出成功');
    }
  };
  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        tableColumn={tableColumn?.filter((v: any) => {
          if (v.code === 'org_id') {
            return enable_organization;
          }
          if (v.code === 'cargo_owner_id') {
            return enable_cargo_owner;
          }
          return true;
        }).map((v: any) => {
          if (v.code === 'fid') {
            return {
              ...v,
              render(text: any, record: any, index: any) {
                return (
                  <div
                    className="link"
                    onClick={() => {
                      setRecord({ fid: text });
                      pageModalRef.current?.setOpen(true);
                    }}
                  >
                    {text}
                  </div>
                );
              },
            };
          }
          // 金额加权限
          if (v.code === 'money') {
            return {
              ...v,
              render: (value: any) => {
                return hasAuth(['领用申请单/成本价', '查询']) &&
                  value !== '****'
                  ? Number(value || 0)?.toFixed(2)
                  : '****';
              },
            };
          }
          return v;
        })}
        url={'/erp/hxl.erp.requisitionorder.page'}
        prevPost={prevPost}
        footerDataSource={(data) => {
          return [
            {
              _index: '合计',
              item_count: Number(data?.item_count || 0)?.toFixed(3),
              money:
                hasAuth(['领用申请单/成本价', '查询']) && data?.money !== '****'
                  ? Number(data?.money || 0)?.toFixed(2)
                  : '****',
              quantity: Number(data?.quantity || 0)?.toFixed(3),
            },
          ];
        }}
      >
        <ToolBtn showColumnsSetting>
          {({ fetchData, loading, dataSource, selectRow, selectRowKeys }) => {
            return (
              <XlbButton.Group>
                {hasAuth(['领用申请单', '查询']) && (
                  <XlbButton
                    label="查询"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      fetchData();
                    }}
                    icon={<XlbIcon name={'sousuo'} />}
                  />
                )}
                {hasAuth(['领用申请单', '编辑']) && (
                  <XlbButton
                    label="新增"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      setRecord({ fid: 1 });
                      pageModalRef.current?.setOpen(true);
                    }}
                    icon={<XlbIcon name={'jia'} />}
                  />
                )}
                {hasAuth(['领用申请单', '删除']) && (
                  <XlbButton
                    label="删除"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    onClick={() => {
                      handleBatchDelete(selectRowKeys, selectRow);
                    }}
                    icon={<XlbIcon name={'shanchu'} />}
                  />
                )}
                {hasAuth(['领用申请单', '导出']) && (
                  <XlbDropdownButton
                    label="导出"
                    disabled={loading}
                    dropList={[
                      {
                        label: '导出',
                        disabled: !dataSource?.length,
                      },
                      {
                        label: '导出明细',
                        disabled: !selectRow?.length,
                      },
                    ]}
                    dropdownItemClick={(value: number, item, e) => {
                      switch (item?.label) {
                        case '导出':
                          handleExport(selectRowKeys, e, 'all');
                          break;
                        case '导出明细':
                          handleExport(selectRowKeys, e, 'detail');
                          break;
                      }
                    }}
                  />
                )}
                {hasAuth(['领用申请单', '编辑']) && (
                  <XlbButton
                    label="复制"
                    type="primary"
                    disabled={loading || selectRow?.length != 1}
                    onClick={() => {
                      handleCopy(selectRow);
                    }}
                    icon={<XlbIcon name={'fuzhi'} />}
                  />
                )}
                {hasAuth(['领用申请单', '审核']) && (
                  <XlbButton
                    label="批量审核"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    onClick={() => {
                      handleAudit(selectRowKeys, selectRow);
                    }}
                    icon={<XlbIcon name={'shenhe'} />}
                  />
                )}
                {hasAuth(['领用申请单', '处理']) && (
                  <XlbDropdownButton
                    label="批量处理"
                    disabled={loading || !selectRow?.length}
                    dropList={[
                      {
                        label: '处理通过',
                        disabled: loading || !selectRow?.length,
                      },
                      {
                        label: '处理拒绝',
                        disabled: loading || !selectRow?.length,
                      },
                    ]}
                    dropdownItemClick={(value: number, item, e) => {
                      switch (item?.label) {
                        case '处理通过':
                          handleHandle(selectRowKeys, true);
                          break;
                        case '处理拒绝':
                          handleHandle(selectRowKeys, false);
                          break;
                      }
                    }}
                  />
                )}
                {hasAuth(['领用申请单', '批复']) && (
                  <XlbDropdownButton
                    label="批量批复"
                    disabled={loading || !selectRow?.length}
                    dropList={[
                      {
                        label: '批复通过',
                        disabled: loading || !selectRow?.length,
                      },
                      {
                        label: '批复拒绝',
                        disabled: loading || !selectRow?.length,
                      },
                    ]}
                    dropdownItemClick={(value: number, item, e) => {
                      switch (item?.label) {
                        case '批复通过':
                          handleApprove(selectRowKeys, true);
                          break;
                        case '批复拒绝':
                          handleApprove(selectRowKeys, false);
                          break;
                      }
                    }}
                  />
                )}
                {hasAuth(['领用申请单', '作废']) && (
                  <XlbButton
                    label="作废"
                    type="primary"
                    disabled={loading || !selectRow?.length}
                    icon={<XlbIcon name={'shenqingtuihuo'} />}
                    onClick={() => {
                      handleSureCancel(selectRow);
                    }}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={formList?.filter((v: any) => {
              if (v.name === 'org_ids') {
                return enable_organization;
              }
              if (v.name === 'cargo_owner_ids') {
                return enable_cargo_owner;
              }
              return true;
            })}
            form={form}
            isHideDate={true}
            initialValues={{
              time_type: 'create_date',
              compactDatePicker: [
                dayjs().format('YYYY-MM-DD'),
                dayjs().format('YYYY-MM-DD'),
              ],
            }}
          />
        </SearchForm>
        <Table key={'fid'} primaryKey={'fid'} selectMode="multiple"></Table>
      </XlbPageContainer>
    </>
  );
};

export default Index;
