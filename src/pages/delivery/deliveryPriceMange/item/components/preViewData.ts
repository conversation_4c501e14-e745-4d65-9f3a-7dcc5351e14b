import { XlbTableColumnProps } from "@xlb/components";

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '商品代码',
    code: 'item_code',
    width: 96,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 124,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '零售单位',
    code: 'basic_unit',
    features: { sortable: true },
  },
  {
    name: '标准售价',
    code: 'store_retail_price',
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '售价2',
    code: 'store_retail_price_s',
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '售价3',
    code: 'store_retail_price_t',
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '售价4',
    code: 'store_retail_price_f',
    features: { sortable: true },
    align: 'right',
  },
];
export const tableColumn1: XlbTableColumnProps<any>[] = [
  {
    name: '商品代码',
    code: 'item_code',
    width: 96,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 124,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '配送单位',
    code: 'delivery_unit',
    features: { sortable: true },
  },
  {
    name: '现配送价',
    code: 'price',
    features: { sortable: true },
    align: 'right',
  },
];

// 供应商公告类型
export const supplierMessageTypes = {
  INFORM: '通知类',
  POLICY: '执行类',
  EXECUTE: '政策类',
};
export const businessDeptListType = {
  STORE: '门店',
  SUPPLY_CENTER: '商品中心',
  STOCK_CENTER: '仓储中心',
  LOGISTICS_CENTER: '物流中心',
  FINANCE_CENTER: '财务中心',
  ADMIN_CENTER: '行政中心',
  EXPAND_DEPT: '拓展中心',
  INFORMATION_DEPT: '信息中心',
  OPERATE_CENTER: '运营中心',
  MARKET_CENTER: '市场中心',
  REGION: '大区',
  PROJECT_CENTER: '工程中心',
  BRAND_CENTER: '品牌中心',
};
