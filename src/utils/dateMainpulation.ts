import dayjs from 'dayjs'
/**
 * 按指定规则返回日期字符串:
 * timeFlag: 是否隐藏时间,默认false
 * timeFormat: 时间格式 默认时分秒
 * currentYearFlag: 是否显示当前年份,默认false
 */
function dateManipulation(
  date: string,
  timeFlag: boolean = false,
  currentYearFlag: boolean = false,
  timeFormat: string = 'HH:mm:ss'
) {
  let returnDate: string = ''
  if (typeof date === 'string') {
    const value = date ? date.split(' ') : []
    if (!value.length) return ''
    const date_value = value[0]
    const time_value = value[1] ? (timeFormat === 'HH:mm:ss' ? value[1] : value[1].slice(0, 5)) : ''
    if (!currentYearFlag) {
      const nowYear: string = dayjs().format('YYYY-MM-DD').split('-')[0]
      const dataYear: string = date_value.split('-')[0]
      if (nowYear == dataYear) {
        returnDate = date_value.split('-')[1] + '-' + date_value.split('-')[2]
        if (!timeFlag) {
          returnDate = returnDate + ' ' + time_value
        }
      } else {
        returnDate = date_value
        if (!timeFlag) {
          returnDate = date_value + ' ' + time_value
        }
      }
    } else {
      if (!timeFlag) {
        returnDate = date_value + ' ' + time_value
      } else {
        returnDate = date_value
      }
    }
  } else {
    return date
  }

  return returnDate
}

export default dateManipulation
