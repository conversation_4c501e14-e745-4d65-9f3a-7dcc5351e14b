import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import { formList, tableColumn } from './data';
import modifyRecord from './modifyRecord';
import {
  batchDeleteStoreItemSupplier,
  updateStoreItemSupplier,
} from './server';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const OrderParamsConfigIndex = () => {
  const [form] = XlbBasicForm.useForm();
  const { enable_organization } = useBaseParams((state) => state);
  let refresh = () => {};

  // 输入框失去焦点事件
  const inputBlur = async (e: any, record: any) => {
    const validateField = (value: any) =>
      value === '' ||
      value === null ||
      value === undefined ||
      (/^(0|[1-9][0-9]*)$/.test(value) && value <= 999999999);

    const fields = [
      { key: 'ad_purchase_period', name: '交货周期' },
      { key: 'order_freq', name: '订货频次' },
      { key: 'safe_days', name: '安全库存天数' },
    ];

    const invalidField = fields.find((f) => !validateField(record?.[f.key]));
    if (invalidField) {
      await XlbTipsModal({
        title: '提示',
        tips: `${invalidField.name}请输入≥0且<999999999的整数或留空`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    const data = {
      ad_purchase_period: record?.ad_purchase_period, //交货周期
      order_freq: record?.order_freq, //订货频次（天）
      safe_days: record?.safe_days, //导入安全库存天数（天）
      item_id: record?.item_id, // *********商品id
      store_id: record?.store_id, //*********门店ID
      supplier_id: record?.supplier_id, //******供应商ID
    };
    const res = await updateStoreItemSupplier(data);
    if (res.code === 0) {
      message.success('更新成功');
      refresh();
    }
  };

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'order_freq': //订货频次
      case 'ad_purchase_period': //交货周期
      case 'safe_days': //导入安全天数
        item.render = (value: any, record: any, index: number) => {
          if (record._click && hasAuth(['订货参数配置', '编辑'])) {
            return (
              <XlbInput
                key={record[item.code]}
                className="full-box"
                defaultValue={record[item.code]}
                onFocus={(e) => e.target.select()}
                onChange={(e) => {
                  record[item?.code] = e.target.value ? e.target.value : '';
                }}
                onBlur={(e) => inputBlur(e, record)}
                style={{ textAlign: 'right' }}
                onClick={(e) => {
                  e.stopPropagation();
                }}
              />
            );
          } else {
            return <div className="info">{value}</div>;
          }
        };
        break;
    }
    return item;
  };
  // 提交参数
  const prevPost = () => {
    const values = form.getFieldsValue(true);
    return {
      ...values,
      filter_delivery_center: true, //后端让加的
      item_brand_id: values.item_brand_id?.[0] || null,
      main: values?.checkValue?.includes('main') ? true : undefined,
    };
  };

  //   导入
  const importItem = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storeitemsupplier.extend.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storeitemsupplier.extend.template.download`,
      params: {},
    });
  };

  const getIds = (selectRow: any[]) =>
    selectRow.map((item) => ({
      item_id: item.item_id,
      store_id: item.store_id,
      supplier_id: item.supplier_id,
    }));

  //   删除
  const deleteRow = async (selectRow: string[], fetchData: any) => {
    await XlbTipsModal({
      tips: `已选择${selectRow.length}条记录,是否确认删除!`,
      isConfirm: true,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const params = { ids: getIds(selectRow) };
        const res = await batchDeleteStoreItemSupplier(params);
        if (res.code === 0) {
          message.success('删除成功');
          fetchData();
          return res.code === 0;
        }
        message.error('删除失败');
        return res.code === 0;
      },
    });
  };

  //导出
  const exportData = async (requestForm: any, setLoading: any, e: any) => {
    setLoading(true);
    const res = await XlbFetch.post(
      '/erp/hxl.erp.storeitemsupplier.extend.export',
      { ...requestForm },
    );
    if (res?.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setLoading(false);
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.storeitemsupplier.page'}
      tableColumn={tableColumn?.map((v) => tableRender(v))}
      prevPost={prevPost}
    >
      <SearchForm>
        <XlbForm
          formList={formList(enable_organization)}
          form={form}
          isHideDate
        />
      </SearchForm>
      <ToolBtn>
        {({
          dataSource,
          fetchData,
          selectRowKeys,
          selectRow,
          loading,
          setLoading,
          requestForm,
        }: any) => {
          refresh = () => {
            if (!form.getFieldValue('store_ids')?.length) return;
            fetchData();
          };
          return (
            <XlbButton.Group>
              <XlbButton
                key="query"
                label="查询"
                type="primary"
                disabled={loading}
                onClick={() => {
                  if (!form.getFieldValue('store_ids')?.length) {
                    message.error('请选择门店');
                    return;
                  }
                  fetchData();
                }}
                icon={<span className="iconfont icon-sousuo" />}
              />
              {/* 暂时隐藏 */}
              {/* {hasAuth(['订货参数配置', '删除']) ? (
                <XlbButton
                  key="delete"
                  label="删除"
                  loading={loading}
                  type="primary"
                  disabled={!selectRowKeys?.length}
                  onClick={() => deleteRow(selectRow, fetchData)}
                  icon={
                    <XlbIcon name="shanchu" color="currentColor" size={16} />
                  }
                />
              ) : null} */}
              {hasAuth(['订货参数配置', '导入']) && (
                <XlbButton
                  type="primary"
                  loading={loading}
                  icon={<XlbIcon name="daoru" />}
                  onClick={importItem}
                >
                  导入
                </XlbButton>
              )}

              {hasAuth(['订货参数配置', '导出']) ? (
                <XlbButton
                  key="exports"
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={!dataSource?.length}
                  onClick={(e: any) => exportData(requestForm, setLoading, e)}
                  icon={
                    <XlbIcon name="daochu" color="currentColor" size={16} />
                  }
                />
              ) : null}

              {hasAuth(['订货参数配置', '查询']) && (
                <XlbButton
                  disabled={selectRowKeys?.length !== 1}
                  label="修改记录"
                  type="primary"
                  icon={<XlbIcon name="shenqing" />}
                  onClick={() => {
                    const _id = {
                      item_id: selectRow?.[0]?.item_id,
                      store_id: selectRow?.[0]?.store_id,
                      supplier_id: selectRow?.[0]?.supplier_id,
                      page_size: 999999,
                      page_number: 0,
                    };
                    NiceModal.show(modifyRecord, { params: _id });
                  }}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table primaryKey="fid" selectMode="multiple" />
    </XlbPageContainer>
  );
};

export default OrderParamsConfigIndex;
