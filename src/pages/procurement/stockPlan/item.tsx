import { hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbTableColumnProps,
} from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { message } from 'antd';
import { useRef, type FC } from 'react';
import { searchFormList } from './data';
import Api from './server';

const StockPlanProFormItem: FC<{ title: string }> = (props: any) => {
  const { record, onBack } = props;
  const [form] = XlbBasicForm.useForm();
  const { Table, ToolBtn, SearchForm } = XlbPageContainer;
  const pageRef = useRef<XlbPageContainerRef>(null);

  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 80,
      lock: true,
      align: 'center',
    },
    {
      name: '所属门店',
      code: 'store_name',
      width: 140,
      features: { sortable: true },
    },
    {
      name: '商品代码',
      code: 'item_code',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '商品条码',
      code: 'item_barcode',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '采购规格',
      code: 'item_spec',
      width: 120,
      features: { sortable: true },
    },
    {
      name: '单位',
      code: 'purchase_unit',
      width: 80,
      features: { sortable: true },
    },
    {
      name: '商品品类',
      code: 'item_category_name',
      width: 100,
      features: { sortable: true },
    },
    {
      name: '品类数量',
      code: 'item_category_quantity',
      width: 120,
      features: { sortable: true },
      align: 'right',
    },
    {
      name: '品类金额',
      code: 'item_category_money',
      width: 120,
      features: { sortable: true },
      align: 'right',
    },
    {
      name: '备货数量',
      code: 'request_quantity',
      width: 140,
      features: { sortable: true },
      align: 'right',
    },
    {
      name: '实际配送数量',
      code: 'actual_request_quantity',
      width: 120,
      features: { sortable: true },
      align: 'right',
    },
    {
      name: '最近更新时间',
      code: 'update_time',
      width: 160,
      features: { sortable: true, format: 'TIME' },
    },
  ];
  const prevPost = () => {
    const formData = form.getFieldsValue(true);
    return {
      ...formData,
      stock_plan_id: record?.id,
    };
  };
  const queryData = () => {
    pageRef.current?.fetchData();
  };
  const exportItem = async (setIsLoading: any, requestForm: any, e) => {
    setIsLoading(true);
    let res = null;
    res = await Api.itemExportDetail({
      ...requestForm,
      stock_plan_id: record?.id,
    });
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };

  return (
    <div style={{ height: 'calc(100vh - 120px)' }}>
      <XlbPageContainer // 查询
        ref={pageRef}
        url={'/erp/hxl.erp.stockplandetail.page'}
        tableColumn={tableColumn}
        immediatePost
        prevPost={prevPost}
      >
        <SearchForm>
          <XlbForm form={form} formList={searchFormList} isHideDate />
        </SearchForm>
        <ToolBtn>
          {({ dataSource, setLoading, requestForm }) => {
            return (
              <XlbButton.Group>
                {hasAuth(['备货计划', '查询']) && (
                  <XlbButton
                    type="primary"
                    onClick={() => queryData()}
                    icon={<XlbIcon name="sousuo" />}
                  >
                    查询
                  </XlbButton>
                )}
                {hasAuth(['备货计划', '导出']) && (
                  <XlbButton
                    type="primary"
                    label="导出"
                    disabled={!dataSource?.length}
                    onClick={(e) => exportItem(setLoading, requestForm, e)}
                    icon={<XlbIcon name="daochu" />}
                  />
                )}

                <XlbButton
                  type="primary"
                  label="返回"
                  onClick={(e) => onBack()}
                  icon={<XlbIcon name="fanhui" />}
                />
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <Table />
      </XlbPageContainer>
    </div>
  );
};

export default StockPlanProFormItem;
