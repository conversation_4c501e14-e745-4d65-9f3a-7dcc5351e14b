import {
  SearchFormType,
  XlbSelect,
  XlbTableColumnProps,
} from '@xlb/components';
import { message } from 'antd';
import { updateGoods } from './server';

export enum MustSellGoodState {
  Pass = 'PASS', // 已审核
  Audit = 'AUDIT', // 待审核
}
export const STATE_LIST = [
  {
    label: '待审核',
    value: MustSellGoodState.Audit,
  },
  {
    label: '已审核',
    value: MustSellGoodState.Pass,
  },
];

export const FORM_TYPE_LIST = [
  {
    label: '地采',
    value: 1,
  },
  {
    label: '集采',
    value: 0,
  },
];

export const FORM_LIST: SearchFormType[] = [
  {
    type: 'compactDatePicker',
    label: '创建日期',
    name: 'create_date',
    allowClear: false,
  },
  {
    label: '所属组织',
    name: 'org_id',
    type: 'select',
    check: true,
    // @ts-ignore
    onChange: (_e: any, form: any) => {
      form?.setFieldsValue({ store_ids: [] });
    },
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      responseTrans(response: any) {
        return (
          response
            ?.filter((item: any) => item.level === 2)
            ?.map((item: any) => ({
              ...item,
              label: item.name,
              value: item.id,
            })) || []
        );
      },
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dependencies: ['org_id'],
    dialogParams: (data) => ({
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: { org_ids: [data.org_id], status: true },
    }),
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '状态',
    name: 'state',
    type: 'select',
    clear: true,
    check: true,
    options: STATE_LIST,
  },
  {
    label: '类型',
    name: 'purchase_type',
    type: 'select',
    clear: true,
    check: true,
    options: FORM_TYPE_LIST,
  },
  {
    label: '商品分类',
    name: 'category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      params: {
        limit_level: 1,
      },
      width: 360, // 模态框宽度
    },
  },
];

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '单号',
    code: 'fid',
    width: 240,
    features: { sortable: true },
  },
  {
    name: '月份',
    code: 'month',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '组织',
    code: 'org_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '商品品类',
    code: 'category_name',
    features: { sortable: true },
    width: 140,
  },
  {
    name: '类型',
    code: 'purchase_type_name',
    features: { sortable: true },
    width: 80,
  },
  {
    name: '状态',
    code: 'state',
    features: { sortable: true },
    width: 80,
    render: (value: any) => (
      <span>{STATE_LIST.find((item) => item.value === value)?.label}</span>
    ),
  },
  {
    name: '审核人',
    code: 'confirm_by',
    width: 160,
  },
  {
    name: '审核时间',
    code: 'confirm_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
  },
];

// item
export const IS_MUSTSELL_STATE = [
  {
    label: '是',
    value: 2,
  },
  {
    label: '否',
    value: 1,
  },
];
export const DETAIL_FORM_LIST: SearchFormType[] = [
  {
    label: '商品',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
    },
  },
];

export const detailTableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    features: { sortable: true },
    width: 200,
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '采购类型',
    code: 'purchase_type_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品分类',
    code: 'item_category_name',
    width: 160,
  },
  {
    name: '必卖品',
    code: 'is_mustsell',
    width: 140,
    render: (value: any, record: any, method: any) => {
      if (record?._click) {
        return (
          <XlbSelect
            width={132}
            defaultValue={value}
            allowClear={false}
            options={IS_MUSTSELL_STATE}
            onChange={async (e: any) => {
              const res = await updateGoods({
                update_list: [
                  {
                    id: record.id,
                    is_mustsell: e,
                  },
                ],
              });
              if (res?.code === 0) {
                message.success('更新成功');
                method?.fetchData?.();
              }
            }}
          />
        );
      }
      return record?.is_mustsell_str;
    },
  },
];
