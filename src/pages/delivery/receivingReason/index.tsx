import { hasAuth } from '@/utils';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { useRef } from 'react';
import { tableList } from './data';
import { read, save, update } from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Index = () => {
  const pageRef = useRef<any>(null);
  const [form] = XlbBasicForm.useForm<any>();
  const [formEdit] = XlbBasicForm.useForm<any>();
  const prevPost = () => {
    const { ...rest } = form.getFieldsValue(true);
    return {
      ...rest,
    };
  };
  const openItem = async (record: any, isAdd: boolean) => {
    if (!isAdd) {
      pageRef?.current?.setLoading(true);
      const res = await read({ id: record.id });
      pageRef?.current?.setLoading(false);
      if (res.code === 0) {
        formEdit.setFieldsValue({
          ...res?.data,
          flag: res?.data?.flag ? 1 : 0,
        });
      }
    } else {
      formEdit.setFieldsValue({
        flag: 1,
      });
    }
    XlbTipsModal({
      title: isAdd ? '新增领用原因' : '编辑领用原因',
      isCancel: true,
      bordered: true,
      tips: (
        <div
          style={{
            padding: 20,
          }}
        >
          <XlbForm
            form={formEdit}
            isHideDate={true}
            formList={[
              {
                label: '领用原因',
                name: 'name',
                type: 'input',
                width: 256,
                placeholder: '请输入',
                rules: [{ required: true, message: '请输入领用原因' }],
              },
              {
                label: '状态',
                name: 'flag',
                type: 'radio',
                rules: [{ required: true, message: '请选择状态' }],
                options: [
                  {
                    label: '启用',
                    value: 1,
                  },
                  {
                    label: '停用',
                    value: 0,
                  },
                ],
              },
            ]}
          />
        </div>
      ),
      onOkBeforeFunction: async () => {
        try {
          await formEdit.validateFields();
        } catch (err: any) {
          return false;
        }
        const params = { ...formEdit.getFieldsValue(true) };
        pageRef?.current?.setLoading(true);
        const res = isAdd ? await save(params) : await update(params);
        pageRef?.current?.setLoading(false);
        if (res.code === 0) {
          XlbMessage.success('操作成功');
          formEdit.resetFields();
          pageRef?.current?.fetchData();
          return true;
        }
      },
    });
  };

  return (
    <>
      <XlbPageContainer
        url={'/erp/hxl.erp.requisitionreason.find'}
        tableColumn={tableList.map((v) => {
          if (v.code === 'name') {
            v.render = (text: any, record: any, index: any) => {
              return (
                <span className="link" onClick={() => openItem(record, false)}>
                  {text}
                </span>
              );
            };
            return v;
          }
          if (v.code === 'flag') {
            v.render = (value: any) => {
              return (
                <span className={`${value ? 'success' : ''}`}>
                  {value ? '启用' : '停用'}
                </span>
              );
            };
          }
          return v;
        })}
        prevPost={prevPost}
        immediatePost={true}
      >
        <ToolBtn showColumnsSetting>
          {(current) => {
            pageRef.current = current;
            return (
              <XlbButton.Group>
                {hasAuth(['领用原因', '查询']) && (
                  <XlbButton
                    key="query"
                    label="查询"
                    type="primary"
                    disabled={current.loading}
                    onClick={() => {
                      current.fetchData();
                    }}
                    icon={<XlbIcon name="sousuo" />}
                  />
                )}
                {hasAuth(['领用原因', '编辑']) ? (
                  <XlbButton
                    label="新增"
                    type="primary"
                    disabled={current.loading}
                    onClick={() => openItem(false, true)}
                    icon={<XlbIcon name="jia" />}
                  />
                ) : null}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={[
              {
                label: '关键字',
                name: 'keyword',
                type: 'input',
              },
            ]}
            form={form}
            isHideDate={true}
          />
        </SearchForm>
        <Table key="id" selectMode="single" primaryKey="id" />
      </XlbPageContainer>
    </>
  );
};
export default Index;
