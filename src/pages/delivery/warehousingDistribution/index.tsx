import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import {
  XlbButton,
  XlbIcon,
  XlbMessage,
  XlbProPageContainer,
} from '@xlb/components';
import type { SearchListItem } from '@xlb/components/dist/lowcodes/XlbProForm/components/ProFormList';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import type { FormInstance } from 'antd';
import { useEffect, useRef, useState } from 'react';
import BatchChange from './component/batchChange/batchChange';
import History from './component/history';
import { formList, tableList } from './data';

const StoreProperties = () => {
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [batchVisible, setBatchVisible] = useState<boolean>(false); //批量设置弹框
  const [chooseList, setChooseList] = useState<any[]>([]);
  //复制弹框
  const [historyVisible, setHistoryVisible] = useState<boolean>(false); //修改记录
  const [formlist, setFormList] = useState<SearchListItem[]>(formList);
  const formInstens = useRef<FormInstance | null>(null);
  const requestFormInstens = useRef<any>(null);
  const refreshRef = useRef<(page?: number) => void>(() => {});
  const { enable_organization } = useBaseParams((state) => state);

  // 表格编辑处理

  const getOrgList = () => {
    const org = formlist.find((i: any) => i.name === 'org_ids') as any;
    org!.hidden = !enable_organization;
    org!.onChange = (e: string[], form: any, _: any) => {
      if (e?.length > 0) {
        form.setFieldsValue({
          store_ids: null,
        });
      }
    };
    setFormList([...formlist]);
  };

  useEffect(getOrgList, [enable_organization]);

  useEffect(() => {
    formInstens.current?.setFieldsValue({
      product_actual_attribute: false,
      show_diff_stop_request: false,
    });
  }, []);

  //导出
  const exportItem = async (requestForm: any, e: any) => {
    setisLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.storeitemdistribution.attribute.export',
      {
        ...requestForm,
      },
    );
    setisLoading(false);
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      XlbMessage.success('导出受理成功,请到下载中心查看');
    }
  };

  return (
    <>
      <BatchChange
        visible={batchVisible}
        handleCancel={() => setBatchVisible(false)}
        getData={refreshRef.current}
        currentData={chooseList}
        enableOrganization={enable_organization}
      />
      <History
        visible={historyVisible}
        id={chooseList}
        onCancel={() => setHistoryVisible(false)}
      />

      <XlbProPageContainer
        searchFieldProps={{
          formList: formlist,
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.storeitemdistribution.attribute.page',
          tableColumn: () => {
            return tableList.map((t) => {
              if (t.code === 'org_name') {
                t.hidden = !enable_organization;
              }
              return t;
            });
          },
          selectMode: 'single',
          immediatePost: false,
          // TODO: 列定制影响表格列显示顺序，暂时去掉
          showColumnsSetting: true,
          changeColumnAndResetDataSource: true,
        }}
        extra={(context) => {
          const { fetchData, dataSource, selectRow, form, requestForm } =
            context;
          refreshRef.current = fetchData;
          formInstens.current = form;
          requestFormInstens.current = requestForm;
          setChooseList(selectRow || []);
          return (
            <XlbButton.Group>
              {hasAuth(['仓配单位维护', '导出']) ? (
                <XlbButton
                  disabled={!dataSource?.length}
                  type="primary"
                  label="导出"
                  loading={isLoading}
                  onClick={(e) => {
                    exportItem(requestForm, e);
                  }}
                  icon={<XlbIcon size={16} name="daochu" />}
                />
              ) : null}
              {hasAuth(['仓配单位维护', '编辑']) ? (
                <XlbButton
                  type="primary"
                  label="批量设置"
                  onClick={() => {
                    setBatchVisible(true);
                  }}
                  icon={<XlbIcon size={16} name="shezhi" />}
                />
              ) : null}
              <XlbButton
                type="primary"
                label="修改记录"
                disabled={!selectRow?.length}
                onClick={() => setHistoryVisible(true)}
                icon={<XlbIcon size={16} name="shenqing" />}
              />
            </XlbButton.Group>
          );
        }}
      ></XlbProPageContainer>
    </>
  );
};

export default StoreProperties;