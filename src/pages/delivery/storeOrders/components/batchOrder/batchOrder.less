.diyCheckBox {
  width: 15px;
  height: 15px;
  margin-right: 5px;
  vertical-align: text-top;
}

.checkText {
  color: black;
  font-weight: bolder;
}

.formWrapCus {
  :global {
    .ant-row {
      align-items: center;
    }
    .xlb-pro-form {
      &.ant-form-inline {
        > .ant-form-item {
          margin-bottom: 4px !important;
        }
      }
    }
  }
}

.myDropDown {
  overflow: inherit;
  &:global(.xlb_select_dropdown) {
    width: unset !important;
  }
  :global {
    .rc-virtual-list {
      width: 240px !important;
      background-color: #fff;
    }
  }
}
