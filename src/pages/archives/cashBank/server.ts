import { XlbFetch as ErpRequest } from "@xlb/utils"
import { CashBankFindReqDTO, CashBankSaveReqDTO, CashBankUpdateReqDTO } from './type'

export default {
    //获取数据 
    getData: async (data: CashBankFindReqDTO) => {
        return await ErpRequest.post('/erp/hxl.erp.cashbank.find', data)
    },

    //新增区域
    add: async (data: CashBankSaveReqDTO) => {
        return await ErpRequest.post('/erp/hxl.erp.cashbank.save', data)
    },
    //更新修改区域
    update: async (data: CashBankUpdateReqDTO) => {
        return await ErpRequest.post('/erp/hxl.erp.cashbank.update', data)
    }
}