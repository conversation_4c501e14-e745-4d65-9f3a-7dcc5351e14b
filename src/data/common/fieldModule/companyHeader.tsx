export const CompanyHeaderKeyMap = {
  erpCompanyTitle: 'erpCompanyTitle',
  erpCompanyTaxNum: 'erpCompanyTaxNum',
  erpLegalPerson: 'erpLegalPerson',
  erpClerk: 'erpClerk',
  erpCompanyIsDefault: 'erpCompanyIsDefault'
}

export const companyHeaderConfig: any[] = [
  // 公司抬头管理
  {
    tag: 'ERP',
    label: '公司抬头',
    id: CompanyHeaderKeyMap.erpCompanyTitle,
    name: 'title',
    componentType: 'input',
    fieldProps: {
      width: 180
    }
  },
  {
    tag: 'ERP',
    label: '公司税号',
    id: CompanyHeaderKeyMap.erpCompanyTaxNum,
    name: 'tax_num',
    componentType: 'input',
    fieldProps: {
      width: 180
    }
  },
  {
    tag: 'ERP',
    label: '法人姓名',
    id: CompanyHeaderKeyMap.erpLegalPerson,
    name: 'legal_person',
    componentType: 'input',
    fieldProps: {
      width: 180
    }
  },
  {
    tag: 'ERP',
    label: '开票员',
    id: CompanyHeaderKeyMap.erpClerk,
    name: 'clerk',
    componentType: 'input',
    fieldProps: {
      width: 180
    }
  },
  // 公司抬头管理 是否默认
  {
    tag: 'ERP',
    label: '设为默认',
    id: CompanyHeaderKeyMap.erpCompanyIsDefault,
    componentType: 'checkbox',
    fieldProps: {
      options: [{ label: '设为默认', value: 'is_default' }]
    },
    formItemProps: {
      label: ' ',
      colon: false
    },
    group: false
  }
]
