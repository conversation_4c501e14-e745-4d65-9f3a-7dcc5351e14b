import { XlbFetch as ErpRequest } from '@xlb/utils';
import { create, StateCreator } from 'zustand';
import { persist } from 'zustand/middleware';

export type useBase = {
  /**是否启用组织机构 */
  enable_organization?: boolean;
  /** 是否开启多货主 */
  enable_cargo_owner?: boolean
  setEnableOrganization: (enable_organization: boolean) => void;
  setEnableCargoOwner: (enable_cargo_owner: boolean) => void
  getEnableOrganization: () => Promise<void>;
};

type BoundState = useBase;

export type BoundStateCreator<SliceState> = StateCreator<
  BoundState,
  [],
  [],
  SliceState
>;

export const createBaseParams = persist(
  (set) => ({
    enable_organization: undefined,
    enable_cargo_owner: undefined,
    setEnableOrganization: (enable_organization: boolean) =>
      set({ enable_organization }),
    setEnableCargoOwner: (enable_cargo_owner: boolean) => set({ enable_cargo_owner }),
    getEnableOrganization: async () => {
      const res = await ErpRequest.post('/erp-mdm/hxl.erp.baseparam.read',{});
      if (res?.code == 0) {
        set({ enable_organization: res.data.enable_organization });
        set({ enable_cargo_owner: res?.data?.enable_cargo_owner })
      }
    },
  }),
  {
    name: 'baseParams',
    partialize: (state: useBase) => ({
      enable_organization: state.enable_organization,
      enable_cargo_owner: state.enable_cargo_owner,
    }),
  },
);

export const useBaseParams = create<BoundState>((...arg) =>
  createBaseParams(...arg),
);