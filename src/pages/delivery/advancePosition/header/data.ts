import { SearchFormType } from '@xlb/components-stage'
import { ArtColumn } from 'ali-react-table'
import { columnWidthEnum } from '@/data/common/constant'

//单据状态
export const Options1 = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info'
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning'
  },
  {
    label: '处理通过',
    value: 'APPROVE',
    type: 'success'
  },
  {
    label: '处理拒绝',
    value: 'REFUSE',
    type: 'danger'
  }
]
//收款状态
export const PayStateList = [
  {
    label: '未收款',
    value: 'INIT',
    type: 'info'
  },
  {
    label: '已收款',
    value: 'FINISH',
    type: 'success'
  }
]
//收款状态
export const PayStateListPersion = [
  {
    label: '供应商',
    value: 'SUPPLIER'
  }
]
export const PayStateListType = [
  {
    label: '对账收款',
    value: 'ACCOUNTAPPLY'
  },
  {
    label: '预付收款',
    value: 'ADVANCEPAY'
  }
]
export const stepDateOption: any[] = [
  {
    label: '按月度',
    value: 'MONTH'
  },
  {
    label: '按季度',
    value: 'QUARTER'
  },
  {
    label: '按年度',
    value: 'YEAR'
  }
]
export const activedOption: any[] = [
  {
    label: '启用',
    value: 1
  },
  {
    label: '停用',
    value: 0
  }
]
export const sharedOption: any[] = [
  {
    label: '是',
    value: true
  },
  {
    label: '否',
    value: false
  }
]

export const payType: any[] = [
  {
    label: '默认日期',
    value: 0
  },
  {
    label: '固定日期',
    value: 1
  },
  {
    label: '固定周期',
    value: 2
  }
]

export const settleModeOption: any[] = [
  {
    value: 0,
    label: '票到付款'
  },
  {
    value: 1,
    label: '先款后票'
  }
]
// 时间类型
export const Options3 = [
  {
    label: '制单时间',
    value: 'create_date'
  },
  {
    label: '审核时间',
    value: 'audit_date'
  },
  {
    label: '处理时间',
    value: 'handle_date'
  },
  {
    label: '收款时间',
    value: 'collect_date'
  },
  {
    label: '结算日期',
    value: 'settlement_date'
  }
]
export const formList: SearchFormType[] = [
  {
    width: 372,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false
  },
  {
    label: '单据状态',
    name: 'states',
    type: 'select',
    clear: true,
    check: true,
    multiple: true,
    options: Options1
  },
  {
    label: '收款状态',
    name: 'collect_states',
    type: 'select',
    clear: true,
    check: true,
    multiple: true,
    options: PayStateList
  },
  {
    label: '收款对象',
    name: 'collect_target',
    type: 'select',
    options: PayStateListPersion
  },
  {
    label: '时间类型',
    name: 'time_type',
    type: 'select',
    clear: false,
    check: true,
    options: Options3
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true
      }
    }
  },
  {},
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true
      },
      fieldName: {
        id: 'id',
        name: 'store_name'
      }
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name'
    } as any
  },
  {
    label: '单据号',
    name: 'fid',
    type: 'input',
    clear: true,
    check: true
  },
  {
    label: '关联单号',
    name: 'related_order_fid',
    type: 'input',
    clear: true,
    check: true
  },
  {
    label: '制单人',
    name: 'create_by',
    type: 'input',
    check: true,
    clear: true
  },
  {
    label: '审核人',
    name: 'audit_by',
    type: 'input',
    check: true,
    clear: true
  },
  {
    label: '处理人',
    name: 'handle_by',
    type: 'input',
    check: true,
    clear: true
  },
  {
    label: '收款人',
    name: 'collect_by',
    type: 'input',
    check: true,
    clear: true
  },
  {
    label: '收款金额',
    name: 'money',
    type: 'input',
    check: true,
    clear: true
  },
  {
    label: '收款类型',
    name: 'collect_type',
    type: 'select',
    options: PayStateListType
  }
]

export const associationList: ArtColumn[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '劳务商代码',
    code: 'code',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '劳务商名称',
    code: 'name',
    width: 200,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '劳务商分类',
    code: 'category_name',
    width: 140,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '启用',
    code: 'actived',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '',
    code: ''
  }
]

// 批量修改
export const batch = [
  {
    label: '所属门店',
    value: 'store_name'
  },
  {
    label: '进项税率',
    value: 'tax_rate'
  },
  {
    label: '劳务商类别',
    value: 'category_id'
  },
  {
    label: '状态',
    value: 'actived'
  },
  {
    label: '结算方式',
    value: 'settlement_mode'
  },
  {
    label: '付款日期',
    value: 'pay_type'
  }
]
// 修改记录
export const historyArr = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true
  },
  {
    name: '修改时间',
    code: 'create_time',
    width: 220,
    align: 'center'
  },
  {
    name: '修改人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    align: 'center'
  },
  {
    name: '查看',
    code: 'look',
    align: 'center'
  },
  {
    name: ''
  }
]
