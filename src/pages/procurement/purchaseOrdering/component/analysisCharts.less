.chart-container {
  position: relative;
  height: 340px;
  .chart-tabs {
    position: absolute;
    inset: -6px 0 auto auto;
    display: flex;
    align-items: center;
    cursor: pointer;
    z-index: 1000;
    &__item {
      padding: 4px 12px;
      font-size: 14px;
      line-height: 20px;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e9e9e9;
      color: #1f2126;
      border-radius: 4px 0px 0px 4px;
      &:last-child {
        margin-left: -1px;
        border-radius: 0px 4px 4px 0px;
      }
      &.active {
        border-color: #1a6aff;
        color: #1a6aff;
        z-index: 1;
      }
    }
  }
  &__chart {
    width: calc(100% + 28px);
    margin-left: -16px;
    position: absolute;
  }
}

.tooltip-container {
  padding: 2px;
  .tooltip-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #1d2129;
  }
  .tooltip-item {
    margin-top: 4px;
    display: flex;
    align-items: center;
    &:first-child {
      margin-top: 6px;
    }
    &__icon {
      width: 18px;
      height: 2px;
      background: #41d985;
      position: relative;
      margin-right: 4px;
      &::after {
        position: absolute;
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 10px;
        inset: 0;
        margin: auto;
        background: #ffffff;
        border: 2px solid #41d985;
        box-sizing: border-box;
      }
      &.is-forecast {
        background: #ff9340;
        &::after {
          border-color: #ff9340;
        }
      }
      &.is-store {
        width: 8px;
        margin-left: 5px;
        margin-right: 9px;
        height: 8px;
        background: #1a6aff;
        &::after {
          display: none;
        }
      }
    }
    &__value {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      color: #1d2129;
    }
  }
}
