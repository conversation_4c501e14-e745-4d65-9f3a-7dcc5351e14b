import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils';
import { useBaseParams } from '@/hooks/useBaseParams';
import {XlbFetch as ErpRequest} from '@xlb/utils';
import { wujieBus } from '@/wujie/utils';
import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  type XlbTableColumnProps,
} from '@xlb/components';
import { message } from 'antd';
import dayjs from 'dayjs';
import { MouseEvent } from 'react';
import {
  deliveryQuantity,
  indexItem,
  returnRate,
  summaryType,
  tableColumnEnum,
  unitType,
} from './data';

const ReturnRateStatistics = () => {
  const { enable_cargo_owner } = useBaseParams((state) => state);
  const exportItem = async (
    formValues: any,
    e: MouseEvent<Element, MouseEvent>,
    columns: XlbTableColumnProps<any>[],
  ) => {
    const data = {
      ...formValues,
      export_fields: columns.map((item) => ({
        name: item.name,
        id: item.code,
      })),
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.delivery.returnorder.report.export',
      data,
    );
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      message.success('导出受理成功，请前往下载中心查看');
    }
  };

  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        // requiredMark: false,
        initialValues: {
          delivery_date: [
            dayjs()?.format('YYYY-MM-DD'),
            dayjs()?.format('YYYY-MM-DD'),
          ],
          apply_date: [
            dayjs()?.format('YYYY-MM-DD'),
            dayjs()?.format('YYYY-MM-DD'),
          ],
          state: 'APPROVE',
          category_level: 0,
          summary_types: ['STORE'],
          reasons: ['质量问题退货'],
          time_type: 2,
          unit_type: 'DELIVERY',
        },
        formList: [
          {
            id: 'dateCommon',
            label: '调入时间',
            name: 'delivery_date',
            format: 'YYYY-MM-DD',
            fieldProps: {
              allowClear: false,
            },
          },
          {
            id: 'dateCommon',
            label: '申请时间',
            name: 'apply_date',
            format: 'YYYY-MM-DD',
            fieldProps: {
              allowClear: false,
            },
          },
          {
            id: ErpFieldKeyMap.erpSummerType,
            label: '汇总条件',
            name: 'summary_types',
            fieldProps: {
              mode: 'multiple',
              options: summaryType,
              allowClear: false,
            },
            rules: [{ required: true, message: '请选择汇总条件' }],
          },
          {
            id: ErpFieldKeyMap.orgIdsWithThreeLevel,
            label: '调出组织',
          },
          {
            id: ErpFieldKeyMap.orgIdsWithThreeLevel,
            label: '调入组织',
            name: 'in_org_ids',
          },
          { id: ErpFieldKeyMap?.erpStoreIdsOrder,
            name: 'store_ids',
            label: '调出门店' },
          {
            id: ErpFieldKeyMap?.erpStoreIdsOrder,
            name: 'in_store_ids',
            label: '调入门店',
          },
          {
            id: ErpFieldKeyMap?.erpRateStatisticsItemCategory,
            label: '商品类别',
          },
          {
            id: ErpFieldKeyMap?.erpCategoryLevel,
            label: '类别等级',
            fieldProps: {
              allowClear: false,
            },
          },
          {
            id: ErpFieldKeyMap?.erpSupplierId,
            label: '供应商',
            name: 'supplier_id',
          },
          {
            id: ErpFieldKeyMap.erpExceptioncategoryParent,
            label: '异常类别',
            fieldProps: {
              allowClear: true,
            },
          },
          {
            id: ErpFieldKeyMap.erpExceptioncategory,
            label: '异常二级',
            fieldProps: {
              allowClear: true,
            },
          },
          {
            id: ErpFieldKeyMap?.erpReasonApplication,
            label: '申请原因',
            fieldProps: {
              mode: 'multiple',
              allowClear: true,
            },
            onChange: (e: string[], _: any, form: any) => {
              // 不包含质量问题退货
              if (!e.includes('质量问题退货')) {
                _.setFieldsValue({
                  problem_category_id: null,
                });
              }
            },
          },
          {
            id: ErpFieldKeyMap?.erpQualityProblemCategory,
            label: '质量问题分类',
          },
          // {
          //   id: 'timeType',
          //   label: '时间类型',
          //   name: 'time_type',
          //   fieldProps: {
          //     options: dateType,
          //     allowClear: false
          //   }
          // },
          // {
          //   id: ErpFieldKeyMap.erpBillTimeType,
          //   label: '审核状态',
          //   name: 'state',
          //   fieldProps: {
          //     options: auditorType,
          //     allowClear: false
          //   }
          // },
          {
            id: ErpFieldKeyMap.erpItemBrandName,
            label: '商品品牌',
            fieldProps: {
              mode: 'multiple',
              allowClear: true,
            },
          },
          {
            id: 'erpitemIds',
            label: '商品档案',
            name: 'item_ids',
          },
          {
            id: ErpFieldKeyMap.erpBillTimeType,
            label: '查询单位',
            name: 'unit_type',
            fieldProps: {
              options: unitType,
              allowClear: false,
            },
          },
          // { id: ErpFieldKeyMap.erpOperator, label: '操作人' }
        ],
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.delivery.returnorder.report',
        tableColumn: (formValues: any) => {
          const summary_types = formValues?.summary_types || ['STORE'];
          let temArr: XlbTableColumnProps<any>[] = [];
          summary_types.forEach((item: any) => {
            temArr = [...temArr, ...tableColumnEnum[item]];
          });
          if (
            summary_types.includes('STORE') &&
            summary_types.includes('IN_STORE') &&
            !enable_cargo_owner
          ) {
            temArr = temArr.filter(
              (column) =>
                !['in_org_name'].includes(column.code),
            );
          }
          const inOnly =
            summary_types.includes('STORE') &&
            !summary_types.includes('IN_STORE');
          const outOnly =
            !summary_types.includes('STORE') &&
            summary_types.includes('IN_STORE');
          const both =
            summary_types.includes('STORE') &&
            summary_types.includes('IN_STORE');

          const dynamicDeliveryQuantity = deliveryQuantity.map((col) => {
            if (col.name === '调入数量') {
              return { ...col, hidden: outOnly }; // 如果只选了仓，则隐藏调入
            }
            if (col.name === '调出数量') {
              return { ...col, hidden: !outOnly }; // 只有只选了仓才展示调出
            }
            return col; // 退货数量不变
          });
          const tableColumns = [
            indexItem,
            ...temArr,
            ...dynamicDeliveryQuantity,
            returnRate,
          ];
          return tableColumns;
        },
        // selectMode: 'single',
        // keepDataSource: false,
        showColumnsSetting: false,
        immediatePost: true,
        changeColumnAndResetDataSource: false,
        footerDataSource: (data: any, formValues: any) => {
          return Array.isArray(data)
            ? {
                _index: '合计',
                delivery_in_quantity: data
                  ?.reduce(
                    (acc: any, cur: any) =>
                      acc + cur?.delivery_in_quantity || 0,
                    0,
                  )
                  .toFixed(2),
                apply_quantity: data
                  ?.reduce(
                    (acc: any, cur: any) => acc + cur?.apply_quantity || 0,
                    0,
                  )
                  .toFixed(2),
                quantity: data
                  ?.reduce((acc: any, cur: any) => acc + cur?.quantity || 0, 0)
                  .toFixed(2),
                return_rate_str:
                  formValues?.state === 'APPROVE'
                    ? !data?.reduce(
                        (acc: any, cur: any) =>
                          acc + cur?.delivery_in_quantity || 0,
                        0,
                      )
                      ? '-'
                      : `${(
                          (data?.reduce(
                            (acc: any, cur: any) => acc + cur?.quantity || 0,
                            0,
                          ) /
                            data?.reduce(
                              (acc: any, cur: any) =>
                                acc + cur?.delivery_in_quantity || 0,
                              0,
                            )) *
                          100
                        ).toFixed(2)}%`
                    : '0%',
              }
            : {
                _index: '合计',
              };
        },
      }}
      extra={(context) => {
        const { loading, form, dataSource, columns } = context;
        return (
          hasAuth(['退货率统计', '导出']) && (
            <XlbButton.Group>
              <XlbButton
                type="primary"
                disabled={!dataSource?.length}
                label="导出"
                loading={loading}
                onClick={(e) =>
                  exportItem(form?.getFieldsValue(true), e, columns)
                }
                icon={<XlbIcon name="daochu" />}
              />
            </XlbButton.Group>
          )
        );
      }}
      // 导出
      // exportFieldProps={{
      //   url: hasAuth(['退货率统计', '导出'])
      //     ? '/erp/hxl.erp.delivery.returnorder.report.export'
      //     : '',
      //   fileName: '退货率统计.xlsx'
      // }}
    />
  );
};

export default ReturnRateStatistics;
