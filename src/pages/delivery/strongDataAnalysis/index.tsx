import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { UploadOutlined } from '@ant-design/icons';
import {
  XlbIcon as IconFont,
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbPageContainer,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState, type FC } from 'react';
import { filterArr, summaryTypes, tableList, unitTypes } from './data';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const StrongDataAnalysis: FC = () => {
  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  const [form] = XlbBasicForm.useForm();
  const [stateFormlist, setFormList] = useState<SearchFormType[]>([
    {
      width: 372,
      type: 'compactDatePicker',
      label: '日期选择',
      name: 'compactDatePicker',
      allowClear: false,
    },
    {
      label: '调出组织',
      name: 'out_org_ids',
      type: 'select',
      multiple: true,
      clear: true,
      hidden: !enable_organization,
      selectRequestParams:{
        url:'/erp-mdm/hxl.erp.org.find',
        responseTrans:{
          label:'name',
          value:'id'
        }
      }
    },
    {
      label: '调出门店',
      name: 'store_ids',
      type: 'inputDialog',
      allowClear: false,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          status: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },
    {
      label: '发货仓库',
      name: 'storehouse_id',
      type: 'select',
      clear: true,
      dropdownMatchSelectWidth: 240,
      options:[]
      // dependencies: ['store_ids'],
      // linkId: 'storehouse'
    },
    {
      label: '调入组织',
      name: 'in_org_ids',
      clear: true,
      type: 'select',
      multiple: true,
      hidden: !enable_organization,
      selectRequestParams:{
        url:'/erp-mdm/hxl.erp.org.find',
        responseTrans:{
          label:'name',
          value:'id'
        }
      }
    },
    {
      label: '调入门店',
      name: 'in_store_ids',
      type: 'inputDialog',
      clear: true,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          status: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },
    {
      label: '商品档案',
      name: 'item_ids',
      type: 'inputDialog',
      allowClear: true,
      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
      },
    },
    {
      label: '查询单位',
      name: 'unit_type',
      type: 'select',
      clear: true,
      options: unitTypes,
    },
    {
      label: '统配原因',
      name: 'reason_name',
      type: 'select',
      clear: true,
      selectRequestParams: {
        url: '/erp/hxl.erp.reason.find',
        postParams: {
          type: 'FROCE_DELIVER',
        },
        responseTrans: {
          label: 'name',
          value: 'name',
        },
      },
    },
    {
      label: '汇总条件',
      name: 'summary_types',
      type: 'select',
      multiple: true,
      clear: true,
      options: summaryTypes,
    },
  ]);
  const [itemArr, setItemArr] = useState<any[]>(
    tableList.filter(
      (v) =>
        enable_cargo_owner || (v.name !== '调入组织' && v.name !== '调出组织'),
    ),
  );
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 0,
  });
  const tableRef = useRef(null);

  let refresh = () => {};

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'basic_force_delivery_quantity':
      case 'force_delivery_quantity':
        item.render = (value: any) => {
          return (
            <div>{value != undefined ? Number(value).toFixed(3) : '0.000'}</div>
          );
        };
        break;
      case 'force_delivery_money':
        item.render = (value: any) => {
          return (
            <div>
              {hasAuth(['统配数据分析/配送价', '查询'])
                ? value != undefined
                  ? Number(value).toFixed(2)
                  : '0.00'
                : '****'}
            </div>
          );
        };
        break;
    }
    return item;
  };

  // 导出
  const exportItem = async (requestForm: any, setLoading: Function, e: any) => {
    setLoading(true);
    const data = prevPost();
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryreport.forcedelivery.export',
      { ...data },
    );
    if (res.code == 0) {
      message.success('导出受理成功，请前往下载中心查看');
    }
    setLoading(false);
  };

  const onValuesChange = (e: any) => {
    if (Object.keys(e).includes('store_ids')) {
      form.setFieldValue('storehouse_id', null);
      stateFormlist.find((i) => i.name === 'storehouse_id')!.options = [];

      setFormList([...stateFormlist]);
      if (e?.store_ids?.length > 1) {
        stateFormlist.find((i) => i.name === 'storehouse_id')!.disabled = true;
        setFormList([...stateFormlist]);
      } else if (e?.store_ids?.length == 1) {
        stateFormlist.find((i) => i.name === 'storehouse_id')!.disabled = false;
        setFormList([...stateFormlist]);
        getStockData();
      }
    }

    if (e?.summary_types) {
      checkSummaryType();
    }
  };

  // 汇总条件变化时表格列显隐藏
  const checkSummaryType = () => {
    const allSummaryTypeCode = summaryTypes.map((item) => item.code);
    const chooseSummaryTypeCode = summaryTypes
      .filter((item) =>
        form.getFieldValue('summary_types')?.includes(item.value),
      )
      .map((item) => item.code);

    const newArr = itemArr.map((item) => {
      if (allSummaryTypeCode.includes(item.code || '')) {
        item.hidden = !chooseSummaryTypeCode.includes(item.code || '');
      }

      return item;
    });
    setItemArr([...newArr]);
  };

  //处理查询参数
  const prevPost = () => {
    const data = {
      ...form.getFieldsValue(),
      audit_date: form.getFieldValue('compactDatePicker'),
      company_id: LStorage.get('userInfo').company_id,
      operator_store_id: LStorage.get('userInfo').store_id,
    };
    return { ...data };
  };

  // 获取发货仓库options
  const getStockData = async () => {
    const id = form.getFieldValue('store_ids')[0];
    const res = await ErpRequest.post('/erp-mdm/hxl.erp.storehouse.store.find', {
      store_id: id,
    });
    if (res?.code === 0) {
      const labArr = res.data
        .filter((v: any) => v.distribution === true)
        .map((item: any) => ({ label: item.name, value: item.id }));
      stateFormlist.find((item) => item.label === '发货仓库')!.options = labArr;
      setFormList([...stateFormlist]);
    }
  };

  useEffect(() => {
    if (LStorage.get('userInfo').store_id) {
      getStockData();
    }
  }, []);

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.deliveryreport.forcedelivery.page'}
      // immediatePost={true}
      tableColumn={itemArr.map((v) => tableRender(v))}
      prevPost={prevPost}
      footerDataSource={(data) => {
        const footerData = [
          {
            _index: '合计',
            force_delivery_quantity:
              data?.force_delivery_quantity_total?.toFixed(3) || '0.000',
            basic_force_delivery_quantity:
              data?.basic_force_delivery_quantity_total?.toFixed(3) || '0.000',
            force_delivery_money: hasAuth(['统配数据分析/配送价', '查询'])
              ? data?.force_delivery_money_total.toFixed(2) || '0.00'
              : '****',
          },
        ];
        return footerData;
      }}
    >
      <SearchForm>
        <XlbForm
          isHideDate
          formList={stateFormlist}
          form={form}
          onValuesChange={onValuesChange}
          getFormRecord={refresh}
          initialValues={{
            compactDatePicker: [
              dayjs()?.format('YYYY-MM-DD'),
              dayjs()?.format('YYYY-MM-DD'),
            ],
            time_type: 0,
            unit_type: 'DELIVERY',
            store_ids: [LStorage.get('userInfo').store_id],
          }}
        />
      </SearchForm>
      <ToolBtn showColumnsSetting={false}>
        {({
          fetchData,
          dataSource,
          requestForm,
          loading,
          setLoading,
        }: // setCurrentIndex,
        any) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['统配数据分析', '查询']) && (
                <XlbButton
                  loading={loading}
                  label="查询"
                  type="primary"
                  onClick={() => {
                    fetchData();
                  }}
                  icon={
                    <IconFont name="sousuo" color="currentColor" size={16} />
                  }
                />
              )}
              {hasAuth(['统配数据分析', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={loading || !dataSource?.length}
                  onClick={(e) => exportItem(requestForm, setLoading, e)}
                  icon={<UploadOutlined size={16} />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        selectMode={false}
        emptyCellHeight={300}
        bordered={false}
        primaryKey={'id'}
        ref={tableRef}
      />
    </XlbPageContainer>
  );
};

export default StrongDataAnalysis;