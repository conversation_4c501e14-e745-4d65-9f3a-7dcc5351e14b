import { XlbFetch } from '@xlb/utils';

interface ReadParams {
  store_id: number;
  supplier_id: number;
  share_type: 'SHARE' | 'DIRECT';
}
export const readDetail = async (data: ReadParams) => {
  return await XlbFetch.post('/erp/hxl.erp.suppliersharedconfig.read', data);
};

interface EditParams {
  supplier_id: number;
  details: {
    share_store_id: number;
    store_id: number;
  }[];
}
export const addPurchaseShare = async (data: EditParams) => {
  return await XlbFetch.post('/erp/hxl.erp.suppliersharedconfig.save', data);
};
export const updatePurchaseShare = async (data: EditParams) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.suppliersharedconfig.batchupdate',
    data,
  );
};
export const deletePurchaseShare = async (data: { ids: number[] }) => {
  return await XlbFetch.post('/erp/hxl.erp.suppliersharedconfig.delete', data);
};

interface SearchParams {
  org_ids: number[];
  share_store_ids: number[];
  store_ids: number[];
  supplier_ids: number[];
}
export const detailsExport = async (data: SearchParams) => {
  return await XlbFetch.post('/erp/hxl.erp.suppliersharedconfig.export', data);
};

// 获取门店
export const getStore = async (data: any) => {
  return await XlbFetch.post('/erp-mdm/hxl.erp.store.short.page', data);
};

// 获取所有门店
export const getAllStore = async (data: any) => {
  return await XlbFetch.post('/erp-mdm/hxl.erp.store.all.find', data);
};
