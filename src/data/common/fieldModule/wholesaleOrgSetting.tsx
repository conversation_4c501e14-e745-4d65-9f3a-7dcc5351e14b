export const WholesaleOrgSettingKeyMap = {
  cooperateStoreIds: 'cooperateStoreIds',
  shareCenterStoreIds: 'shareCenterStoreIds'
}

export const wholesaleOrgSettingConfig: any[] = [
  {
    tag: 'ERP',
    label: '合作门店',
    id: WholesaleOrgSettingKeyMap?.cooperateStoreIds,
    name: 'cooperate_store_ids',
    componentType: 'select',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp/hxl.erp.wholesalecenterstore.find', {})
      if (res.code == 0 && Array.isArray(res?.data)) {
        const map = new Map()
        const newArr = res?.data?.filter(
          (v: any) => !map.has(v.cooperate_store_id) && map.set(v.cooperate_store_id, v)
        )
        return newArr?.map((item: any) => {
          return {
            label: item.cooperate_store_name,
            value: item.cooperate_store_id
          }
        })
      }
      return []
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    }
  },
  {
    tag: 'ERP',
    label: '批发共享中心',
    id: WholesaleOrgSettingKeyMap?.shareCenterStoreIds,
    name: 'share_center_store_ids',
    componentType: 'select',
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp/hxl.erp.wholesalecenterstore.find', {})
      if (res.code == 0 && Array.isArray(res?.data)) {
        const map = new Map()
        const newArr = res?.data?.filter(
          (v: any) => !map.has(v.share_center_store_id) && map.set(v.share_center_store_id, v)
        )
        return newArr?.map((item: any) => {
          return {
            label: item.share_center_store_name,
            value: item.share_center_store_id
          }
        })
      }
      return []
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    }
  }
]
