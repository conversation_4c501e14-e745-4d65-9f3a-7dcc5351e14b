import { useBaseParams } from '@/hooks/useBaseParams';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';

import { exportPage } from '@/services/system';
import { LStorage } from '@/utils/storage';
import type { XlbTableColumnProps } from '@xlb/components';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbMessage,
  XlbModal,
  XlbPageContainer,
  XlbSelect,
  XlbShortTable,
  XlbTabs,
  XlbTimePicker,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useUpdateEffect } from 'ahooks';
import type { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { isNumber } from 'lodash';
import { useEffect, useState } from 'react';
import Api from '../api';
import { itemTableListDetail, orderStatusIcons } from '../data';
import styles from './item.less';
const { Option } = XlbSelect;
const { ToolBtn } = XlbPageContainer;

const DeliverySpecialPriceItem = (props: any) => {
  const { record, onBack } = props;
  const [form] = XlbBasicForm.useForm();
  const disabledDate: RangePickerProps['disabledDate'] = (current) => {
    return current && current < dayjs().add(-1, 'day').endOf('day');
  };
  const [rowData, setRowData] = useState<any[]>([]);
  const [storeList, setStoreList] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [itemArrdetail, setdetailItemArr] = useState<
    XlbTableColumnProps<any>[]
  >(JSON.parse(JSON.stringify(itemTableListDetail)));
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [isFold, setIsFold] = useState<boolean>(false);
  const [batchVisible, setBatchVisible] = useState<boolean>(false);
  const [fid, setFid] = useState<any>();
  const [info, setInfo] = useState({ state: 'INIT' });
  const [dataSource, setDataSource] = useState<any>({});
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [backFresh, setBackFresh] = useState<any>(false);
  const [activeKey, setActiveKey] = useState('baseInfo');

  const { enable_organization, enable_cargo_owner } = useBaseParams(
    (state) => state,
  );
  const [orgNameList, setOrgNameList] = useState<any[]>([]);
  const [disabledCargoOwner, setDisabledCargoOwner] = useState<boolean>(false);

  const getInStoreOrg = async (list: any, isEdit: boolean = false) => {
    if (enable_cargo_owner) {
      // 获取货主
      const data = {
        store_id: Array.isArray(list) ? list[0].id : list,
      };
      // 组织接口
      const res = await XlbFetch.post(
        '/erp-mdm/hxl.erp.delivery.cargo.owner.org.find',
        data,
      );
      const cargoOwnerList = res?.data?.map((v) => {
        return {
          ...v,
          label: v.name,
          value: v.id,
        };
      });
      setOrgNameList(cargoOwnerList);
      // 禁用货主弹窗 默认赋值
      if (res?.data?.length == 1) {
        if (isEdit) {
          form.setFieldsValue({
            org_id: res?.data?.[0].id,
            org_name: res?.data?.[0]?.name,
          });
        }
        setDisabledCargoOwner(true);
      }
      // 不操作..
      if (res?.data?.length !== 1) {
        setDisabledCargoOwner(false);
      }
    }
  };

  const supplierSelectChange = (value: any, index: any) => {
    rowData[index].supplier_name = value;
    rowData[index].supplier_details?.map((v: any) => {
      if (v.supplier_name === value) {
        rowData[index].supplier_id = v.supplier_id;
        rowData[index].supplier_name = v.supplier_name;
      }
    });
    setRowData([
      ...rowData?.map((v, index) => {
        return {
          ...v,
          key: index + '_' + v?.item_id,
        };
      }),
    ]);
  };

  const s_type = XlbBasicForm.useWatch('type', form);
  useUpdateEffect(() => {
    if (s_type === 'DELIVERY_OUT') {
      setdetailItemArr(
        itemTableListDetail.filter(
          (item1) =>
            !['batch_stock', 'producing_date'].some(
              (item2) => item1.code === item2,
            ),
        ),
      );
      const removeDuplicatesByItemName = (arr: any) => {
        return arr.reduce((acc: any, item: any) => {
          if (
            !acc.some(
              (existingItem: any) => existingItem.item_id === item.item_id,
            )
          ) {
            acc.push(item);
          }
          return acc;
        }, []);
      };
      setRowData(
        removeDuplicatesByItemName(rowData)?.map((v, index) => {
          return {
            ...v,
            key: index + '_' + v?.item_id,
          };
        }),
      );
    } else {
      setdetailItemArr(itemTableListDetail);
    }
  }, [s_type]);

  // 特价类型onchange事件
  const specialPriceTypeSelectChange = (e, record) => {
    record.special_price_type = e;
    if (e === 1 && isNumber(record.value)) {
      record.special_price = record?.value;
    } else if (e === 2 && isNumber(record?.value)) {
      record.special_price = record?.price * record?.value;
    } else if (e === 3 && isNumber(record?.value)) {
      // if (row.price < row.value) {
      //   message.error('折扣不能大于原价')
      //   return
      // }
      record.special_price = record?.price - record?.value;
    }
    if (record?.value) {
      record.special_price = countMoney(
        record?.special_price_type,
        record?.value,
        record,
      );
    }
    // setRowData([...rowData])
  };

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'supplier_name':
        item.render = (value: any, record: any, index: any) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbSelect
                style={{ width: '100%' }}
                size={'middle'}
                allowClear
                value={record.supplier_name}
                onChange={(e) => supplierSelectChange(e, index?.index)}
                onClick={(e) => e.stopPropagation()}
              >
                {record?.supplier_details?.map((v: any, i: any) => (
                  <Option key={i} value={v.supplier_name}>
                    {v.supplier_name}
                  </Option>
                ))}
              </XlbSelect>
            );
          } else {
            return <div className="info">{record.supplier_name}</div>;
          }
        };
        break;
      case 'initial_quantity':
      case 'activity_quantity':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbInput
                id={item.code + '-' + index.toString()}
                defaultValue={value?.toFixed(3)}
                onFocus={(e) => e.target.select()}
                onChange={(e) => inputChange(e, record, item.code, index)}
                onBlur={(e) => inputBlur(e, index, item.code)}
              />
            );
          } else {
            return <div className="info">{value?.toFixed(3)}</div>;
          }
        };
        break;
      case 'producing_date':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbInputDialog
                value={record.batch_key}
                dialogParams={{
                  type: 'selectBatchTemplate',
                  isLeftColumn: false,
                  dataType: 'lists',
                  isMultiple: false,
                  data: {
                    item_id: record.item_id,
                    company_id: LStorage.get('userInfo')?.company_id,
                    store_id: form.getFieldValue('store_id'),
                    org_id: enable_cargo_owner ? form.getFieldValue('org_id') : undefined,
                  },
                  primaryKey: 'batch_key',
                }}
                fieldNames={{
                  idKey: 'batch_key',
                  nameKey: 'batch_key',
                }}
                onChange={(value: any, options: any[]) => {
                  record.producing_date = options?.[0]?.producing_date;
                  record.expire_date = options?.[0]?.expire_date;
                  record.batch_stock = options?.[0]?.quantity;
                  record.batch_key = options?.[0]?.batch_key;
                  // setRowData([...rowData])
                }}
                width={171}
              />
            );
          } else {
            return <div className="info">{record.batch_key}</div>;
          }
        };
        break;
      case 'special_price_type':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbSelect
                style={{ width: '100%' }}
                value={value}
                onChange={(e) => specialPriceTypeSelectChange(e, record)}
              >
                <Option key={'FIXED_MONEY'} value={'FIXED_MONEY'}>
                  固定金额
                </Option>
                <Option key={'DISCOUNT'} value={'DISCOUNT'}>
                  组织配送价折扣%
                </Option>
                <Option key={'REDUCTION'} value={'REDUCTION'}>
                  组织配送价减免
                </Option>
              </XlbSelect>
            );
          } else {
            return (
              <div className="info">
                {value === 'FIXED_MONEY'
                  ? '固定金额'
                  : value === 'DISCOUNT'
                    ? '组织配送价折扣%'
                    : value === 'REDUCTION'
                      ? '组织配送价减免'
                      : ''}
              </div>
            );
          }
        };
        break;

      case 'value':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbInput
                id={item.code + '-' + index.toString()}
                defaultValue={value?.toFixed(4)}
                onFocus={(e) => e.target.select()}
                onChange={(e) => inputChange(e, record, item.code, index)}
                onBlur={(e) => inputBlur(e, record, item.code)}
              />
            );
          } else {
            return <div className="info">{value?.toFixed(4)}</div>;
          }
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: number) => {
          if (record?._click && record?.item_name) {
            return (
              <XlbInput
                id={item.code + '-' + index.toString()}
                value={value}
                onFocus={(e) => e.target.select()}
                onChange={(e) => {
                  record.memo = e?.target?.value;
                }}
                onBlur={(e) => inputBlur(e, record, item.code)}
              />
            );
          } else {
            return <div className="info">{value}</div>;
          }
        };
        break;
    }
    return item;
  };
  //输入框改变
  const inputChange = (e: any, record: any, key: any, index: any) => {
    const row = rowData[index.index];
    setEdit(true);
    row[key] = Number(e.target.value);
    if (key === 'value') {
      row.special_price = countMoney(row?.special_price_type, row?.value, row);
    }
    setRowData([...rowData]);
  };
  //失去焦点
  const inputBlur = (e: any, record: any, key: any) => {
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    const arr = [
      'special_price',
      'initial_quantity',
      'activity_quantity',
      'value',
    ];

    if (
      arr.includes(key) &&
      (!regPos.test(e.target.value) ||
        e.target.value < 0 ||
        e.target.value > 999999999)
    ) {
      XlbTipsModal({
        tips: `【${itemArrdetail.find((v) => v.code === key)?.name}】请输入0-999999999之间的数字`,
      });
      record[key] = 0;
      return;
    }
    if (key == 'memo' && e.target.value.length > 20) {
      XlbTipsModal({
        tips: `备注长度应<=20`,
      });
      record[key] = e.target.value.substring(0, 20);
      return;
    }
  };
  //读取信息
  const readinfo = async (fid: any) => {
    const res = await Api.readInfo({ fid });
    if (res.code === 0) {
      form.setFieldsValue({
        ...res.data,
        start_time: dayjs(res.data.start_time.slice(0, 10)),
        end_time: dayjs(res.data.end_time.slice(0, 10)),
        start_hour: dayjs(res.data.start_time.slice(11), 'HH:mm'),
        end_hour: dayjs(res.data.end_time.slice(11), 'HH:mm'),
        create_time: res.data.create_time?.slice(0, 10),
        audit_time: res.data.audit_time?.slice(0, 10),
        updata_time: res.data.updata_time?.slice(0, 10),
        supply_store_ids: res?.data?.supply_stores?.map((v: any) => v.id) || '',
        supply_business_area_ids:
          res?.data?.supply_business_areas?.map((v: any) => v.id) || '',
        supply_area_codes:
          res?.data?.supply_areas?.map((v: any) => v.code) || '',
        supply_store_names:
          res?.data?.supply_stores?.map((v: any) => v.store_name) || '',
      });
      if (enable_cargo_owner) {
        getInStoreOrg(res?.data?.store_id);
      }
      setFid(res.data.fid);
      setInfo({ state: res.data.state });
      setDataSource(res.data);
      setRowData(
        res.data.details?.map((v, index) => {
          return { ...v, key: index + '_' + v?.item_id };
        }),
      );
      setPagin({ ...pagin, total: res.data.details.length });
    }
  };
  const getStore = async () => {
    const res = await Api.getCenterStore({
      company_id: LStorage.get('userInfo')?.company_id,
      operator_store_id: LStorage.get('userInfo'),
    });
    if (res?.code === 0) {
      const arr = res.data?.map((v: any) => ({
        ...v,
        label: v.store_name,
        value: v.id,
      }));
      form?.setFieldsValue({
        storeList: arr,
      });
      setStoreList(arr);
    }
  };

  useEffect(() => {
    getStore();
    setFid(record.fid);
    if (record.fid === 1) {
      form.setFieldsValue({
        org_id: !enable_cargo_owner
          ? enable_organization &&
            JSON.parse(localStorage.userInfo).value.org_id
          : undefined,
        org_name: !enable_cargo_owner
          ? enable_organization &&
            JSON.parse(localStorage.userInfo).value.org_name
          : undefined,
        store_id: JSON.parse(localStorage.userInfo).value.store_id,
        start_time: dayjs().format('YYYY-MM-DD'),
        end_time: dayjs().format('YYYY-MM-DD'),
        start_hour: '00:00:00',
        end_hour: '23:59:00',
      });
      if (enable_cargo_owner) {
        getInStoreOrg(JSON.parse(localStorage.userInfo).value.store_id, true);
      }
    } else {
      readinfo(record.fid);
    }
    itemArrdetail.forEach((item) => {
      if (item.code === 'price') {
        item.name = enable_organization ? '组织配送价' : '中心配送价';
      }
    });
    setdetailItemArr([...itemArrdetail]);
  }, []);

  // 保存 审核 作废t:string
  const operateOrder = async (t: string) => {
    const _rowData = rowData?.filter((v) => v?.item_id);
    if (_rowData.length == 0) {
      XlbTipsModal({
        tips: '请先添加商品！',
      });
      return;
    }

    const s_time = dayjs(form.getFieldValue('start_time'));
    const e_time = dayjs(form.getFieldValue('end_time'));

    if (s_time.format('X') > e_time.format('X')) {
      XlbTipsModal({
        tips: '结束日期≥开始日期！',
      });
      return;
    }
    const s_hour =
      s_time?.format('YYYY-MM-DD') +
      ' ' +
      dayjs(form.getFieldValue('start_hour'), 'HH:mm').format('HH:mm');
    const e_hour =
      e_time?.format('YYYY-MM-DD') +
      ' ' +
      dayjs(form.getFieldValue('end_hour'), 'HH:mm').format('HH:mm');

    if (dayjs(e_hour).isBefore(dayjs(s_hour))) {
      XlbTipsModal({
        tips: '起始时间≤结束时间',
      });
      return;
    }
    if (_rowData.some((v) => v.special_price < 0)) {
      XlbTipsModal({
        tips: '配送特价不合法！',
      });
      return;
    }
    if (form.getFieldValue('type') === 'REQUEST') {
      const obj1 = _rowData.find(
        (v) => v.batch_stock && v.activity_quantity > v.batch_stock,
      );
      if (obj1) {
        XlbTipsModal({
          tips: `商品【${obj1.item_name}】需活动数量≤批次库存！`,
        });
        return;
      }
      const findDuplicatesByBatchUk = (arr: any) => {
        const hashTable = {};
        const duplicates: any[] = [];
        arr.forEach((item: any) => {
          if (
            item.batch_key === '$_$' ||
            item.batch_key === '' ||
            !item.batch_key
          )
            return;
          const key = `${item.batch_key}-${item.item_id}`;
          if (hashTable[key]) {
            hashTable[key].count += 1;
            if (hashTable[key].count === 2) {
              duplicates.push(item);
            }
          } else {
            hashTable[key] = { item, count: 1 };
          }
        });
        return duplicates;
      };
      const obj2 = findDuplicatesByBatchUk(rowData);
      if (obj2.length) {
        XlbTipsModal({
          tips: `商品【${obj2.map((v: any) => v.item_name).join('、')}】批次重复！`,
        });
        return;
      }
    }
    const obj = _rowData.find((v) => v.activity_quantity < v.initial_quantity);
    if (obj) {
      XlbTipsModal({
        tips: `商品【${obj.item_name}】需活动数量≥起始数量！`,
      });
      return;
    }

    _rowData.forEach((item: any) => {
      item.supplier_id = item?.supplier_name ? item?.supplier_id : null;
    });

    const data = {
      org_id: enable_organization ? form.getFieldValue('org_id') : undefined,
      supply_org_id: enable_organization
        ? form.getFieldValue('supply_org_id')
        : undefined,
      start_time: s_hour,
      end_time: e_hour,
      supply_store_ids: form.getFieldValue('supply_store_ids') || undefined,
      supply_business_area_ids:
        form.getFieldValue('supply_business_area_ids') || undefined,
      supply_area_codes: form.getFieldValue('supply_area_codes') || undefined,
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      store_id: form.getFieldValue('store_id'),
      type: form.getFieldValue('type'),
      details: _rowData,
      store_count: form.getFieldValue('supply_store_ids')?.length || 0,
    };
    let res: any = null;
    setIsLoading(true);
    t === '保存' && fid === 1 && (res = await Api.addInfo(data));
    t === '保存' && fid !== 1 && (res = await Api.updateInfo(data));
    t === '审核' && (res = await Api.auditInfo(data));
    t === '反审核' &&
      (res = await Api.reaudit({ fid: form.getFieldValue('fid') }));
    t === '处理通过' &&
      (res = await Api.handle({ fid: form.getFieldValue('fid') }));
    t === '处理拒绝' &&
      (res = await Api.handlerefuse({ fid: form.getFieldValue('fid') }));
    t === '作废' &&
      (res = await Api.invalidInfo({ fid: form.getFieldValue('fid') }));
    setIsLoading(false);
    if (res.code == 0) {
      setEdit(false);
      XlbMessage.success('操作成功');
      readinfo(res.data?.fid || form.getFieldValue('fid'));
      setBackFresh(true);
    }
  };

  //返回前判断保存状态
  const goBack = () => {
    if (edit) {
      XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        isCancel: true,
        onOk: () => {
          onBack(backFresh);
          // back('/xlb_erp/deliverySpecialPrice/index', backFresh);
        },
      });
      return;
    }
    onBack(backFresh);
    // back('/xlb_erp/deliverySpecialPrice/index', backFresh);
  };

  // 批量添加
  const confirmAdd = (list: any, add_type = 'item') => {
    const type = form.getFieldValue('type');
    let filterArr = [...list];
    if (type === 'DELIVERY_OUT') {
      const ids = rowData.map((v) => v.item_id);
      const repeatArr = list?.filter((v) =>
        ids.includes(add_type === 'import' ? v?.item_id : v.id),
      );
      const rName = [
        repeatArr.map(
          (v) => `【${add_type === 'import' ? v?.item_name : v.name}】`,
        ),
      ];
      if (repeatArr.length) {
        XlbTipsModal({
          tips: '以下商品已存在，不允许重复添加，系统已自动过滤！',
          tipsList: rName,
        });
      }
      filterArr = list.filter(
        (item: any) =>
          !ids.includes(add_type === 'import' ? item?.item_id : item.id),
      );
    }
    const newList = filterArr?.map((v) => {
      return {
        item_code: v.code || v.item_code,
        item_bar_code: v.bar_code || v.item_bar_code,
        item_name: v.name || v.item_name,
        item_spec: v.purchase_spec || v.item_spec,
        unit: v.delivery_unit || v.unit,
        ratio: v.delivery_ratio || v.ratio,
        basic_unit: v.unit,
        price: v.price,
        special_price: add_type === 'import' ? v?.special_price : v.price,
        memo: null,
        item_id: add_type === 'import' ? v.item_id : v.id,
        initial_quantity: add_type === 'import' ? v?.initial_quantity : 0,
        activity_quantity: add_type === 'import' ? v?.activity_quantity : 0,
        remain_quantity: add_type === 'import' ? v?.remain_quantity : 0,
        supplier_details: v?.supplier_details,
        supplier_id: v?.supplier_id,
        supplier_name: v?.supplier_name,
        memo: v?.memo,
        special_price_type: 'FIXED_MONEY',
        value: 0,
        _click: false,
        _edit: false,
      };
    });
    newList.length > 0 ? setEdit(true) : setEdit(false);
    // 最后过滤掉没有item_id的
    const mergeArr = [...rowData, ...newList]?.filter((v) => v?.item_id);
    setPagin({
      ...pagin,
      pageSize: mergeArr.length,
      total: mergeArr.length,
    });
    setRowData(
      mergeArr?.map((v, index) => ({ ...v, key: index + '_' + v?.item_id })),
    );
  };
  const countMoney = (type: number | string, value: number, row: any) => {
    switch (type) {
      case 'FIXED_MONEY':
        return value;
      case 'DISCOUNT':
        return row.price * value * 0.01;
      case 'REDUCTION':
        return row.price - value < 0 ? 0 : row.price - value;
      default:
        break;
    }
  };
  // 批量修改
  const batchChange = () => {
    const check: string[] = form.getFieldValue('checkValue');
    if (check?.length) {
      const arr = rowData
        .filter((e) => !e.newRow)
        .map((v) => ({
          ...v,
          initial_quantity: check.includes('number')
            ? form.getFieldValue('number_value')
            : v.initial_quantity,
          special_price: check.includes('money')
            ? countMoney(
                form.getFieldValue('money_type'),
                form.getFieldValue('money_value'),
                v,
              )
            : v.special_price,
          special_price_type:
            form.getFieldValue('money_type') || v.special_price_type,
          value: form.getFieldValue('money_value') || v.value,
          _edit: false,
          _click: false,
        }));
      setRowData(
        arr?.map((v, index) => ({ ...v, key: index + '_' + v?.item_id })),
      );
      form.setFieldsValue({
        checkValue: [],
        number_value: 0,
        money_value: 0,
        money_type: 0,
      });
      setBatchVisible(false);
    } else {
      XlbMessage.error('请选择设置项');
    }
  };
  const exportItem = async () => {
    setIsLoading(true);
    const data = { fid: form.getFieldValue('fid'), responseType: 'blob' };
    const res = await exportPage(
      '/erp/hxl.erp.deliveryspecialprice.detail.export',
      { data },
    );
    const download = new Download();
    download.filename = '配送特价详情导出.xlsx';
    download.xlsx(res);
    setIsLoading(false);
  };

  // 门店导入
  const importStore = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.commonstorename.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storenametemplate.download`,
    });
    if (res.code !== 0) return;
    form.setFieldsValue({
      supply_store_ids: res?.data?.store_ids,
    });
  };

  return (
    // <XlbConfigProvider.Provider
    //   value={{
    //     baseURL: process.env.BASE_URL as string,
    //     config: globalConfig,
    //     globalFetch: XlbFetch,
    //     isOldBtn: true,
    //   }}
    // >
    <div className={styles.container}>
      <XlbPageContainer>
        <XlbModal
          title="批量设置"
          open={batchVisible}
          width={400}
          isCancel={true}
          onCancel={() => {
            form?.setFieldsValue({
              checkValue: [],
              number_value: 0,
              money_value: 0,
              money_type: 0,
            });
            setBatchVisible(false);
          }}
          onOk={() => batchChange()}
          centered
        >
          <div style={{ width: 'auto', fontSize: '16px' }} className="v-flex">
            <XlbBasicForm
              colon
              form={form}
              autoComplete="off"
              layout="vertical"
            >
              <XlbBasicForm.Item name="checkValue">
                <XlbCheckbox.Group>
                  <div style={{ marginTop: 10, lineHeight: '30px' }}>
                    <XlbCheckbox value={'number'}> 起始数量 </XlbCheckbox>
                    <XlbBasicForm.Item
                      name="number_value"
                      initialValue={0}
                      style={{ display: 'inline-block' }}
                    >
                      <XlbInputNumber
                        min={0}
                        max={999999999}
                        size={'small'}
                        style={{
                          width: 210,
                          height: 27,
                          lineHeight: '25px',
                        }}
                      />
                    </XlbBasicForm.Item>
                  </div>
                  <div style={{ marginTop: 10, lineHeight: '30px' }}>
                    <XlbCheckbox value={'money'}> 配送特价 </XlbCheckbox>
                    <XlbBasicForm.Item
                      name="money_type"
                      initialValue={'FIXED_MONEY'}
                      style={{ display: 'inline-block' }}
                    >
                      <XlbSelect style={{ width: 150 }}>
                        <Option key={'FIXED_MONEY'} value={'FIXED_MONEY'}>
                          固定金额
                        </Option>
                        <Option key={'DISCOUNT'} value={'DISCOUNT'}>
                          组织配送价折扣%
                        </Option>
                        <Option key={'REDUCTION'} value={'REDUCTION'}>
                          组织配送价减免
                        </Option>
                      </XlbSelect>
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      name="money_value"
                      initialValue={0}
                      style={{ display: 'inline-block' }}
                    >
                      <XlbInputNumber
                        min={0}
                        max={999999999}
                        size={'small'}
                        style={{
                          width: 50,
                          height: 27,
                          marginLeft: 10,
                          lineHeight: '25px',
                        }}
                      />
                    </XlbBasicForm.Item>
                  </div>
                </XlbCheckbox.Group>
              </XlbBasicForm.Item>
            </XlbBasicForm>
          </div>
        </XlbModal>
        <ToolBtn withHLine={false}>
          {({}) => {
            return (
              <XlbButton.Group>
                {hasAuth(['配送特价', '编辑']) && (
                  <XlbButton
                    label="保存"
                    type="primary"
                    disabled={info.state !== 'INIT'}
                    loading={isLoading}
                    onClick={() => operateOrder('保存')}
                    icon={<XlbIcon size={16} name="baocun" />}
                  />
                )}
                {hasAuth(['配送特价', '审核']) && (
                  <XlbButton
                    label="审核"
                    type="primary"
                    disabled={info.state !== 'INIT' || fid === 1}
                    loading={isLoading}
                    onClick={() => operateOrder('审核')}
                    icon={<XlbIcon size={16} name="shenhe" />}
                  />
                )}
                {hasAuth(['配送特价', '反审核']) && (
                  <XlbButton
                    label="反审核"
                    type="primary"
                    disabled={info.state !== 'AUDIT' || fid === 1}
                    loading={isLoading}
                    onClick={() => operateOrder('反审核')}
                    icon={<XlbIcon size={16} name="fanshenhe" />}
                  />
                )}
                {hasAuth(['配送特价', '处理']) && (
                  <XlbDropdownButton
                    label="处理"
                    dropList={
                      hasAuth(['配送特价', '处理'])
                        ? [
                            {
                              label: '处理通过',
                              disabled:
                                rowData[0]?.newRow || info.state !== 'AUDIT',
                            },
                            {
                              label: '处理拒绝',
                              disabled:
                                rowData[0]?.newRow || info.state !== 'AUDIT',
                            },
                          ]
                        : []
                    }
                    dropdownItemClick={(index: number, item: any) => {
                      switch (item?.label) {
                        case '处理通过':
                          operateOrder('处理通过');
                          break;
                        case '处理拒绝':
                          operateOrder('处理拒绝');
                          break;
                      }
                    }}
                  />
                )}
                {hasAuth(['配送特价', '作废']) && (
                  <XlbButton
                    label="作废"
                    type="primary"
                    onClick={() => operateOrder('作废')}
                    disabled={info.state !== 'HANDLE' || fid === 1}
                    loading={isLoading}
                    icon={<XlbIcon size={16} name="shanchu" />}
                  />
                )}
                {hasAuth(['配送特价', '导出']) && (
                  <XlbButton
                    label="导出"
                    type="primary"
                    onClick={() => exportItem()}
                    disabled={
                      !rowData?.length || !hasAuth(['配送特价', '导出'])
                    }
                    loading={isLoading}
                    icon={<XlbIcon size={16} name="daochu" />}
                  />
                )}
                {/* {!hasAuth(['配送特价', '导出']) &&
                  !hasAuth(['配送特价', '打印']) ? null : (
                    <XlbDropdownButton
                      label="业务操作"
                      dropList={
                        hasAuth(['配送特价', '导出']) && hasAuth(['配送特价', '打印'])
                          ? [
                              {
                                label: '导出',
                                disabled: rowData[0]?.newRow || !hasAuth(['配送特价', '导出'])
                              },
                              {
                                label: '打印',
                                disabled: rowData[0]?.newRow || !hasAuth(['配送特价', '打印'])
                              }
                            ]
                          : hasAuth(['配送特价', '导出'])
                          ? [
                              {
                                label: '导出',
                                disabled: rowData[0]?.newRow || !hasAuth(['配送特价', '导出'])
                              }
                            ]
                          : [
                              {
                                label: '打印',
                                disabled: rowData[0]?.newRow || !hasAuth(['配送特价', '打印'])
                              }
                            ]
                      }
                      dropdownItemClick={(index: number, item: any) => {
                        switch (item?.label) {
                          case '导出':
                            exportItem()
                            break
                          case '打印':
                            // printItem().then()
                            break
                        }
                      }}
                    />
                  )} */}
                <XlbButton
                  label="返回"
                  type="primary"
                  onClick={goBack}
                  icon={<XlbIcon size={16} name="fanhui" />}
                />
              </XlbButton.Group>
            );
          }}
        </ToolBtn>

        {isFold ? null : (
          <XlbBasicForm
            style={{ paddingLeft: '12px' }}
            colon
            form={form}
            autoComplete="off"
            layout="inline"
          >
            <XlbTabs
              onChange={(e) => setActiveKey(e)}
              activeKey={activeKey}
              defaultActiveKey="baseInfo"
              items={[
                {
                  label: '基本信息',
                  key: 'baseInfo',
                  children: (
                    <div className="row-flex">
                      <div style={{ flex: 1, marginTop: 12 }}>
                        <div className="row-flex" style={{ flexWrap: 'wrap' }}>
                          <XlbBasicForm.Item label="配送门店" name="store_id">
                            <XlbSelect
                              style={{ width: 180 }}
                              allowClear={false}
                              disabled={
                                !hasAuth(['配送特价', '编辑']) ||
                                info.state !== 'INIT' ||
                                // rowData里面存在item_code有值的才会禁用
                                rowData?.some((v: any) => v?.item_code)
                              }
                              onChange={(value, options) => {
                                console.log(value, options, 'options========>');
                                  if (options?.org_id) {
                                    form.setFieldsValue({
                                      org_id: !enable_cargo_owner?options?.org_id:undefined  ,
                                      org_name: !enable_cargo_owner?options?.org_name:undefined,
                                    });
                                  }
                                form.setFieldValue('store_id', value);
                                if (enable_cargo_owner) {
                                  getInStoreOrg(value, true);
                                }
                              }}
                              options={
                                form?.getFieldValue('storeList') || storeList
                              }
                            />
                          </XlbBasicForm.Item>
                          {/* {enable_organization ? (
                            <XlbBasicForm.Item label="配送组织" name="org_name">
                              <XlbInput disabled style={{ width: '180px' }} />
                            </XlbBasicForm.Item>
                          ) : null} */}
                          {enable_organization ? (
                            enable_cargo_owner ? (
                              <XlbBasicForm.Item
                                rules={[
                                  { required: true, message: '请选择配送组织' },
                                ]}
                                label="配送组织"
                                name="org_id"
                              >
                                <XlbSelect
                                  showSearch
                                  style={{ width: '180px' }}
                                  disabled={
                                    !hasAuth(['配送特价', '编辑']) ||
                                    info.state !== 'INIT' ||
                                    disabledCargoOwner ||
                                    rowData?.some((v: any) => v?.item_code)
                                  }
                                  optionFilterProp="children"
                                  filterOption={(input, option) => {
                                    return (
                                      (
                                        `${option!.label ? option!.label.toString() : ''}` as unknown as string
                                      )
                                        .toLowerCase()
                                        .includes(input.toLowerCase()) ||
                                      (
                                        `${option!.value ? option!.value.toString() : ''}` as unknown as string
                                      )
                                        .toLowerCase()
                                        .includes(input.toLowerCase())
                                    );
                                  }}
                                  options={orgNameList}
                                ></XlbSelect>
                              </XlbBasicForm.Item>
                            ) : (
                              <XlbBasicForm.Item
                                label="配送组织"
                                name="org_name"
                              >
                                <XlbInput disabled style={{ width: '180px' }} />
                              </XlbBasicForm.Item>
                            )
                          ) : null}
                          <XlbBasicForm.Item
                            noStyle
                            dependencies={[
                              'supply_area_codes',
                              'supply_business_area_ids',
                              'supply_store_ids',
                            ]}
                          >
                            {({ getFieldValue }) => (
                              <>
                                <XlbBasicForm.Item
                                  label="行政区域"
                                  name="supply_area_codes"
                                >
                                  <XlbInputDialog
                                    disabled={
                                      !hasAuth(['配送特价', '编辑']) ||
                                      (!form.getFieldValue('supply_store_ids')
                                        ?.length &&
                                        info.state !== 'INIT') ||
                                      getFieldValue('supply_store_ids')
                                        ?.length ||
                                      getFieldValue('supply_business_area_ids')
                                        ?.length
                                    }
                                    treeModalConfig={{
                                      zIndex: 2002,
                                      title: '选择区域', // 标题
                                      url: '/erp-mdm/hxl.erp.store.area.find', // 请求地址
                                      dataType: 'lists',
                                      checkable: true, // 是否多选
                                      primaryKey: 'code',
                                      fieldName: {
                                        id: 'code',
                                        parent_id: 'parent_code',
                                        name: 'name',
                                      },
                                      width: 360,
                                    }}
                                    fieldNames={{
                                      idKey: 'code',
                                      nameKey: 'name',
                                    }}
                                    width={180}
                                  />
                                </XlbBasicForm.Item>

                                <XlbBasicForm.Item
                                  label="业务区域"
                                  name="supply_business_area_ids"
                                >
                                  <XlbInputDialog
                                    disabled={
                                      !hasAuth(['配送特价', '编辑']) ||
                                      (!form.getFieldValue('supply_store_ids')
                                        ?.length &&
                                        info.state !== 'INIT') ||
                                      getFieldValue('supply_area_codes')
                                        ?.length ||
                                      getFieldValue('supply_store_ids')?.length
                                    }
                                    dialogParams={{
                                      type: 'businessArea',
                                      isLeftColumn: false,
                                      isMultiple: true,
                                      dataType: 'lists',
                                      initAfterPost: (data: any) => {
                                        const dataValueDetails =
                                          data?.value_details?.map((v: any) => {
                                            return {
                                              ...v,
                                              show_name:
                                                v.six_name ||
                                                v.five_name ||
                                                v.four_name ||
                                                v.third_name ||
                                                v.second_name ||
                                                v.first_name,
                                            };
                                          });
                                        return {
                                          list: dataValueDetails,
                                          total: data?.value_details?.length,
                                        };
                                      },
                                      handleDefaultValue: (data) => {
                                        const _data = data.map((v) => {
                                          return {
                                            ...v,
                                            id: v.id,
                                            show_name:
                                              v.six_name ||
                                              v.five_name ||
                                              v.four_name ||
                                              v.third_name ||
                                              v.second_name ||
                                              v.first_name,
                                          };
                                        });
                                        return _data;
                                      },
                                    }}
                                    fieldNames={{
                                      idKey: 'id',
                                      nameKey: 'show_name',
                                    }}
                                    width={180}
                                  />
                                </XlbBasicForm.Item>

                                <div style={{ position: 'relative' }}>
                                  <XlbBasicForm.Item
                                    label="应用门店"
                                    name="supply_store_ids"
                                  >
                                    <XlbInputDialog
                                      disabled={
                                        !hasAuth(['配送特价', '编辑']) ||
                                        (!form.getFieldValue('supply_store_ids')
                                          ?.length &&
                                          info.state !== 'INIT') ||
                                        getFieldValue('supply_area_codes')
                                          ?.length ||
                                        getFieldValue(
                                          'supply_business_area_ids',
                                        )?.length
                                      }
                                      dialogParams={{
                                        type: 'store',
                                        isMultiple: true,
                                        data: {
                                          isShowWaitAssign: true,
                                          center_flag: LStorage.get('userInfo')
                                            ?.store?.enable_delivery_center
                                            ? false
                                            : null,
                                          enable_organization: false,
                                          status: true,
                                        },
                                      }}
                                      fieldNames={{
                                        idKey: 'id',
                                        nameKey: 'store_name',
                                      }}
                                      width={180}
                                    />
                                  </XlbBasicForm.Item>
                                  {info.state == 'INIT' &&
                                    hasAuth(['配送特价', '编辑']) && (
                                      <XlbButton
                                        label="导入"
                                        type={'text'}
                                        onClick={() => importStore()}
                                        disabled={
                                          getFieldValue('supply_area_codes')
                                            ?.length ||
                                          getFieldValue(
                                            'supply_business_area_ids',
                                          )?.length
                                        }
                                        style={{
                                          width: '45px',
                                          position: 'absolute',
                                          top: 0,
                                          right: '-22px',
                                        }}
                                      />
                                    )}
                                </div>
                              </>
                            )}
                          </XlbBasicForm.Item>
                          {enable_organization ? (
                            <XlbBasicForm.Item
                              label="应用组织"
                              name="supply_org_name"
                            >
                              <XlbInput disabled style={{ width: '180px' }} />
                            </XlbBasicForm.Item>
                          ) : null}
                          <XlbBasicForm.Item
                            label="开始时间"
                            name="start_time"
                            // initialValue={dayjs()}
                          >
                            <XlbDatePicker
                              allowClear={false}
                              style={{ width: 180 }}
                              disabledDate={disabledDate}
                              format={'YYYY-MM-DD'}
                              disabled={
                                info.state !== 'INIT' ||
                                !hasAuth(['配送特价', '编辑'])
                              }
                            />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="结束时间"
                            name="end_time"
                            // initialValue={dayjs()}
                          >
                            <XlbDatePicker
                              style={{ width: 180 }}
                              allowClear={false}
                              format={'YYYY-MM-DD'}
                              disabled={
                                info.state !== 'INIT' ||
                                !hasAuth(['配送特价', '编辑'])
                              }
                            />
                          </XlbBasicForm.Item>

                          <XlbBasicForm.Item label="单据号" name="fid">
                            <XlbInput
                              size="small"
                              style={{ width: '180px' }}
                              disabled
                            />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item label="开始时段" name="start_hour">
                            <XlbTimePicker
                              style={{ width: 180 }}
                              defaultValue={dayjs('00:00', 'HH:mm')}
                              allowClear={false}
                              format={'HH:mm'}
                              disabled={
                                info.state !== 'INIT' ||
                                !hasAuth(['配送特价', '编辑'])
                              }
                            />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item label="结束时段" name="end_hour">
                            <XlbTimePicker
                              allowClear={false}
                              style={{ width: 180 }}
                              format={'HH:mm'}
                              defaultValue={dayjs('23:59', 'HH:mm')}
                              disabled={
                                info.state !== 'INIT' ||
                                !hasAuth(['配送特价', '编辑'])
                              }
                            />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="商品部门"
                            name="item_dept_names"
                          >
                            <XlbInput
                              disabled
                              size="small"
                              style={{ width: '180px' }}
                            />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="特价模式"
                            name="type"
                            initialValue={'DELIVERY_OUT'}
                          >
                            <XlbSelect
                              style={{ width: 180 }}
                              disabled={
                                info.state !== 'INIT' ||
                                rowData?.some((v: any) => v?.item_code)
                              }
                            >
                              <Option
                                key={'DELIVERY_OUT'}
                                value={'DELIVERY_OUT'}
                              >
                                调出特价
                              </Option>
                              <Option key={'REQUEST'} value={'REQUEST'}>
                                补货特价
                              </Option>
                            </XlbSelect>
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item label="留言备注" name="memo">
                            <XlbInput
                              style={{ width: 508 }}
                              disabled={
                                !hasAuth(['配送特价', '编辑']) ||
                                info.state !== 'INIT'
                              }
                            />
                          </XlbBasicForm.Item>
                        </div>
                      </div>
                      {info?.state && (
                        <div
                          style={{
                            width: '150px',
                            flexBasis: '150px',
                            display: 'flex',
                            justifyContent: 'center',
                          }}
                        >
                          <img
                            src={orderStatusIcons[info?.state]}
                            width={86}
                            height={78}
                          />
                        </div>
                      )}
                    </div>
                  ),
                },
                {
                  label: '其他信息',
                  key: 'otherInfo',
                  children: (
                    <>
                      <div className="row-flex" style={{ marginTop: 12 }}>
                        <XlbBasicForm.Item label="制单人" name="create_by">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="制单时间" name="create_time">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="审核人" name="audit_by">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="审核时间" name="audit_time">
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                      </div>
                      <div className="row-flex">
                        <XlbBasicForm.Item label={'修改人'} name={'update_by'}>
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item
                          label={'修改时间'}
                          name={'update_time'}
                        >
                          <XlbInput style={{ width: '180px' }} disabled />
                        </XlbBasicForm.Item>
                      </div>
                    </>
                  ),
                },
              ]}
            />
          </XlbBasicForm>
        )}

        {/*<div*/}
        {/*  style={{*/}
        {/*    display: 'flex',*/}
        {/*    flexDirection: 'column',*/}
        {/*    height: 'calc(100% - 300px)',*/}
        {/*  }}*/}
        {/*>*/}
        <div
          style={{
            marginBottom: 12,
            paddingLeft: '16px',
            paddingRight: '16px',
          }}
        >
          <XlbButton.Group>
            {hasAuth(['配送特价', '编辑']) && (
              <XlbButton
                label="批量添加"
                type="primary"
                disabled={
                  info.state !== 'INIT' ||
                  !form.getFieldValue('store_id')
                }
                onClick={async () => {
                  // handleDialogClick('item')
                  const list = await XlbBasicData({
                    type: 'goods',
                    url: '/erp/hxl.erp.deliveryspecialprice.item.page',
                    isMultiple: true,
                    dataType: 'lists',
                    primaryKey: 'id',
                    resetForm: true,
                    nullable: false,
                    data: {
                      store_id: form.getFieldValue('store_id'),
                      supply_store_ids: form.getFieldValue('supply_store_ids'),
                      storeStatus: true,
                    },
                  });
                  if (!list) return;
                  if (Array.isArray(list)) {
                    confirmAdd(list, 'item');
                  }
                }}
                icon={<XlbIcon size={16} name="jia" />}
              />
            )}
            {hasAuth(['配送特价', '编辑']) && (
              <XlbButton
                label="批量设置"
                type="primary"
                disabled={
                  info.state !== 'INIT' ||
                  rowData?.length === 0 ||
                  !hasAuth(['配送特价', '编辑'])
                }
                onClick={() => setBatchVisible(true)}
                icon={<XlbIcon size={16} name="piliang" />}
              />
            )}
            {hasAuth(['配送特价', '导入']) && (
              <XlbButton
                label="导入"
                type="primary"
                disabled={
                  // !fid ||
                  // !form.getFieldValue('store_id') ||
                  // !form.getFieldValue('supply_store_ids')
                  info.state !== 'INIT' || !form.getFieldValue('store_id')
                }
                onClick={async () => {
                  const res = await XlbImportModal({
                    templateUrl:
                      process.env.BASE_URL +
                      '/erp/hxl.erp.deliveryspecialprice.template.download',
                    importUrl:
                      process.env.BASE_URL +
                      '/erp/hxl.erp.deliveryspecialprice.itemDeliveryImport',
                    title: '配送特价商品导入',
                  });
                  if (res.code !== 0) return;
                  if (res?.data?.details && res?.data?.details?.length) {
                    confirmAdd(res?.data?.details, 'import');
                  }
                }}
                icon={<XlbIcon size={16} name="daoru" />}
              />
            )}
          </XlbButton.Group>
        </div>
        <XlbShortTable
          isLoading={isLoading}
          showSearch
          key={activeKey}
          style={{ flex: 1, margin: '0 16px' }}
          url={'/erp/hxl.erp.deliveryspecialprice.item.page'}
          data={{
            store_id: form.getFieldValue('store_id'),
          }}
          onChangeData={(data) => {
            const initialValues = Object.freeze({
              initial_quantity: 0,
              activity_quantity: 0,
              remain_quantity: 0,
              value: 0,
              memo: null,
              special_price_type: 'FIXED_MONEY',
            });
            const originIds = rowData.map((v) => v.item_id);
            const getInitials = (v: any) =>
              originIds.includes(v.item_id) ? {} : initialValues;
            setRowData(
              data.map((v, index) => ({
                ...v,
                key: index + '_' + v?.id || v?.item_id,
                item_id: v?.id || v?.item_id,
                item_code: v.code || v?.item_code,
                item_bar_code: v.bar_code || v?.item_bar_code,
                item_name: v.name || v.item_name,
                item_spec: v.purchase_spec || v.item_spec,
                unit: v.delivery_unit || v.unit,
                ratio: v.delivery_ratio || v.ratio,
                basic_unit: v.unit,
                special_price:
                  v.special_price || v.special_price === 0
                    ? v.special_price
                    : v.price,
                // memo: null,
                // initial_quantity: 0,
                // activity_quantity: 0,
                // remain_quantity: 0,
                // special_price_type: 'FIXED_MONEY',
                // value: 0,
                supplier_details: v?.supplier_details,
                supplier_id: v?.supplier_id,
                supplier_name: v?.supplier_name,
                ...getInitials(v),
              })),
            );
          }}
          disabled={info.state !== 'INIT' || !hasAuth(['配送特价', '编辑'])}
          columns={itemArrdetail?.map((v) => tableRender(v))}
          dataSource={rowData}
          total={rowData?.length}
          selectMode="single"
          primaryKey="key"
          // popoverPrimaryKey="id"
        />
        {/*</div>*/}
      </XlbPageContainer>
    </div>
    // </XlbConfigProvider.Provider>
  );
};

export default DeliverySpecialPriceItem;
