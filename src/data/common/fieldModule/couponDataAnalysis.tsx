export const CouponDataAnalysisKeyMap = {
  // 消费券分类
  couponTypes: 'couponTypes',
  // 消费券
  coupon: 'coupon',
  erpStoreIdsMoney: 'erpStoreIdsMoney'
}

export const couponDataAnalysisConfig: any[] = [
  {
    tag: 'ERP',
    componentType: 'select',
    id: CouponDataAnalysisKeyMap.couponTypes,
    name: 'coupon_category_ids',
    label: '消费券分类',
    fieldProps: {
      mode: 'multiple'
    },
    dependencies: ['summary_types'],
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(anybaseURL + '/erp/hxl.erp.couponcategory.find', {})
      if (res.code == 0) {
        return res.data
          ?.filter((v: any) => v.id)
          ?.map((item: any) => {
            return {
              label: item.name,
              value: item.id
            }
          })
      }
      return []
    }
  },
  {
    tag: 'ERP',
    label: '消费券',
    id: CouponDataAnalysisKeyMap?.coupon,
    name: 'coupon_type_ids',
    dependencies: ['summary_types'],
    fieldProps: {
      dialogParams: {
        isLeftColumn: false,
        type: 'coupon',
        dataType: 'lists',
        isMultiple: true,
        initialValues: {
          status: 'true'
        }
      }
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '门店',
    id: CouponDataAnalysisKeyMap?.erpStoreIdsMoney,
    name: 'store_ids',
    fieldProps: {
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isMultiple: true
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name'
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  }
]
