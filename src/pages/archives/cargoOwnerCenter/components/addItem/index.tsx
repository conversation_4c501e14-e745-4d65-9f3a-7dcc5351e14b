import type { BaseModalProps } from '@xlb/components';
import { XlbBasicForm, XlbInputDialog, XlbModal } from '@xlb/components';
import { type FC } from 'react';

import NiceModal from '@ebay/nice-modal-react';
import { message } from 'antd';
import Api from '../../server';

interface Props extends Pick<BaseModalProps, 'title'> {
  fetchData?: any;
  type?: string;
}

const AddItem: FC<Props> = ({ fetchData = () => {} }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();

  const handleOk = async (values: any) => {
    // console.log(values, 'handleOk');
    const { store_id, cargo_owner_ids } = values;
    const params = {
      store_id: store_id?.[0] || undefined,
      cargo_owner_ids,
    };
    const res = await Api.addInfo(params);
    if (res?.code === 0) {
      form.resetFields();
      modal.hide();
      fetchData();
      message.success('操作成功');
    }
  };

  return (
    <XlbModal
      width={450}
      open={modal.visible}
      title={'配送中心货主配置'}
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        form.resetFields();
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div style={{ marginLeft: '12px' }}>
        <XlbBasicForm
          form={form}
          style={{ margin: '20px 0' }}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          <XlbBasicForm.Item
            name="store_id"
            label="门店名称"
            rules={[{ required: true, message: '请选择门店' }]}
          >
            <XlbInputDialog
              dialogParams={{
                type: 'store',
                data: {
                  center_flag: true,
                  enable_organization: false,
                },
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'store_name',
              }}
              width={180}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            name="cargo_owner_ids"
            label="货主范围"
            rules={[{ required: true, message: '请选择货主范围' }]}
          >
            <XlbInputDialog
              width={180}
              placeholder="请选择货主范围"
              dialogParams={{
                type: 'cargoOwner',
                isMultiple: true,
                isLeftColumn: false,
                data: { status: 'ENABLED' },
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: 'source_name',
              }}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
export default NiceModal.create(AddItem);
