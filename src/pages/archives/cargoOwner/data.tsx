export const cargoList: any[] = [
  {
    label: '组织',
    value: 'ORGANIZATION',
  },
  {
    label: '供应商',
    value: 'SUPPLIER',
  },
];

export const statusList: any[] = [
  { label: '启用', value: 'ENABLED' },
  { label: '停用', value: 'DISABLED' },
];
export const formList: any[] = [
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    allowClear: true,
    check: true,
  },
  {
    label: '货主类型',
    name: 'owner_type',
    type: 'select',
    check: true,
    // multiple: true,
    allowClear: true,
    options: cargoList,
  },
  {
    label: '状态',
    name: 'status_list',
    type: 'select',
    check: true,
    multiple: true, // DISABLED
    allowClear: true,
    options: statusList,
  },
];

export const tableList: any[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '货主代码',
    code: 'owner_code',
    align: 'left',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '组织代码/供应商代码',
    code: 'source_code',
    align: 'left',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '组织代码/供应商名称',
    code: 'source_name',
    align: 'left',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '货主类型',
    code: 'owner_type',
    align: 'left',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '状态',
    code: 'status',
    align: 'left',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '创建人',
    code: 'create_by',
    align: 'left',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '创建时间',
    code: 'create_time',
    align: 'left',
    width: 162,
    features: { sortable: true, format: 'TIME' },
  },
];
