import { XlbFetch as ErpRequest } from '@xlb/utils';

const urlInfoList: any = {
  getorderstate: '/erp/hxl.erp.purchasereport.purchasemonitor.orderstate.page',
  getOrderarrivalrate:
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderarrivalrate.page',
  getExpireorder:
    '/erp/hxl.erp.purchasereport.purchasemonitor.expireorder.page',
  getOrderdetail:
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderdetail.page',
  getPrepareOrder: '/scm/hxl.scm.prepareorder.purchaseandshippingorder',
};

//下载中心下载
const commonExport = async (url: string, data: any) => {
  return await ErpRequest.post(url, data);
};

// 订单状态
const getorderstate = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderstate.page',
    data,
  );
};

// 订单到货率
const getOrderarrivalrate = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderarrivalrate.page',
    data,
  );
};

// 过期订单
const getExpireorder = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.expireorder.page',
    data,
  );
};

// 订单商品汇总
const getOrderdetail = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderdetail.page',
    data,
  );
};

// 待交货订单明细
const getPrepareOrder = async (data: any) => {
  return await ErpRequest.post(
    '/scm/hxl.scm.prepareorder.purchaseandshippingorder',
    data,
  );
};

// 订单状态导出
const orderstateExport = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderstate.export',
    data,
  );
};
// 订单到货率导出
const orderarrivalrateExport = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderarrivalrate.export',
    data,
  );
};
// 过期订单导出
const expireorderExport = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.expireorder.export',
    data,
  );
};

// 订单商品汇总导出
const orderdetailExport = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereport.purchasemonitor.orderdetail.export',
    data,
  );
};

// 待交货订单明细导出
const prepareorderExport = async (data: any) => {
  return await ErpRequest.post(
    '/scm/hxl.scm.prepareorder.purchaseandshippingorder.export',
    data,
  );
};

export default {
  getorderstate,
  getOrderarrivalrate,
  getExpireorder,
  orderstateExport,
  orderarrivalrateExport,
  expireorderExport,
  getOrderdetail,
  orderdetailExport,
  getPrepareOrder,
  prepareorderExport,
  urlInfoList,
  commonExport,
};
