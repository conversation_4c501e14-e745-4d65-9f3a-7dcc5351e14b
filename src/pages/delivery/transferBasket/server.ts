import { XlbFetch as ErpRequest } from '@xlb/utils';
import { transferBasketReqDTO } from './transferBasket.d';

export default {
  // 删除
  delete: async (data: { id: number }) => {
    return await ErpRequest.post('/erp/hxl.erp.basket.delete', data);
  },
  // 新增
  save: async (data: transferBasketReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.basket.save', data);
  },
  // 更新
  update: async (data: transferBasketReqDTO) => {
    return await ErpRequest.post('/erp/hxl.erp.basket.update', data);
  },
};
