import { XlbFetch as ErpRequest } from '@xlb/utils';

export const Api = {
  /**
   * @name 新增/修改公司抬头
   */
  addOrUpdateCompanyHeader: async (data: any) =>
    await ErpRequest.post('/erp-mdm/hxl.erp.companyinvoice.save', { data }),
  /**
   * @name 删除公司抬头
   */
  deleteCompanyHeader: async (data: any) =>
    await ErpRequest.post('/erp-mdm/hxl.erp.companyinvoice.delete', { data }),
  /**
   * @name 导出公司抬头
   */
  companyHeaderExport: async (data: any) =>
    await ErpRequest.post('/erp-mdm/hxl.erp.companyinvoice.export', { data }),
};
