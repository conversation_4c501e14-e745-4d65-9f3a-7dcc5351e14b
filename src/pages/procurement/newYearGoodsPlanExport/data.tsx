import type { XlbTableColumnProps } from '@xlb/components'
import type { PurchasePlanReportResDTO } from './type'

export const baseColumns: XlbTableColumnProps<PurchasePlanReportResDTO>[] = [
  {
    code: 'purchaser1',
    name: '采购计划',
    align: 'center',
    children: [
      {
        code: 'plan_quantity',
        name: '采购计划',
        features: { sortable: true, format: 'QUANTITY', tips: '采购计划数量' },
        align: 'right',
        width: 160
      },
      {
        code: 'plan_money',
        name: '金额（元）',
        features: { sortable: true, format: 'MONEY', tips: '采购计划金额' },
        align: 'right',
        width: 160
      },
      {
        code: 'purchase_quantity',
        name: '采购数量',
        features: { sortable: true, format: 'QUANTITY', tips: '实际订购数量' },
        align: 'right',
        width: 160
      },
      {
        code: 'purchase_money',
        name: '采购金额（元）',
        features: { sortable: true, format: 'MONEY', tips: '实际订购金额' },
        align: 'right',
        width: 160
      },
      {
        code: 'purchase_diff_quantity',
        name: '采购差异量',
        features: { sortable: true, format: 'QUANTITY', tips: '采购计划-采购数量' },
        align: 'right',
        width: 160
      },
      {
        code: 'purchase_diff_money',
        name: '采购差异额（元）',
        features: { sortable: true, format: 'MONEY', tips: '金额(元)-采购金额(元)' },
        align: 'right',
        width: 160
      },
      {
        code: 'purchase_complete_rate_str',
        name: '计划达成率',
        features: { sortable: true, tips: '采购数量/采购计划' },
        align: 'center',
        width: 160
      },
      {
        name: '仓库缺货数量',
        code: 'over_stock_quantity',
        features: { sortable: true, tips: '要货未完成数量-库存数量-在定量' },
        align: 'right',
        width: 160
      }
    ]
  },
  {
    code: 'receive',
    name: '收货信息',
    align: 'center',
    children: [
      {
        code: 'receive_quantity',
        name: '到货数量',
        features: { sortable: true, format: 'QUANTITY', tips: '仓库实际收货数量' },
        align: 'right',
        width: 160
      },
      {
        code: 'receive_money',
        name: '到货金额（元）',
        features: { sortable: true, format: 'MONEY', tips: '仓库实际收货金额' },
        align: 'right',
        width: 160
      },
      {
        code: 'not_receive_quantity',
        name: '未到货量',
        features: { sortable: true, format: 'QUANTITY', tips: '采购数量-到货数量' },
        align: 'right',
        width: 160
      },
      {
        code: 'not_receive_money',
        name: '未到货金额（元）',
        features: { sortable: true, format: 'MONEY', tips: '采购金额(元)-到货金额(元)' },
        align: 'right',
        width: 160
      },
      {
        code: 'receive_complete_rate_str',
        name: '到货率',
        features: { sortable: true, tips: '到货数量/采购数量' },
        align: 'center',
        width: 160
      }
    ]
  },
  {
    code: 'request',
    name: '门店要货信息',
    align: 'center',
    children: [
      {
        code: 'request_quantity',
        name: '要货数量',
        features: {
          sortable: true,
          format: 'QUANTITY',
          tips: '门店向配送中心实际补货数量（包含常规+备货）'
        },
        align: 'right',
        width: 160
      },
      {
        code: 'request_money',
        name: '要货金额（元）',
        features: {
          sortable: true,
          format: 'MONEY',
          tips: '门店向配送中心实际补货金额（包含常规+备货）'
        },
        align: 'right',
        width: 160
      },
      {
        code: 'request_complete_quantity',
        name: '要货完成量',
        features: {
          sortable: true,
          format: 'QUANTITY',
          tips: '针对门店的补货数量，配送中心实际送货到门店数量'
        },
        align: 'right',
        width: 160
      },
      {
        code: 'request_complete_money',
        name: '要货完成金额（元）',
        features: {
          sortable: true,
          format: 'MONEY',
          tips: '针对门店的补货数量，配送中心实际送货到门店金额'
        },
        align: 'right',
        width: 200
      },
      {
        code: 'request_not_complete_quantity',
        name: '要货未完成数量',
        features: { sortable: true, format: 'QUANTITY', tips: '要货数量-要货完成数量' },
        align: 'right',
        width: 200
      },
      {
        code: 'request_not_complete_money',
        name: '要货未完成金额（元）',
        features: { sortable: true, format: 'MONEY', tips: '要货金额(元)-要货完成金额(元)' },
        align: 'right',
        width: 200
      },
      {
        code: 'request_complete_rate_str',
        name: '要货完成率',
        features: { sortable: true, tips: '要货完成数量/要货数量' },
        align: 'center',
        width: 160
      }
    ]
  }
]
