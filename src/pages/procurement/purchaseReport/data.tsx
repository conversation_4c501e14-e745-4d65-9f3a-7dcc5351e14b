import { columnWidthEnum } from '@/data/common/constant';
import { SelectType } from '@xlb/components';

//时间类型
export const timeType: SelectType[] = [
  {
    label: '审核时间',
    value: 'audit_date',
  },
  {
    label: '制单时间',
    value: 'create_date',
  },
  {
    label: '收退货时间',
    value: 'order_operate_date',
  },
];

export const UnitType = [
  {
    label: '单据单位',
    value: 'ORDER',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
];

export const categoryLevels = [
  {
    label: '按一级类别',
    value: 1,
  },
  {
    label: '按二级类别',
    value: 2,
  },
  {
    label: '按三级类别',
    value: 3,
  },
];

// 根据汇总条件
export const TableEnumBySummary = [
  { label: 'store_name', value: 'STORE' },
  { label: 'cargo_owner_name', value: 'STORE' },
  { label: 'business_area', value: 'BUSINESS_AREA' },
  { label: 'administrative', value: 'PROVINCE' },
  { label: 'administrative', value: 'CITY' },
  { label: 'administrative', value: 'DISTRICT' },
];

export const summartOptions = [
  {
    label: '门店',
    value: 'STORE',
  },
  {
    label: '业务区域',
    value: 'BUSINESS_AREA',
  },
  {
    label: '省',
    value: 'PROVINCE',
  },
  {
    label: '市',
    value: 'CITY',
  },
  {
    label: '区',
    value: 'DISTRICT',
  },
];

const summaryItemList = [
  {
    name: '货主',
    code: 'cargo_owner_id',
    width: 120,
    features: { sortable: true },
    align: 'left',
    hidden: false,
  },
  {
    name: '业务区域',
    code: 'business_area',
    width: 200,
    features: { sortable: false },
    hidden: true,
  },
  {
    name: '行政区域',
    code: 'administrative',
    width: 200,
    features: { sortable: false },
    hidden: true,
  },
];
export const PURCHASE_TYPE_LIST = [
  {
    label: '集采品',
    value: 'COLLECTIVE_PURCHASE',
  },
  {
    label: '集售品',
    value: 'COLLECTIVE_SALE',
  },
  {
    label: '地采品',
    value: 'GROUND_PURCHASE',
  },
  {
    label: '店采品',
    value: 'SHOP_PURCHASE',
  },
];

export const formList = (
  enable_organization: boolean,
  enable_cargo_owner: boolean,
) => [
  {
    label: '时间类型',
    name: 'timeClass',
    type: 'select',
    allowClear: false,
    check: true,
    multiply: false,
    options: timeType,
  },
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },

  {
    label: '组织',
    name: 'org_ids',
    type: 'select',
    allowClear: true,
    multiple: true,
    check: true,
    options: [],
    hidden: !enable_organization,
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.org.find',
      responseTrans: {
        label: 'name',
        value: 'id',
      },
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    check: true,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
    },

    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '仓库',
    name: 'storehouse_ids',
    type: 'select',
    multiple: true,
    dependencies: ['store_ids'],
    dropdownStyle: { width: 200 },
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return [defaultStoreHouse?.value];
    },
    // @ts-ignore
    selectRequestParams: (params: any, form) => {
      form?.setFieldsValue({
        storehouse_ids: null,
      });
      if (params?.store_ids?.length == 1) {
        return {
          url: '/erp-mdm/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data: any) {
            const options = data
              .filter((v: any) => v.distribution)
              .map((item: any) => ({
                label: item.name,
                value: item.id,
                default_flag: item.default_flag,
              }));
            return options;
          },
        };
      }
    },
  },
  {
    label: '货主',
    name: 'cargo_owner_ids',
    type: 'inputDialog',
    allowClear: true,
    hidden: !enable_cargo_owner,
    dialogParams: {
      type: 'cargoOwner',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      data: {
        owner_type: 'ORGANIZATION',
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'source_name',
    } as any,
  },
  {
    label: '供应商',
    check: true,
    name: 'supplier_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,

    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
  {
    label: '商品类别',
    name: 'category_ids',
    type: 'inputDialog',
    allowClear: true,
    check: true,
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
  },
  {
    label: '类别等级',
    name: 'category_level',
    type: 'select',
    allowClear: false,
    initialValue: 1,
    options: categoryLevels,
  },
  {
    label: '单据号',
    name: 'fid',
    type: 'input',
    allowClear: true,
    check: true,
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    allowClear: false,
    initialValue: 'ORDER',
    options: UnitType,
  },
  // {
  //   label: '类别等级',
  //   value: 'top_category',
  //   type: 'select',
  //   clear: false,
  //   initialValue: 0,
  //   hidden: true,
  //   options: [
  //     { label: '按当前类别', value: 0 },
  //     { label: '按顶级类别', value: 1 }
  //   ]
  // },
  {
    label: '业财核算分类',
    name: 'finance_code',
    type: 'select',
    disabled: false,
    allowClear: true,
    options: [],
  },
  {
    label: '查询条件',
    name: 'condition',
    type: 'select',
    disabled: false,
    hidden: true,
    allowClear: false,
    options: [
      {
        label: '汇总',
        value: 'SUMMARY',
      },
      {
        label: '月度',
        value: 'MONTHLY',
      },
    ],
  },
  {
    label: '汇总条件',
    name: 'summary_conditions',
    type: 'select',
    allowClear: false,
    options: summartOptions,
    hidden: true,
    rules: [{ required: true, message: '最少选择一项汇总条件' }],
    initialValue: ['STORE'],
    multiple: true,
  },
  {
    label: '业务区域',
    name: 'business_area_id',
    type: 'inputDialog',
    allowClear: true,
    check: true,
    hidden: true,
    dialogParams: {
      type: 'businessArea',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      initAfterPost: (data: any) => {
        const dataValueDetails = data?.value_details?.map((v) => {
          return {
            ...v,
            show_name:
              v.six_name ||
              v.five_name ||
              v.four_name ||
              v.third_name ||
              v.second_name ||
              v.first_name,
          };
        });
        return {
          list: dataValueDetails,
          total: data?.value_details?.length,
        };
      },

      handleDefaultValue: (data) => {
        console.log('🚀 ~ UserManageItem ~ data:', data);
        const _data = data.map((v) => {
          return {
            ...v,
            id: v.id,
            show_name:
              v.six_name ||
              v.five_name ||
              v.four_name ||
              v.third_name ||
              v.second_name ||
              v.first_name,
          };
        });
        return _data;
      },
    },
    getValueFromEvent: (e: any, arr) => {
      // console.log(e, 'getValueFromEvent', arr)
      let arrT: any[] = [];
      arr.forEach((h: any) => {
        arrT = [...arrT, ...h?.ids];
      });
      return arrT;
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'show_name',
    },
  },
  {
    label: '行政区域',
    name: 'city_codes',
    type: 'inputDialog',
    allowClear: true,
    check: true,
    hidden: true,
    treeModalConfig: {
      zIndex: 2002,
      title: '选择区域', // 标题
      url: '/erp-mdm/hxl.erp.store.area.find.all', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'code',
      // data: {
      //   enabled: true
      // },
      // params: {
      //   company_id: LStorage.get('userInfo')?.company_id,
      //   // levels: [0, 1]
      // },
      // requestParams: {
      //   levels: [0, 1]
      // },
      fieldName: {
        id: 'code',
        parent_id: 'parent_code',
      },
      width: 360, // 模态框宽度
    },
    fieldNames: {
      // @ts-ignore
      idKey: 'code',
      nameKey: 'name',
    },
  },
  {
    label: '采购类型',
    name: 'purchase_types',
    type: 'select',
    allowClear: true,
    multiple: true,
    options: PURCHASE_TYPE_LIST,
  },
];
const purchaseTypeList = [
  {
    name: '采购类型',
    code: 'purchase_type',
    width: 120,
    features: { sortable: true },
  },
];

export const receiveTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
  },
  {
    name: '收货组织',
    code: 'org_name',
    width: 140,
    lock: true,
    hidden: true,
  },
  {
    name: '收货门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '货主',
    code: 'cargo_owner_id',
    width: 120,
    features: { sortable: true },
    align: 'left',
    hidden: false,
  },
  {
    name: '操作时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
  },
  {
    name: '收货日期',
    code: 'operate_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '品牌',
    code: 'item_brand_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  ...purchaseTypeList,
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '类别等级',
    code: 'category_level',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 180,
    // features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '单据单位',
    code: 'unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货单价',
    code: 'price',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货单价(去税)',
    code: 'no_tax_price',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '收货金额',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },

  {
    name: '赠品单位',
    code: 'present_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'present_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额(去税)',
    code: 'no_tax_present_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
  },
  {
    name: '商品部门',
    code: 'item_dept_name',
    width: 160,
    features: { sortable: false },
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
];

export const returnTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
  },
  {
    name: '退货组织',
    code: 'org_name',
    width: 140,
    lock: true,
    hidden: true,
  },
  {
    name: '退货门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '退货仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '货主',
    code: 'cargo_owner_id',
    width: 120,
    features: { sortable: true },
    align: 'left',
    hidden: false,
  },
  {
    name: '操作时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
  },
  {
    name: '退货日期',
    code: 'operate_date',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '品牌',
    code: 'item_brand_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  ...purchaseTypeList,
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '类别等级',
    code: 'category_level',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 180,
    // features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '单据单位',
    code: 'unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货单价',
    code: 'price',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货单价(去税)',
    code: 'no_tax_price',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '赠品单位',
    code: 'present_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'present_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额(去税)',
    code: 'no_tax_present_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
  },
  {
    name: '商品部门',
    code: 'item_dept_name',
    width: 160,
    features: { sortable: false },
  },
];

export const storeItemTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    lock: true,
    hidden: true,
  },
  ...summaryItemList,
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  ...purchaseTypeList,
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '类别等级',
    code: 'category_level',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 180,
    // features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '收货基本数量',
    code: 'receive_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'receive_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额',
    code: 'receive_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额(去税)',
    code: 'receive_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'receive_basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'receive_present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'receive_present_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额(去税)',
    code: 'receive_no_tax_present_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货基本数量',
    code: 'return_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'return_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'return_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额(去税)',
    code: 'return_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货成本',
    code: 'return_cost_money',
    width: 100,
    features: { sortable: true },
  },

  {
    name: '退货成本（去税）',
    code: 'return_no_tax_cost_money',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '数量小计',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '商品部门',
    code: 'item_dept_name',
    width: 160,
    features: { sortable: false },
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
];

export const supplierTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '货主',
    code: 'cargo_owner_id',
    width: 120,
    features: { sortable: true },
    align: 'left',
    hidden: false,
  },
  {
    name: '营业执照名称',
    code: 'letterhead',
    width: 150,
  },
  ...purchaseTypeList,
  {
    name: '收货基本数量',
    code: 'receive_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'receive_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额',
    code: 'receive_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额(去税)',
    code: 'receive_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'receive_basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收退赠品数量',
    code: 'receive_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收退赠品金额',
    code: 'receive_present_money',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收退赠品金额（去税）',
    code: 'receive_no_tax_present_money',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '退货基本数量',
    code: 'return_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'return_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'return_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额(去税)',
    code: 'return_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货成本',
    code: 'return_cost_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货成本（去税）',
    code: 'return_no_tax_cost_money',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '数量小计',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '月份',
    code: 'month',
    width: 100,
    hidden: true,
    features: { sortable: true },
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
];

export const categoryTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    lock: true,
    hidden: true,
  },

  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  ...summaryItemList,
  ...purchaseTypeList,
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '类别等级',
    code: 'category_level',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 180,
    // features: { sortable: true }
  },
  {
    name: '收货基本数量',
    code: 'receive_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'receive_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额',
    code: 'receive_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额(去税)',
    code: 'receive_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'receive_basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'receive_present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'receive_present_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额(去税)',
    code: 'receive_no_tax_present_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货基本数量',
    code: 'return_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'return_quantity',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'return_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额(去税)',
    code: 'return_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货成本',
    code: 'return_cost_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货成本（去税）',
    code: 'return_no_tax_cost_money',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '数量小计',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
];

export const supplierStoreTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    lock: true,
    hidden: true,
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  ...summaryItemList,
  ...purchaseTypeList,
  {
    name: '收货基本数量',
    code: 'receive_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'receive_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额',
    code: 'receive_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额(去税)',
    code: 'receive_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'receive_basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'receive_present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'receive_present_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额(去税)',
    code: 'receive_no_tax_present_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货基本数量',
    code: 'return_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'return_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'return_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额(去税)',
    code: 'return_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货成本',
    code: 'return_cost_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货成本（去税）',
    code: 'return_no_tax_cost_money',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '数量小计',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
];

export const supplierItemTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
    features: { sortable: true },
  },

  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '货主',
    code: 'cargo_owner_id',
    width: 120,
    features: { sortable: true },
    align: 'left',
    hidden: false,
  },
  {
    name: '品牌',
    code: 'item_brand_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  ...purchaseTypeList,
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '类别等级',
    code: 'category_level',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 180,
    // features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '收货基本数量',
    code: 'receive_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'receive_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额',
    code: 'receive_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额(去税)',
    code: 'receive_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'receive_basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'receive_present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'receive_present_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额(去税)',
    code: 'receive_no_tax_present_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货基本数量',
    code: 'return_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'return_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'return_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额(去税)',
    code: 'return_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货成本',
    code: 'return_cost_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '数量小计',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
];

export const supplierStoreItemTableColumn = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    lock: true,
    hidden: true,
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  ...summaryItemList,
  {
    name: '品牌',
    code: 'item_brand_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true },
  },
  ...purchaseTypeList,
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '类别等级',
    code: 'category_level',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '业财核算分类',
    code: 'finance_name',
    width: 180,
    // features: { sortable: true }
  },

  {
    name: '基本单位',
    code: 'basic_unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'unit',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '收货基本数量',
    code: 'receive_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货数量',
    code: 'receive_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额',
    code: 'receive_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '收货金额(去税)',
    code: 'receive_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'receive_basic_present_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'receive_present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'receive_present_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品金额(去税)',
    code: 'receive_no_tax_present_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货基本数量',
    code: 'return_basic_quantity',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '退货数量',
    code: 'return_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额',
    code: 'return_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '退货金额(去税)',
    code: 'return_no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '退货成本',
    code: 'return_cost_money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '数量小计',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计',
    code: 'money',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '金额小计(去税)',
    code: 'no_tax_money',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '运费',
    code: 'supplier_money',
    width: 160,
    align: 'right',
    features: { sortable: true },
  },
];
