import NiceModal from '@ebay/nice-modal-react';
import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbDatePicker,
  XlbModal,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import type { SelectProps } from 'antd';
import { Checkbox, Tooltip } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import style from './index.less';
interface Props extends Pick<BaseModalProps, 'title'> {
  /**
   * 请求数据方法
   */
  fetchData?: Function;
  /**
   * data中包含的id列表
   */
  idsList?: string[];
  /**
   * 请求地址
   */
  url: string;
  /**
   * 单据id key
   */
  idKey?: string | number;
  /**
   * 日期key
   */
  timeKey: string | number;
  /**
   * 单据类型
   */
  type: string;
  /**
   * 弹窗title
   */
  title?: string;
}

interface ItemProps {
  label: string;
  value: string;
}

export const BatchChangeTimeModal = NiceModal.create<Props>(
  ({
    url,
    fetchData,
    idsList = [],
    idKey = 'fid_list',
    timeKey = 'chage_date',
    title = '批量修改',
    type,
  }) => {
    const [form] = XlbBasicForm.useForm();
    const modal = NiceModal.useModal();
    const [options, setOptions] = useState<ItemProps[]>([]);
    const formatChangeDate = (date) => {
      if (!date) return null;
      return date.includes(' ') ? date : `${date} 00:00:00`;
    };
    const handleOk = async (values: any) => {
      if (
        !values?.check_value?.includes('in_date_check') &&
        !values?.change_date
      ) {
        await XlbTipsModal({
          tips: '请选择修改日期',
        });
        return;
      }
      const data = {
        [idKey]: values?.fids?.map((item: any) => item.value),
        [timeKey]: formatChangeDate(values?.change_date),
        order_date_change_type: type,
      };
      await XlbFetch.post(process.env.BASE_URL + url, data);
      fetchData?.();
      modal.hide();
    };

    useEffect(() => {
      const newOptions: ItemProps[] = idsList?.map((id) => ({
        label: id,
        value: id,
      }));
      setOptions(newOptions);
      form.setFieldsValue({ fids: newOptions });
    }, [idsList, modal.visible]);

    const sharedProps: SelectProps = {
      mode: 'multiple',
      style: { width: 240 },
      placeholder: '',
      maxTagCount: 1,
    };
    useEffect(() => {
      if (!modal.visible) {
        form.resetFields();
      }
    }, [modal.visible]);

    return (
      <XlbModal
        width={450}
        open={modal.visible}
        title={title}
        isCancel={true}
        onOk={async () => {
          const values = form.getFieldsValue(true);
          console.log('🚀 ~ onOk={ ~ values:', values);
          handleOk(values);
        }}
        onCancel={() => {
          modal.resolve(false);
          modal.hide();
        }}
      >
        <XlbBasicForm form={form} style={{ margin: '20px 0' }}>
          <div className={style.box}>
            <p className={style.title}>修改范围</p>
            <XlbBasicForm.Item
              label="单据号"
              name="fids"
              style={{ marginTop: 15 }}
            >
              <XlbSelect
                {...sharedProps}
                style={{ marginLeft: 10 }}
                options={options}
                disabled
                maxTagPlaceholder={(omittedValues) => (
                  <Tooltip
                    title={omittedValues.map(({ label }) => label).join(', ')}
                  >
                    <span>+{idsList?.length - 1}</span>
                  </Tooltip>
                )}
              />
            </XlbBasicForm.Item>
          </div>
          <div className={style.box} style={{ marginBottom: 0 }}>
            <p className={style.title}>修改内容</p>
            <XlbBasicForm.Item name="check_value" style={{ marginTop: 15 }}>
              <Checkbox.Group
                style={{ width: '100%', display: 'flex', alignItems: 'center' }}
              >
                <Checkbox
                  value={'in_date_check'}
                  style={{
                    width: '90px',
                    margin: '0 6px 12px 28px',
                  }}
                >
                  <span style={{ whiteSpace: 'nowrap' }}>修改日期:</span>
                </Checkbox>
                <XlbBasicForm.Item dependencies={['check_value']} noStyle>
                  {({ getFieldValue }) => {
                    return (
                      <XlbBasicForm.Item name={'change_date'}>
                        <XlbDatePicker
                          showTime={true}
                          format={'YYYY-MM-DD HH:mm:ss'}
                          style={{ width: 240 }}
                          allowClear={true}
                          disabledDate={(current) =>
                            current && current > moment().endOf('day')
                          }
                        />
                      </XlbBasicForm.Item>
                    );
                  }}
                </XlbBasicForm.Item>
              </Checkbox.Group>
            </XlbBasicForm.Item>
          </div>
        </XlbBasicForm>
      </XlbModal>
    );
  },
);
