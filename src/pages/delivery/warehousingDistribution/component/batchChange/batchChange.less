.box {
  position: relative;
  //width: 860px;
  margin: 25px auto;
  padding: 20px 10px 10px 10px;
  border: 1px solid #d8d8d8;
  border-radius: 5px;

  .title {
    position: absolute;
    top: -18px;
    left: 20px;
    padding: 0 13px;
    background: white;
  }

  //:global .ant-form-item {
  //  display: inline-block;
  //  //margin: 0 30px 0 10px;
  //}
  //
  //:global .ant-radio-wrapper {
  //  line-height: 30px;
  //}
  //
  //:global .ant-input-affix-wrapper > .ant-input {
  //  height: 26px;
  //}

  // :global .ant-input-affix-wrapper {
  //   margin: 0 10px;
  // }
  //:global .ant-select {
  //  display: inline-block;
  //  width: 120px;
  //}

  //:global .ant-select-selector {
  //  height: 26px !important;
  //}
  //
  //:global .ant-checkbox-wrapper {
  //  width: 100px;
  //  vertical-align: -webkit-baseline-middle;
  //}
  //
  //:global .ant-select-selector {
  //  height: 26px !important;
  //}
}

.cbox{
  :global .ant-checkbox{
    margin-bottom: 11px;
  }
}

.verify_title_box {
  padding: 0 12px;

  & > span:nth-child(1) {
    color: #4E5969;
    display: inline-block;
    width: 92px;
  }

  & > span:nth-child(2) {
    color: #1D2129;
  }

  p {
    display: inline-block;
    font-size: 24px;
    color: #1D2129;
    font-weight: bold;
  }
}

.verify_content_box {
  background: #F7F8FA;
  padding: 14px 12px;
  margin-top: 12px;

  .verify_content_box_item {
    margin-bottom: 8px;
    display: flex;

    & > span:nth-child(1) {
      flex-shrink: 0;
      color: #4E5969;
      display: inline-block;
      width: 100px;
    }

    & > span:nth-child(2) {
      color: #1D2129

    }
  }
}

.popoverWrap {
  :global .xlb-ant-popover-inner-content {
    max-height: 200px !important;
    overflow-y: auto !important;
  }
}