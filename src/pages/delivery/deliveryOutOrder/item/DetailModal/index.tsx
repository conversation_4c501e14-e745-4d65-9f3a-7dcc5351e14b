import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { formatWithCommas, hasAuth, sortTypeSwitch } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import { SearchOutlined } from '@ant-design/icons';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbModal,
  XlbSelect,
  XlbShortTable,
  XlbTable,
  XlbTabs,
  XlbTipsModal,
  XlbUploadFile,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Badge, DatePicker, Input, message, Select, Tooltip } from 'antd';
import { unionBy } from 'lodash-es';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import SignaturePrintingModal from './../attachmentOfReceiptForm';
import SelectOrder from './../SelectOrder';
import { itemTableList_all, itemTableListDetail } from './data';
import styles from './item.less';
import {
  addInfo,
  auditInfo,
  deliveryout,
  deliveryparamRead,
  getReceiveOrderItem,
  getStock,
  getStoreAmout,
  print,
  readInfo,
  reauditRequest,
  updateInfo,
} from './server';
// import { orderStatusIcons } from '../../../data/common/data';

const { Option } = Select;
const DeliveryInOrderItem = (props: any) => {
  const [fileList, setFileList] = useState<any[]>([]);
  const forbidenClickRowEvent = useRef(false);
  const [SelectOrderVisible, setSelectOrderVisible] = useState(false);
  const [rowData, setRowData] = useState<any[]>([]);
  const [summaryData, setSummaryData] = useState<any[]>([]); //汇总表数据
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [keepData, setKeepData] = useState(false);
  const { downByProgress } = useDownload();
  const [fileInfo, setFileInfo] = useState<any[]>([]);
  const [signatureVisible, setSignatureVisible] = useState<boolean>(false);
  const signatureData = useRef([]); // 打印数据
  const [AmountInfo, setAmount] = useState<any>({}); //门店余额

  const [unCenterStoreApplication, setUnCenterStoreApplication] =
    useState(false);
  const [managementType, setManagementType] = useState(null);
  const [enableDeliveryCenter, setEnableDeliveryCenter] = useState(null); //是否是配送中心门店
  const [itemArr, setItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableList_all)),
  );
  const [itemArrdetail, setdetailItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableListDetail)),
  );
  const [pagin, setPagin] = useState({
    pageSize: 200000,
    pageNum: 1,
    total: 0,
  });
  const [isFold, setIsFold] = useState<boolean>(false);
  const [uploadFileModalVisible, setUploadFileModalVisible] =
    useState<boolean>(false);
  const formBox = useRef<HTMLDivElement | null>(null);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [stockList, setStockList] = useState<any[]>([]);
  const [ErrorState, setErrorState] = useState<any>({
    isCancel: false,
    isConfirm: true,
    msg: '',
    fail_list: [],
  });
  // const [form] = Form.useForm()
  const [form] = XlbBasicForm.useForm();
  const in_store_id = XlbBasicForm.useWatch('in_store_id', form);
  const [fid, setFid] = useState<any>();
  const [info, setInfo] = useState({ state: 'INIT', fid: null });
  const [batchLoding, setBatchLoding] = useState<boolean>(false);
  const [tabsKey, setTabsKey] = useState<string>('detailTab'); //明细汇总切换
  const [tableKey, setTableKey] = useState<any>('baseInfo');
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [centerStore, setCenterStore] = useState({
    isCenter: '',
    deliveryCenterId: '',
  }); //记录调入门店是否为配送中心,上游配送中心id
  const [outStore, setOutStore] = useState({ isCenter: '' }); //记录调入门店是否为配送中心,上游配送中心id
  const [toOtherCenter, setToOtherCenter] = useState<boolean>(false); //是否支持跨配送中心调拨
  const [audit, setAudit] = useState<boolean>(true); //新增保存后审核可用
  // const [itemRowData, setItemRowData] = useState<any>([]); //快捷添加行表格数据
  // const [keyWordLength, setKeyWordLength] = useState<number>(0); //快捷添加行关键字length
  // const [cIndex, setcIndex] = useState<any>(''); //快捷添加行高亮下标
  const [outOrder, setOutOrder] = useState<boolean>(false);
  const [storeModal, setStoreModal] = useState<any>({
    modalVisible: false, // 弹窗是否展示
    storeItem: {}, // 接收弹窗选中的值
    modalState: {
      // hasChosenItem:[],
      isMultiple: true, //（默认false单选，true多选）是否多选
      modalType: 'store', //弹窗类型 （必传）'item'商品 'store'门店  'supplier'供应商
      value: '',
    },
  });
  const [storesChoose, setStoresChoose] = useState<any>({
    modalVisible: false, // 弹窗是否展示
    storeItem: {}, // 接收弹窗选中的值
    modalState: {
      // hasChosenItem:[],
      isMultiple: true, //（默认false单选，true多选）是否多选
      modalType: 'store', //弹窗类型 （必传）'item'商品 'store'门店  'supplier'供应商
    },
  });
  const [TipModalVisible, setTipModalVisible] = useState<boolean>(false);
  const [stateTips, setStateTips] = useState<{
    tips: any;
    isConfirm: boolean;
    isCancel: boolean;
    width: string;
    showDesc: string;
  }>({
    tips: '',
    isConfirm: true,
    isCancel: false,
    width: '450px',
    showDesc: '',
  });
  //明细弹框
  const [billModel, setBillModel] = useState<any>({
    title: '',
    BillModelVisible: false, // 弹窗是否展示
    Multiple: true, //多选(true)单选(false)
    selectData: [], //已选择的明细
  });
  const [ban, setBan] = useState<boolean>(true); //是否可编辑
  // const ser = useRef<HTMLDivElement | null>(null);
  // const tab = useRef<HTMLDivElement | null>(null);
  const [errNames, setErrNames] = useState<any>([]);
  const [WmsInfo, setWmsInfo] = useState<any>({});
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });
  const { enable_organization } = useBaseParams((state) => state);

  const InvoiceRender = (item: any) => {
    switch (item.code) {
      case '_index':
        item.render = (value: any, record: any, index: any) => {
          return value === '合计' ? (
            <div className="info overwidth">{value}</div>
          ) : (
            <div className="info overwidth">
              {(pagin.pageNum - 1) * pagin.pageSize + 1 + index.index}
            </div>
          );
        };
        break;
      case 'item_name':
        item.render = (text) => {
          return text;
        };
        break;
      case 'basic_stock_quantity':
        item.render = (value: any, record: any) => {
          return record.index === '合计' || record.newRow ? null : (
            <div className="overwidth">
              {toFixed(
                safeMath.divide(record.value, record.ratio),
                'QUANTITY',
                true,
              )}
            </div>
          );
        };
        break;
      // 零售单价---零售价
      case 'sale_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {record.index == '合计'
                ? null
                : hasAuth(['调入单/零售价', '查询'])
                  ? Number(value || 0).toFixed(4)
                  : '****'}
            </div>
          );
        };
        break;
      case 'present_unit':
      case 'unit':
        item.render = (value: any, record: any, scope: { index: any }) => {
          const index = scope.index;
          return record?._click &&
            record?.item_name &&
            tabsKey == 'detailTab' &&
            !outOrder ? (
            <Select
              style={{ width: '100%' }}
              defaultValue={value}
              onClick={(e) => {
                console.log(record, index);
                e?.stopPropagation();
              }}
              onChange={(e) => ratioChangeByUnit(e, item.code, index)}
            >
              {record?.units?.map((v: any, i: any) => {
                return JSON.parse(v).name ? (
                  <Option key={i} value={JSON.parse(v).name}>
                    {JSON.parse(v).name}
                  </Option>
                ) : null;
              })}
            </Select>
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'ratio': //#region 换算率
      case 'basic_quantity': //基本数量
        item.render = (value: any, record: any, index) => {
          return record?._click &&
            record?.item_name &&
            tabsKey == 'detailTab' &&
            record.account_method === '中心手工批次' &&
            centerStore.isCenter &&
            !outOrder ? (
            <Input
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.index.toString()}
              defaultValue={Number(value || 0).toFixed(3)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index.index, item.code, record.ratio)
              }
              onBlur={(e) => inputBlur(e, index.index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index.index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {record.index == '合计' && item.code == 'ratio'
                ? null
                : Number(value || 0).toFixed(3)}
            </div>
          );
        };
        break;
      //#region数量 赠品数量
      case 'quantity': //数量
      case 'present_quantity': //赠品数量
        item.render = (value: any, record: any, index) => {
          return record._click && record?.item_name ? (
            <Input
              key={record[item.code]}
              className="full-box"
              type="number"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={Number(value || 0).toFixed(3)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index.index, item.code, record.ratio)
              }
              onBlur={(e) => inputBlur(e, index.index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index.index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {Number(value || 0).toFixed(3)}
            </div>
          );
        };
        break;
      // 税费、成本、成本去税---成本价
      case 'cost_money':
      case 'no_tax_cost_money':
      case 'tax_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {hasAuth(['调入单/成本价', '查询'])
                ? Number(value || 0).toFixed(2)
                : '****'}
            </div>
          );
        };
        break;

      //单价、金额、基本单价---配送价
      case 'money': //金额(含税)
        item.render = (value: any, record: any, index) => {
          return hasAuth(['调入单/配送价', '编辑']) &&
            record._click &&
            record?.item_name &&
            !outOrder ? (
            <Input
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={Number(value || 0).toFixed(2)}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index.index, item.code, record.ratio)
              }
              onBlur={(e) => inputBlur(e, index.index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index.index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {hasAuth(['调入单/配送价', '查询'])
                ? formatWithCommas(Number(value || 0).toFixed(2))
                : '****'}
            </div>
          );
        };
        break;
      case 'price': //单价(含税)
      case 'basic_price': //基本单价(含税)
        item.render = (value: any, record: any, index) => {
          return hasAuth(['调入单/配送价', '编辑']) &&
            record._click &&
            record?.item_name &&
            !outOrder ? (
            <Input
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={Number(value ? value : 0).toFixed(4)}
              onFocus={(e) => e.target.select()}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onChange={(e) =>
                inputChange(e, index.index, item.code, record.ratio)
              }
              onBlur={(e) => inputBlur(e, index.index, item.code, record.ratio)}
              style={{ textAlign: 'right' }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index.index);
              }}
            />
          ) : (
            <div className="info overwidth">
              {record.index === '合计'
                ? null
                : hasAuth(['调入单/配送价', '查询'])
                  ? Number(value ? value : 0).toFixed(4)
                  : '****'}
            </div>
          );
        };
        break;
      case 'producing_date':
        item.render = (value: any, record: any, index) => {
          return record.edit && record.producing_date_flag ? (
            <DatePicker
              defaultValue={value ? moment(value) : undefined}
              onChange={(e) =>
                inputChange(e, index.index, item.code, record.ratio)
              }
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'expire_date':
        item.render = (value: any, record: any, index) => {
          return record.edit && record.expire_date_flag ? (
            <DatePicker
              defaultValue={value ? moment(value) : undefined}
              onChange={(e) =>
                inputChange(e, index.index, item.code, record.ratio)
              }
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'batch_number':
        item.render = (value: any, record: any, index) => {
          const reg = /[\W]/g;
          return record.edit && record.batch_number_flag ? (
            <Input
              key={record[item.code]}
              style={{ width: '120px' }}
              autoComplete={'off'}
              maxLength={20}
              onFocus={(e) => e.target.select()}
              defaultValue={value}
              size="small"
              onChange={(e) =>
                (rowData[index.index].batch_number = e.target.value.replace(
                  reg,
                  '',
                ))
              }
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: number) => {
          return record._click && record?.item_name ? (
            <Input
              key={record[item.code]}
              className="full-box"
              autoComplete={'off'}
              id={item.code + '-' + index.toString()}
              defaultValue={value}
              onFocus={(e) => e.target.select()}
              onBlur={(e) => inputBlur(e, index, item.code, record.ratio)}
              style={{ textAlign: 'left' }}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onPressEnter={(e) => {
                onPressEnter(item.code, index);
              }}
            />
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      // case 'period':
      //   item.render = (value: any, record: any, index: number) => {
      //     return (
      //       <div className="info overwidth">
      //         {value?.slice(-1) === '月' ? value.slice(0, -1) + '个月' : value}
      //       </div>
      //     )
      //   }
      //   break
      default:
        return (item.render = (value: any, record: any, index: number) => (
          <div className="info overwidth">{value}</div>
        ));
    }
  };
  const getArrayChild = (v: any) => {
    const res = Object.prototype.toString.call(v).slice(8, -1).toLowerCase();
    return res === 'array' ? v[0] : v;
  };
  // 单据选择添加
  const addOrder = async (list: any, type: string = 'no_count') => {
    console.log('list', list);
    const res = await getReceiveOrderItem({
      fids: list,
      add_count: type === 'count',
      in_store_id: getArrayChild(form.getFieldValue('in_store_id')),
      storehouse_id: getArrayChild(form.getFieldValue('storehouse_id')),
      out_store_id: getArrayChild(form.getFieldValue('store_id')),
    });
    if (res.code === 0) {
      // const newArr = itemConfirmCompare(res.data)
      const ids = rowData.map((v) => v.item_id);
      let repeatArr: Array<any> = [];
      let newArr: Array<any> = [];
      if (centerStore.isCenter) {
        repeatArr = res.data?.filter(
          (v: any) =>
            ids.includes(v.item_id) &&
            !(
              v.account_method == '中心手工批次' ||
              (v.account_method == '移动加权平均' &&
                (v.expire_date_flag || v.producing_date_flag))
            ),
        );
        newArr = res.data?.filter(
          (item: any) =>
            !ids.includes(item.item_id) ||
            item.account_method == '中心手工批次' ||
            (item.account_method == '移动加权平均' &&
              (item.expire_date_flag || item.producing_date_flag)),
        );
      } else {
        repeatArr = res.data?.filter((v: any) => ids.includes(v.item_id));
        newArr = res.data?.filter((item: any) => !ids.includes(item.item_id));
      }
      let rName = [repeatArr.map((v: any) => `【${v.item_name}】`).join('、')];
      if (repeatArr.length) {
        XlbTipsModal({
          tips: (
            <>
              <div style={{ color: 'red' }}>
                以下商品已存在，不允许重复添加，系统已自动过滤!
              </div>
              {rName.map((_) => {
                return <div key={_}>{_}</div>;
              })}
            </>
          ),
        });
        return;
      }
      const newList = newArr.map((v: any) => {
        return {
          // sale_price: v?.sale_price,
          account_method: v.account_method,
          item_id: v.item_id,

          item_code: v.item_code,
          item_bar_code: v.item_bar_code,
          item_name: v.item_name,
          item_spec: v.item_spec,
          unit: v?.unit,
          quantity: v?.quantity,
          price: v?.price,
          money: v?.money,
          tax_rate: v?.tax_rate,
          tax_money: v?.tax_money || 0,
          tare: v?.tare || 0,
          basic_unit: v?.basic_unit || v?.unit,
          ratio: v?.ratio,
          basic_quantity: v?.basic_quantity || 0,
          basic_price: v?.basic_price,
          present_unit: v?.present_unit,
          present_ratio: v?.present_ratio,
          present_quantity: v?.present_quantity,
          producing_date: v?.producing_date || null,
          expire_date: v?.expire_date || null,
          batch_number: v?.batch_number || null,
          // expire_type: v.expire_type,//保质期规则
          period: v?.period
            ? v?.period
            : v?.expire_type === 1
              ? v?.expire_type_num + '天'
              : v?.expire_type_num + '月', //保质期
          basic_stock_quantity: v?.basic_stock_quantity,
          basic_available_stock_quantity: v?.basic_available_stock_quantity,
          memo: v?.memo || '',
          cost_price: v?.cost_price,
          producing_date_flag: v?.producing_date_flag, //是否可选生产日期
          batch_number_flag: v?.batch_number_flag, //批次号
          expire_date_flag: v?.expire_date_flag, //到期日期
          date_in_type: v?.date_in_type, //日期录入规则 -----
          delivery_unit: v?.delivery_unit,
          delivery_ratio: v?.delivery_ratio, //配送单位换算率
          purchase_unit: v?.purchase_unit,
          purchase_ratio: v?.purchase_ratio, //采购单位
          stock_unit: v?.stock_unit,
          stock_ratio: v?.stock_ratio, //库存单位
          wholesale_unit: v?.wholesale_unit,
          wholesale_ratio: v?.wholesale_ratio, //批发单位
          receive_order_fids: v?.receive_order_fids, //关联采购单单据号
          // batch_unit: v.batch_unit || '', // ----
          // batch_ratio: v.batch_ratio || '', //批次单位 ----
          units: Array.from(
            new Set([
              JSON.stringify({ name: v.basic_unit, ratio: 1 }),
              JSON.stringify({
                name: v.unit,
                ratio: v.ratio,
              }),
              JSON.stringify({
                name: v.delivery_unit,
                ratio: v.delivery_ratio,
              }),
              JSON.stringify({
                name: v.purchase_unit,
                ratio: v.present_ratio,
              }),
              JSON.stringify({
                name: v.stock_unit,
                ratio: v.stock_ratio,
              }),
              JSON.stringify({
                name: v.wholesale_unit,
                ratio: v.wholesale_ratio,
              }),
            ]),
          ),
        };
      });
      setEdit(newList.length > 0); //   newList有数据可编辑
      let mergeArr = [...rowData, ...newList];
      mergeArr.map((item, index) => {
        if (item.newRow) {
          mergeArr.splice(index, 1);
        }
      });
      forbidenClickRowEvent.current = true;
      setRowData([...mergeArr]);
      setTimeout(() => {
        forbidenClickRowEvent.current = false;
      }, 0);
    }
    setSelectOrderVisible(false);
  };
  //回车事件
  const onPressEnter = (code: any, index: any) => {
    Promise.resolve()
      .then(() => {
        rowData[index].edit = false;
        index + 1 == rowData.length
          ? (rowData[0].edit = true)
          : (rowData[index + 1].edit = true);
        setRowData(JSON.parse(JSON.stringify(rowData)));
      })
      .then(() => {
        const inputBox =
          index + 1 == rowData.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString());
        inputBox?.focus();
      });
  };
  //快捷添加行上、下、回车 键盘事件

  //单位改变换算率
  const ratioChangeByUnit = (value: any, key: any, index: any) => {
    // console.log(value, key, index)
    //触发编辑
    setEdit(true);
    rowData[index][key] = value;
    //切换【单位】计算单价=基本单价*切换后单位的换算率；计算金额=单价*数量 ；计算基本数量=数量*切换后单位的换算率
    if (key == 'unit') {
      rowData[index].units.map((v: any, i: any) => {
        JSON.parse(v).name == value
          ? (rowData[index].ratio = JSON.parse(v).ratio)
          : null;
      });
      rowData[index].price = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].ratio,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index].price,
        rowData[index].quantity,
      );
      rowData[index].basic_quantity = safeMath.multiply(
        rowData[index].quantity,
        rowData[index].ratio,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      rowData[index].present_unit == rowData[index]?.unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
    }
    if (key === 'present_unit') {
      rowData[index].units.map((v: any, i: any) => {
        JSON.parse(v).name == value
          ? (rowData[index].present_ratio = JSON.parse(v).ratio)
          : null;
      });
      rowData[index].present_unit == rowData[index].unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
    }
  };
  //输入框改变
  const inputChange = (e: any, index: any, key: any, ratio: any) => {
    // console.log(index, key, ratio,e)
    //记录编辑
    setEdit(true);
    if (key == 'producing_date' || key == 'expire_date') {
      rowData[index][key] = e ? moment(e._d).format('YYYY-MM-DD') : null;
    }
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (key == 'quantity' && regPos.test(e.target.value)) {
      if (
        !outOrder ||
        (outOrder &&
          safeMath.multiply(Number(e.target.value), ratio) <=
            rowData[index].available_basic_quantity)
      ) {
        //编辑【数量】,计算基本数量,金额（含税）;
        rowData[index][key] = Number(e.target.value);
        rowData[index].basic_quantity = safeMath.multiply(
          e.target.value,
          ratio,
        );
        rowData[index].money = safeMath.multiply(
          rowData[index].price,
          rowData[index][key],
        );
        rowData[index].tax_money = safeMath.minus(
          rowData[index].money,
          safeMath.divide(
            rowData[index].money,
            safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
          ),
        );
      }
    }
    if (key == 'price' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【单价（含税）】 计算金额（含税）=单价（含税）*数量；计算基本单价（含税）=单价（含税）/ 换算率；
      rowData[index][key] = Number(e.target.value);
      rowData[index].money = safeMath.multiply(
        rowData[index][key],
        rowData[index].quantity,
      );
      rowData[index].basic_price = safeMath.divide(
        rowData[index][key],
        rowData[index].ratio,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key == 'money' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【金额（含税）】 计算税费=金额(含税)*销项税率*0.01  计算单价含税
      rowData[index][key] = Number(e.target.value);
      if (rowData[index].quantity != 0) {
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
        rowData[index].basic_price = safeMath.divide(
          rowData[index].money,
          rowData[index].basic_quantity,
        );
      }
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key == 'ratio' && regPos.test(e.target.value) && e.target.value >= 0) {
      //编辑【换算率】 计算基本单价（含税）, 基本数量；
      rowData[index][key] = Number(e.target.value);
      rowData[index].basic_quantity =
        safeMath.multiply(rowData[index].ratio, rowData[index].quantity) || 0;
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].present_unit == rowData[index].unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      if (rowData[index].quantity != 0) {
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
      }
    }
    if (key == 'basic_quantity' && regPos.test(e.target.value)) {
      //编辑【基本数量】计算换算率
      rowData[index][key] = Number(e.target.value);
      if (Number(e.target.value) === 0) {
        rowData[index].quantity = 0;
      }
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
      if (rowData[index].quantity != 0) {
        rowData[index].ratio = safeMath.divide(
          rowData[index].basic_quantity,
          rowData[index].quantity,
        );
        rowData[index].price = safeMath.divide(
          rowData[index].money,
          rowData[index].quantity,
        );
      }
      rowData[index].present_unit == rowData[index].unit &&
        (rowData[index].present_ratio = rowData[index].ratio);
    }
    if (
      key == 'basic_price' &&
      regPos.test(e.target.value) &&
      e.target.value >= 0
    ) {
      //编辑【基本单价（含税）】计算金额（含税）=基本单价*基本数量；计算单价=基本单价*换算率；
      rowData[index][key] = Number(e.target.value);
      rowData[index].price = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].ratio,
      );
      rowData[index].money = safeMath.multiply(
        rowData[index].basic_price,
        rowData[index].basic_quantity,
      );
      rowData[index].tax_money = safeMath.minus(
        rowData[index].money,
        safeMath.divide(
          rowData[index].money,
          safeMath.add(1, safeMath.multiply(rowData[index].tax_rate, 0.01)),
        ),
      );
    }
    if (key == 'present_quantity' && regPos.test(e.target.value)) {
      if (
        !outOrder ||
        (outOrder &&
          safeMath.multiply(
            Number(e.target.value),
            rowData[index].present_ratio,
          ) <= rowData[index].available_basic_present_quantity)
      ) {
        rowData[index][key] = Number(e.target.value);
      }
    }
    // debounce(setData)
  };
  //失去焦点
  const inputBlur = (e: any, scope: any, key: any, ratio: any) => {
    const index = scope.hasOwnProperty('index') ? scope.index : scope;
    // console.log(index, key, ratio)
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (key == 'quantity' && !regPos.test(e.target.value)) {
      setStateTips({
        ...stateTips,
        tips: `数量请输入数字`,
        isCancel: false,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'quantity' &&
      outOrder &&
      safeMath.multiply(Number(e.target.value), ratio) >
        rowData[index].available_basic_quantity
    ) {
      setStateTips({
        ...stateTips,
        tips: `商品【${rowData[index].item_name}】待收数量为${safeMath.divide(
          rowData[index].available_basic_quantity,
          ratio,
        )}，已超量！`,
        isCancel: false,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = safeMath.divide(
        rowData[index].available_basic_quantity,
        ratio,
      );
      return;
    }
    if (
      key == 'price' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      setStateTips({
        ...stateTips,
        tips: `单价（含税）请输入>=0的数字`,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'money' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      setStateTips({
        ...stateTips,
        tips: `金额（含税）请输入>=0的数字`,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'ratio' &&
      (!regPos.test(e.target.value) || e.target.value <= 0)
    ) {
      setStateTips({
        ...stateTips,
        tips: `换算率请输入>0的数字`,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (key == 'basic_quantity' && !regPos.test(e.target.value)) {
      setStateTips({
        ...stateTips,
        tips: `基本数量请输入数字`,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (key == 'basic_quantity' && !Number(rowData[index].quantity)) {
      setStateTips({
        ...stateTips,
        tips: `数量为0,请先输入数量`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'basic_price' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      setStateTips({
        ...stateTips,
        tips: `基本单价（含税）请输入>=0的数字`,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (key == 'present_quantity' && !regPos.test(e.target.value)) {
      setStateTips({
        ...stateTips,
        tips: `赠品数量请输入数字`,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = 0;
      return;
    }
    if (
      key == 'present_quantity' &&
      outOrder &&
      safeMath.multiply(Number(e.target.value), rowData[index].present_ratio) >
        rowData[index].available_basic_present_quantity
    ) {
      setStateTips({
        ...stateTips,
        tips: `商品【${rowData[index].item_name}】赠品待收数量为${safeMath.divide(
          rowData[index].available_basic_present_quantity,
          rowData[index].present_ratio,
        )}，已超量！`,
        isCancel: false,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index][key] = safeMath.divide(
        rowData[index].available_basic_present_quantity,
        rowData[index].present_ratio,
      );
      return;
    }
    if (
      safeMath.multiply(
        rowData[index]?.present_quantity || 0,
        rowData[index].quantity,
      ) < 0
    ) {
      setStateTips({
        ...stateTips,
        tips: `商品【${rowData[index].item_name}】数量与赠品数量必须同为正数或负数！`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'blur',
      });
      setTipModalVisible(true);
      rowData[index].present_quantity = 0;
      rowData[index].quantity = 0;
      return;
    }
  };
  //#region 查询门店下仓库
  const getStockData = async (id: any) => {
    const res = await getStock({ store_id: id });
    if (res?.code == 0) {
      const labArr = res.data.map((item: any) => ({
        label: item.name,
        value: item.id,
        default_flag: item.default_flag,
      }));
      const _lab = labArr.filter((item: any) => item.default_flag);
      if (_lab.length !== 0) {
        form.setFieldsValue({ storehouse_id: _lab[0].value });
      } else {
        form.setFieldsValue({ storehouse_id: labArr[0]?.value });
      }
      setStockList(labArr);
    }
  };
  //#region 单位下拉框转换
  const setUnits = (v: any) => {
    if (!v) return;
    const unit = { name: v.unit, ratio: v.ratio };
    const basic_unit = {
      name: v.basic_unit,
      ratio: v.basic_unit === v.unit ? unit.ratio : 1,
    };
    const delivery_unit = {
      name: v.delivery_unit,
      ratio: v.delivery_unit === v.unit ? unit.ratio : v.delivery_ratio,
    };
    const purchase_unit = {
      name: v.purchase_unit,
      ratio: v.purchase_unit === v.unit ? unit.ratio : v.purchase_ratio,
    };
    const stock_unit = {
      name: v.stock_unit,
      ratio: v.stock_unit === v.unit ? unit.ratio : v.stock_ratio,
    };
    const wholesale_unit = {
      name: v.wholesale_unit,
      ratio: v.wholesale_unit === v.unit ? unit.ratio : v.wholesale_ratio,
    };

    v.units = Array.from(
      new Set([
        JSON.stringify(basic_unit),
        JSON.stringify(delivery_unit),
        JSON.stringify(purchase_unit),
        JSON.stringify(stock_unit),
        JSON.stringify(wholesale_unit),
        JSON.stringify(unit),
      ]),
    );
  };
  //#region 读取信息
  const readinfo = async (
    _fid: any,
    fids: any = [],
    summary: boolean = tabsKey == 'detailTab' ? false : true,
  ) => {
    setEdit(_fid === 'handleBill');
    setAudit(_fid == 'handleBill');
    setIsLoading(true);
    const res =
      _fid === 'handleBill'
        ? await deliveryout({ fids: fids })
        : await readInfo({
            fid: _fid,
            summary: summary,
            orders: sortType.code
              ? [
                  {
                    direction: sortType.order.toUpperCase(),
                    property: sortTypeSwitch(sortType.code),
                  },
                ]
              : undefined,
          });

    setIsLoading(false);
    if (res?.code === 0) {
      await getStockData(res.data.store_id);
      setCenterStore({
        isCenter: res.data.out_center_flag,
        deliveryCenterId: '',
      });
      setOutStore({ isCenter: res.data.out_center_flag });
      setManagementType(res.data.management_type);
      setEnableDeliveryCenter(res.data.in_center_flag);
      res.data.details.map((v: any) => {
        v.tax_rate = v.tax_rate ? v.tax_rate : 0;
        v.present_quantity = v.present_quantity ? v.present_quantity : 0;
        v.basic_stock_quantity = v.basic_stock_quantity
          ? v.basic_stock_quantity
          : 0;
        v.basic_available_stock_quantity = v.basic_available_stock_quantity
          ? v.basic_available_stock_quantity
          : 0;
        v._edit = false;
        setUnits(v);
      });
      setInfo({ state: res.data.state, fid: res.data.fid });
      setFileList(res.data.files);
      setWmsInfo(res.data?.wms_info ? res.data?.wms_info : {});
      setRowData(res.data.details);
      setSummaryData(res.data?.summary_details);
      form.setFieldsValue({
        ...res.data,
        in_store_id: res.data.in_store_id,
        store_id: res.data.store_id,
        storehouse_id: res.data.storehouse_id,
        item_dept_names: res.data.item_dept_names,
        delivery_out_order_fids: res.data.delivery_out_order_fids,
        memo: res.data.memo,
        operate_date: res.data.operate_date
          ? moment(res.data.operate_date)
          : moment(),
        payment_date: res?.data?.payment_date
          ? moment(res.data.payment_date)
          : null,
        create_time: res.data.create_time,
        audit_time: res.data.audit_time,
        updata_time: res.data.updata_time,
      });

      setAmount(
        res.data.in_enable_credit_line
          ? {
              balance: res.data.balance,
              credit_line: res.data.credit_line,
              available_balance: res.data.available_balance,
            }
          : {},
      );
      if (res.data.state === 'INIT' && res.data.in_enable_credit_line) {
        getAmount(res.data.in_store_id, res.data.store_id);
      }
    }
  };
  //门店管理 启用授信额度
  const getAmount = async (store_id: number, out_store_id: number) => {
    if (!store_id || !out_store_id) return;
    const res = await getStoreAmout({ store_id, out_store_id, freeze: false });
    if (res.code === 0) setAmount(res.data);
  };
  //查询是否开启跨配送中心调拨
  const getdeliveryparam = async () => {
    const res = await deliveryparamRead();
    if (res?.code == 0) {
      setToOtherCenter(res.data.un_center_transform);
      setUnCenterStoreApplication(res.data.un_center_store_application);
    }
  };

  useEffect(() => {
    console.log('%c load1', 'color:red;font-size:20px');
    setBan(!hasAuth(['调出单', '编辑']));
    const record = props.data.current;
    console.log(record);

    const { index, ...rest } = record;
    setFid(record.fid);
    // setAllRow({ ...rest, index: index });
    // if (record.index === 0) setLastLoding(true);
    // if (record.index === record.total - 1) setNextLoding(true);
    if (record.fid === 1) {
      setRowData([{ item_name: '', newRow: true }]);
      setCenterStore({
        isCenter: LStorage.get('userInfo').store.enable_delivery_center,
        deliveryCenterId: LStorage.get('userInfo').store.upstream_center_id,
      });
      getStockData(LStorage.get('userInfo').store_id);
      form.setFieldsValue({
        store_name: LStorage.get('userInfo').store_name,
        store_id: [LStorage.get('userInfo').store_id],
        org_id: enable_organization && LStorage.get('userInfo').org_id,
        out_org_name: enable_organization && LStorage.get('userInfo').org_name,
        item_dept_names: LStorage.get('userInfo').item_dept_names,
        storehouse_id: LStorage.get('userInfo').item_dept_names,
        operate_date: moment(),
        // payment_date: moment()
      });
      setManagementType(LStorage.get('userInfo').store.management_type);
      setEnableDeliveryCenter(
        LStorage.get('userInfo').store.enable_delivery_center,
      );
    } else {
      // readinfo(record.fid)
    }
    getdeliveryparam();
    itemArr.map((v) => InvoiceRender(v));
    itemArrdetail.map((v) => InvoiceRender(v));
  }, []);
  useEffect(() => {
    console.log('%c load2', 'color:red;font-size:20px');

    const rec = props.data.current || {};
    if (tabsKey === 'totalTab') {
      (rec?.fid !== 1 || form.getFieldValue('fid')) &&
        readinfo(
          rec?.fid == 1 ? form.getFieldValue('fid') : rec?.fid,
          [],
          true,
        );
      itemArr.map((v) => InvoiceRender(v));
      rowData[rowData.length - 1]?.newRow &&
        rowData.splice(rowData.length - 1, 1);
      setRowData([...rowData]);
    } else {
      (rec.fid !== 1 || form.getFieldValue('fid')) &&
        readinfo(rec.fid == 1 ? form.getFieldValue('fid') : rec.fid);
    }
  }, [JSON.stringify(tabsKey)]);
  useEffect(() => {
    console.log('%c load3', 'color:red;font-size:20px');
    getArrayChild(form.getFieldValue('out_store_id')) &&
    getArrayChild(form.getFieldValue('store_id')) &&
    getArrayChild(form.getFieldValue('storehouse_id')) &&
    info.state === 'INIT'
      ? setBatchLoding(false)
      : setBatchLoding(true);
    form.getFieldValue('delivery_out_order_fids')
      ? form.getFieldValue('delivery_out_order_fids')[0]
        ? setOutOrder(true)
        : setOutOrder(false)
      : setOutOrder(false);
  }, [form.getFieldsValue()]);
  useEffect(() => {
    // 设置合计行
    footerData[0] = {};
    footerData[0]._index = '合计';
    footerData[0].money = hasAuth(['调入单/配送价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v?.newRow ? 0 : v.money)),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].quantity = rowData
      .reduce(
        (sum, v) => safeMath.add(sum, Number(v?.newRow ? 0 : v.quantity)),
        0,
      )
      .toFixed(3);
    footerData[0].tax_money = hasAuth(['调入单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v?.newRow ? 0 : v.tax_money)),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].basic_quantity = rowData
      .reduce(
        (sum, v) => safeMath.add(sum, Number(v?.newRow ? 0 : v.basic_quantity)),
        0,
      )
      .toFixed(3);
    footerData[0].present_quantity = rowData
      .reduce(
        (sum, v) =>
          safeMath.add(sum, Number(v?.newRow ? 0 : v.present_quantity)),
        0,
      )
      .toFixed(3);
    footerData[0].cost_money = hasAuth(['调入单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) => safeMath.add(sum, Number(v?.newRow ? 0 : v.cost_money)),
            0,
          )
          .toFixed(2)
      : '****';
    footerData[0].no_tax_cost_money = hasAuth(['调入单/成本价', '查询'])
      ? rowData
          .reduce(
            (sum, v) =>
              safeMath.add(sum, Number(v?.newRow ? 0 : v.no_tax_cost_money)),
            0,
          )
          .toFixed(2)
      : '****';
    setFooterData([...footerData]);
    setPagin({
      ...pagin,
      pageSize: rowData.length || 200,
      total: rowData[rowData.length - 1]?.newRow
        ? rowData.length - 1
        : rowData.length,
    });
    if (
      rowData.length &&
      rowData[0].newRow &&
      form.getFieldValue('delivery_out_order_fids') &&
      form.getFieldValue('delivery_out_order_fids')[0]
    ) {
      allClear();
    }
  }, [JSON.stringify(rowData)]);
  useEffect(() => {
    console.log('%c load5', 'color:red;font-size:20px');
    if (sortType.code !== '' && fid !== 1) {
      readinfo(form.getFieldValue('fid'));
    }
  }, [sortType]);
  //数据校验
  const errListFun = (data: any) => {
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    const Arow = [...data].filter((_) => _.item_name);
    Arow.forEach((v: any, i: any) => {
      v.quantity = v.quantity || 0;
      v.price = v.price || 0;
      v.money = v.momey || 0;
      v.basic_quantity = v.basic_quantity || 0;
      if (!regPos.test(v.quantity))
        errNames.push(`第${i + 1}行,【${v.item_name}】数量请输入数字!`);
      if (!(regPos.test(v.ratio) || v.ratio < 0 || v.ratio > 999999999.999))
        errNames.push(
          `第${i + 1}行,【${v.item_name}】换算率请输入>=0并且<=999999999.999的数字!`,
        );
      if (!regPos.test(v.price) || v.price < 0)
        errNames.push(
          `第${i + 1}行,【${v.item_name}】单价（含税）请输入>=0的数字!`,
        );
      if (!regPos.test(v.money))
        errNames.push(`第${i + 1}行,【${v.item_name}】金额（含税）请输入数字!`);
      if (!regPos.test(v.basic_quantity))
        errNames.push(`第${i + 1}行,【${v.item_name}】基本数量请输入数字`);
      if (!regPos.test(v.basic_price) || v.basic_price < 0)
        errNames.push(
          `第${i + 1}行,【${v.item_name}】基本单价（含税）请输入>=0的数字!`,
        );
      if (!regPos.test(v.present_quantity)) v.present_quantity = 0; // 赠品 undefined 赋 0
      if (
        regPos.test(v.present_quantity) &&
        regPos.test(v.quantity) &&
        safeMath.multiply(v.present_quantity, v.quantity) < 0
      )
        errNames.push(
          `第${i + 1}行,【${v.item_name}】数量与赠品数量必须同为正数或负数！`,
        );
    });
    rowData
      .filter((_) => _.item_name)
      .forEach((v) => {
        console.log(
          v.quantity,
          'v.quantityv.quantityv.quantityv.quantityv.quantityv.quantity',
          Number(v.quantity).toFixed(8),
        );
        v.basic_price = Number(v.basic_price || 0).toFixed(8);
        v.quantity = Number(v.quantity || 0).toFixed(8);
        v.basic_quantity = Number(v.basic_quantity || 0).toFixed(8);
      });
    if (errNames.length) {
      XlbTipsModal({
        tips: (
          <>
            <div style={{ color: 'red' }}>以下数据有问题</div>
            {errNames.map((_) => {
              return <div key={_}>{_}</div>;
            })}
          </>
        ),
      });
      setErrNames([]);
      return false;
    }
    return true;
  };

  // 保存
  const saveOrder = async () => {
    if (!form.getFieldValue('store_id')) {
      XlbTipsModal({
        tips: `请先选择调出门店！`,
      });

      return;
    }
    if (!rowData[0]?.item_name || rowData.length === 0) {
      XlbTipsModal({
        tips: `请先添加商品！`,
      });
      return;
    }
    if (!errListFun(rowData)) {
      return;
    }

    // if (errNames.length) return;

    // rowData.map(
    //   (v: any) =>
    //     (v.basic_present_quantity =
    //       safeMath.multiply(v.present_quantity || 0, v.present_ratio || 0) ||
    //       0),
    // );
    const data = {
      out_org_id: form.getFieldValue('out_org_id'),
      org_id: form.getFieldValue('org_id'),
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      operate_date: moment(form.getFieldValue('operate_date')).format(
        'YYYY-MM-DD',
      ),
      payment_date: form.getFieldValue('payment_date')
        ? moment(form.getFieldValue('payment_date')).format('YYYY-MM-DD')
        : null,
      out_store_id: getArrayChild(form.getFieldValue('out_store_id')),
      store_id: getArrayChild(form.getFieldValue('store_id')),
      in_store_id: getArrayChild(form.getFieldValue('in_store_id')),
      storehouse_id: form.getFieldValue('storehouse_id'),
      delivery_out_order_fids: form.getFieldValue('delivery_out_order_fids'),
      details: rowData.filter((_) => _.item_name),
      files: fileList,
    };
    if (fid == 1) {
      // getData(1);
      setIsLoading(true);
      const res = await addInfo(data);
      setIsLoading(false);
      if (res?.code == 0) {
        readinfo(res.data.fid);
        setEdit(false);
        setFid(res.data.fid);
        message.success('保存成功');
        return;
      }
    } else {
      setIsLoading(true);
      const res = await updateInfo(data);
      setIsLoading(false);
      if (res?.code == 0) {
        readinfo(res.data.fid);
        setEdit(false);
        message.success('保存成功');
        return;
      }
    }
  };
  /**
   * @function 获取数据
   */
  const checkData = (page_Num: number) => {
    const _formData: any = LStorage.get('deliveryOrder');
    const time_type = _formData.time_type;
    const data: any = {};
    data.page_number = page_Num - 1;
    data.page_size = pagin.pageSize;
    data.create_date =
      time_type === 'create_date' ? _formData.create_date : undefined;
    data.audit_date =
      time_type === 'audit_date' ? _formData.create_date : undefined;
    data.state = _formData.state;
    data.settlement_state = _formData.settlement_state;
    data.store_ids = _formData.store_names ? _formData.store_ids : undefined;
    data.storehouse_id = _formData.storehouse_id
      ? _formData.storehouse_id
      : undefined;
    data.out_store_ids = _formData.out_store_names
      ? _formData.out_store_ids
      : undefined;
    data.item_ids = _formData.item_names ? _formData.item_ids : undefined;
    data.fid = _formData.fid;
    return data;
  };
  // const getData = async (page_Num: number) => {
  //   setIsLoading(true);
  //   const data = checkData(page_Num);
  //   const res = await getDeliveryInOrder({ ...data });
  //   setIsLoading(false);
  // };
  const reauditItem = async () => {
    const data = {
      fids: [form.getFieldValue('fid')],
    };
    setIsLoading(true);
    const res = await reauditRequest(data);
    if (res?.code == 0) {
      readinfo(form.getFieldValue('fid'));
      setEdit(false);
      message.success('反审核成功');
    }
    setIsLoading(false);
  };
  //审核
  const auditItem = async () => {
    rowData[rowData.length - 1].newRow && rowData.splice(rowData.length - 1, 1);
    //判断是否添加商品
    if (rowData.length == 0) {
      setStateTips({
        ...stateTips,
        tips: `请先添加商品！`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'auditItem',
      });
      setTipModalVisible(true);
      return;
    }
    // 判断商品数量
    if (
      !(Number(footerData[0].quantity) + Number(footerData[0].present_quantity))
    ) {
      setStateTips({
        ...stateTips,
        tips: `数量、赠品数量都为0，无法审核！`,
        isConfirm: true,
        isCancel: false,
        showDesc: 'auditItem',
      });
      setTipModalVisible(true);
      return;
    }
    //判断商品是否选择明细
    const errs: any = [];
    rowData.map((item, index) => {
      item.basic_present_quantity = safeMath.multiply(
        item.present_quantity,
        item.present_ratio,
      );
      if (
        centerStore.isCenter &&
        item.producing_date_flag &&
        !item.producing_date
      ) {
        errs.push(
          `第${index + 1}行,【${rowData[index].item_name}】未录入生产日期`,
        );
      }
      if (centerStore.isCenter && item.expire_date_flag && !item.expire_date) {
        errs.push(
          `第${index + 1}行,【${rowData[index].item_name}】未录入到期日期`,
        );
      }
    });
    if (errs.length > 0) {
      setErrorState({
        isCancel: false,
        isConfirm: true,
        fail_list: errs,
        msg: '以下商品日期未录入，无法审核！',
      });
      return;
    }
    errListFun(rowData);
    if (errNames.length) return;
    const data = {
      memo: form.getFieldValue('memo'),
      fid: form.getFieldValue('fid'),
      operate_date: moment(form.getFieldValue('operate_date')).format(
        'YYYY-MM-DD',
      ),
      payment_date: form.getFieldValue('payment_date')
        ? moment(form.getFieldValue('payment_date')).format('YYYY-MM-DD')
        : null,
      out_store_id: form.getFieldValue('out_store_id'), //调出
      store_id: form.getFieldValue('store_id'), //调入
      storehouse_id: form.getFieldValue('storehouse_id'),
      delivery_out_order_fids: form.getFieldValue('delivery_out_order_fids'),
      details: rowData,
      files: fileList,
    };
    setIsLoading(true);
    const res = await auditInfo(data);
    setIsLoading(false);
    if (res?.code == 0) {
      readinfo(res.data.fid);
      setEdit(false);
      message.success('操作成功');
      return;
    }
  };
  // 导入

  const importItem = async () => {
    const res = await XlbImportModal({
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.deliveryoutorder.template.download`,
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.deliveryoutorder.import?store_id=${form.getFieldValue(
        'store_id',
      )}&out_store_id=${form.getFieldValue('out_store_id')}&storehouse_id=${form.getFieldValue(
        'storehouse_id',
      )}`,
      templateName: '下载导入模板',
      callback: (res) => {
        console.log(res);
      },
    });
    console.log(res, '导入 success');
  };

  //导出
  const exportItem = async (e: any) => {
    if (edit) return message.warning('请先保存！');
    setIsLoading(true);
    const data = {
      fid: form.getFieldValue('fid'),
      summary: tabsKey === 'totalTab',
      orders: sortType.code
        ? [
            {
              direction: sortType.order.toUpperCase(),
              property: sortTypeSwitch(sortType.code),
            },
          ]
        : null,
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryoutorder.detail.export',
      { ...data },
    );
    if (res?.code == 0) {
      // downByProgress(e);
      message.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };
  //返回前判断保存状态
  const goBack = () => {
    if (edit) {
      setStateTips({
        ...stateTips,
        tips: `单据未保存，是否确认返回？`,
        isConfirm: true,
        isCancel: true,
        showDesc: 'edit',
      });
      setTipModalVisible(true);
      return;
    }
    props.parentRef.current.close();
  };
  //分页切换事件
  const pageChange = (p: number) => {
    setPagin({
      ...pagin,
      pageNum: p,
    });
  };
  //操作删除
  const showDeleteModal = (record: any, index: number) => {
    record.delete = true;
    rowData.splice(
      rowData.findIndex((v) => v.delete),
      1,
    );
    if (!rowData.length) {
      setRowData([...rowData, { name: '', newRow: true }]);
    } else {
      setRowData([...rowData]);
    }
    setEdit(true);
  };

  //确认事件

  //弹窗取消事件
  const handleCancel = () => {
    setSelectOrderVisible(false);
  };
  //弹窗取消事件
  const storesChooseCancel = () => {
    setStoresChoose({
      ...storesChoose,
      modalVisible: false,
    });
  };

  //门店弹框点击事件
  const handleDialogClick = async () => {
    const { in_store_id, store_id, storehouse_id } = form.getFieldsValue(true);
    // if (!getArrayChild(store_id)) { //
    //   message.warning('调出门店不能为空!');
    //   return;
    // }
    if (getArrayChild(in_store_id) === getArrayChild(store_id)) {
      message.warning('调出门店与调入门店不可相同!');
      return;
    }
    const result = await XlbBasicData({
      type: 'batchAddGoods',
      url: '/erp/hxl.erp.deliveryoutorder.item.page',
      data: {
        company_id: LStorage.get('userInfo')?.company_id,
        operator_store_id: getArrayChild(store_id),
        in_store_id: getArrayChild(in_store_id),
        store_id: getArrayChild(store_id),
        storehouse_id,
        status: 1,
      },
      isMultiple: true,
      dataType: 'lists',
      primaryKey: 'id',
      resetForm: true,
      nullable: false,
    });
    console.log(result);
    if (result) {
      const arr = result?.map((item0) => {
        setUnits(item0);
        return {
          ...item0,
          fid: item0?.id,
          _edit: false,
          ratio: item0.ratio || 1,
          item_name: item0?.name,
          newRow: false,
          bar_code: item0.bar_code,
          item_bar_code: item0.bar_code,
          item_code: item0.code,
          item_id: item0.id || item0.item_id,
          basic_unit: item0.basic_unit || item0.unit,
          tax_money: item0?.tax_money || 0,
        };
      });
      const data = rowData.filter((_) => _.item_name);
      const __data = unionBy(data, arr, 'bar_code');
      setRowData([...__data]);
    }
  };
  //打印
  const printItem = async () => {
    const data = {
      fid: form.getFieldValue('fid'),
      orders: sortType.code
        ? [
            {
              direction: sortType.order.toUpperCase(),
              property: sortTypeSwitch(sortType.code),
            },
          ]
        : null,
    };
    setIsLoading(true);
    const res = await print(data);
    setIsLoading(false);
    if (res?.code == 0) {
      // signatureData.current = res.data;
      // setSignatureVisible(true);
      printDom(res.data);
      // NiceModal.show(PrintMoreModal, { src: res.data });
      // window.open(res.data, "_blank", "toolbar=no, menubar=no, scrollbars=no, resizable=no,location=no, status=no")
    }
  };

  const printDom = async (data: any[]) => {
    const url = data[0];
    await XlbTipsModal({
      width: 900,
      tips: (
        <>
          <div style={{ height: '400px', overflow: 'hidden' }}>
            <iframe
              style={{ width: '100%', height: '100%' }}
              src={url?.url || ''}
            ></iframe>
          </div>
        </>
      ),
    });
  };
  //提示框确认事件
  const tipConfirm = async () => {
    if (stateTips.showDesc === 'edit') {
    } else {
      setTipModalVisible(false);
    }
  };
  //#region 单据弹框确定
  const handleBillOk = async (list: any) => {
    const store_id = list.every((v: any) => v.store_id == list[0].store_id);
    const in_store_id = list.every(
      (v: any) => v.in_store_id == list[0].in_store_id,
    );
    const storehouse_id = list.every(
      (v: any) => v.storehouse_id == list[0].storehouse_id,
    );
    if (!storehouse_id || !in_store_id || !store_id) {
      setStateTips({
        ...stateTips,
        tips: `请选择调出门店、调出仓库、调入门店相同的单据进行调入！`,
        isConfirm: true,
        isCancel: true,
        showDesc: 'error',
      });
      setTipModalVisible(true);
      return;
    }
    if (list.every((v: any) => v.in_state == 'ALLIN')) {
      setStateTips({
        ...stateTips,
        tips: `所选单据已全部调入，请重新选择！`,
        isConfirm: true,
        isCancel: true,
        showDesc: 'error',
      });
      setTipModalVisible(true);
      return;
    }
    handleBillCancel();
    readinfo(
      'handleBill',
      list.map((v: any) => v.fid),
    );
  };
  //单据取消事件
  const handleBillCancel = () => {
    setBillModel({
      ...billModel,
      BillModelVisible: false,
    });
  };
  const allClear = () => {
    setRowData([]);
    form.setFieldsValue({
      out_store_name: '',
      out_store_id: '',
      store_name: LStorage.get('userInfo').store_name,
      store_id: LStorage.get('userInfo').store_id,
      storehouse_id: '',
      item_dept_names: '',
      memo: '',
      delivery_out_order_fids: [],
      operate_date: moment(),
      // payment_date: moment()
    });
    getStockData(LStorage.get('userInfo').store_id);
  };
  if (rowData.length > 0) {
    itemArr.map((v) => InvoiceRender(v));
    itemArrdetail.map((v) => InvoiceRender(v));
  }
  if (rowData.length === 0 && tabsKey == 'detailTab') {
    setRowData([...[{ item_name: '', newRow: true }]]);
  }

  const openUpload = async () => {
    setFileInfo([...fileList]);
    setUploadFileModalVisible(true);
  };

  // #region  HTML
  return (
    <div style={{ padding: '12px 0 0 12px' }}>
      <SignaturePrintingModal
        open={signatureVisible}
        handleCancel={() => {
          setSignatureVisible(false);
        }}
        setPrintModal={() => {
          setSignatureVisible(false);
        }}
        rowData={signatureData.current}
      />

      <XlbModal
        title={
          <>
            <div>
              上传附件
              <span
                style={{
                  fontSize: 14,
                  color: ' rgb(134, 144, 156)',
                  marginLeft: 8,
                  wordBreak: 'break-all',
                }}
              >
                (支持上传PNG,JPG,JPEG,png,jpeg,webp,jpg,gif,bmp格式，最多9个)
              </span>
            </div>
          </>
        }
        open={uploadFileModalVisible}
        centered
        onOk={() => {
          setFileList([...fileInfo]);
          setUploadFileModalVisible(false);
          setFileInfo([]);
        }}
        onCancel={() => setUploadFileModalVisible(false)}
      >
        <div style={{ padding: '12px' }}>
          <XlbUploadFile
            accept={'image'}
            fileList={fileInfo}
            onChange={(e = []) => {
              console.log(e);
              if (e) {
                setFileInfo([...e]);
              } else {
                setFileInfo([]);
              }
            }}
            // mode="default"
            // fileList={[file]}
            listType={'picture'}
            maxCount={9}
            data={{ fid: form.getFieldValue('fid') }}
            action={`${process.env.BASE_URL}/erp/hxl.erp.deliveryoutorder.file.upload`}
          ></XlbUploadFile>
        </div>
      </XlbModal>
      <div className={'button_box row-flex'}>
        <div
          style={{ width: '90%', height: '100%' }}
          className="row-flex v-flex"
        >
          {/*  ToolBtn  */}
          <XlbButton.Group>
            {hasAuth(['调出单', '编辑']) ? (
              <XlbButton
                type="primary"
                label="保存"
                disabled={info.state !== 'INIT' || isLoading}
                onClick={saveOrder}
                icon={<XlbIcon name="baocun" />}
              />
            ) : null}
            {hasAuth(['调出单', '审核']) ? (
              <XlbButton
                type="primary"
                label="审核"
                disabled={info.state !== 'INIT' || audit || isLoading}
                onClick={auditItem}
                icon={<XlbIcon name="shenhe" />}
              />
            ) : null}
            {hasAuth(['调出单', '编辑']) ? (
              <Badge
                className={styles.badge}
                count={fileList.length}
                offset={[-16, 10]}
                size="small"
              >
                <XlbButton
                  type="primary"
                  label="附件"
                  disabled={info.state !== 'INIT' && fileList.length == 0}
                  onClick={openUpload}
                  icon={<XlbIcon name="fujian" />}
                />
              </Badge>
            ) : null}
            {!hasAuth(['调出单', '导出']) &&
            !hasAuth(['调出单', '打印']) ? null : (
              <XlbDropdownButton
                label="业务操作"
                dropList={
                  hasAuth(['调出单', '导出']) && hasAuth(['调出单', '打印'])
                    ? [
                        {
                          label: '导出',
                          disabled:
                            isLoading ||
                            !hasAuth(['调出单', '导出']) ||
                            !info.fid,
                        },
                        {
                          label: '打印',
                          disabled:
                            !form.getFieldValue('fid') ||
                            isLoading ||
                            !hasAuth(['调出单', '打印']),
                        },
                      ]
                    : hasAuth(['调出单', '导出'])
                      ? [
                          {
                            label: '导出',
                            disabled:
                              isLoading ||
                              !hasAuth(['调出单', '导出']) ||
                              !info.fid,
                          },
                        ]
                      : [
                          {
                            label: '打印',
                            disabled:
                              !form.getFieldValue('fid') ||
                              isLoading ||
                              !hasAuth(['调出单', '打印']),
                          },
                        ]
                }
                dropdownItemClick={(value: string, opt: any, e) => {
                  switch (opt.label) {
                    case '导出':
                      exportItem(e);
                      break;
                    case '打印':
                      printItem();
                      break;
                  }
                }}
              />
            )}

            {LStorage.get('userInfo').tel == '15151864744' ? (
              <XlbButton
                type="primary"
                label="反审核"
                loading={info.state !== 'AUDIT' || isLoading}
                onClick={reauditItem}
                icon={<XlbIcon name="fanshenhe" />}
              />
            ) : null}
            <XlbButton
              type="primary"
              label="返回"
              onClick={goBack}
              icon={<XlbIcon name="fanhui" />}
            />
          </XlbButton.Group>
        </div>
      </div>
      {isFold ? null : (
        <div ref={formBox}>
          {/* new form */}
          <XlbBasicForm
            colon
            form={form}
            autoComplete="off"
            layout="inline"
            className={styles.contractTab}
          >
            <XlbTabs
              defaultActiveKey={'baseInfo'}
              onChange={(key: string) => setTableKey(key)}
              tabBarExtraContent={{
                right: (
                  <div>
                    {/* {centerStore.isCenter ? '有数据' : '未渲染'} */}
                    {centerStore.isCenter && Object.keys(AmountInfo)?.length ? (
                      <div className={styles.amount_box}>
                        <span>门店余额：</span>
                        {AmountInfo.balance?.toFixed(2) || 0}
                        <span>授信额度：</span>
                        {AmountInfo.credit_line?.toFixed(2) || 0}
                        <span>配送额度：</span>
                        {AmountInfo.available_balance?.toFixed(2) || 0}
                      </div>
                    ) : null}
                  </div>
                ),
              }}
              items={[
                {
                  label: '基本信息',
                  key: 'baseInfo',
                  children: (
                    <div className="row-flex">
                      <div
                        className="row-flex"
                        style={{ flex: 1, flexWrap: 'wrap', marginTop: 12 }}
                      >
                        <XlbBasicForm.Item label="调入门店" name="in_store_id">
                          <XlbInputDialog
                            dialogParams={{
                              type: 'store',
                              dataType: 'lists',
                              isMultiple: false,
                              allClear: false,
                              // showDialogByDisabled: getFieldValue('org_ids')?.length,
                              // initCustomValue: '所有门店',
                              data: {
                                skip_filter: true,
                                centerFlag: true,
                                superiors:
                                  !centerStore.isCenter && !toOtherCenter
                                    ? centerStore.deliveryCenterId
                                    : null,
                                center_flag: LStorage.get('userInfo').store
                                  .enable_delivery_center
                                  ? null
                                  : false,
                                management_type:
                                  !unCenterStoreApplication &&
                                  managementType !== null &&
                                  !enableDeliveryCenter
                                    ? managementType == '0'
                                      ? '0'
                                      : '1'
                                    : null,
                              },
                            }}
                            disabled={
                              (fid !== 1 && info.state !== 'INIT') ||
                              (fid !== 1 &&
                                info.state == 'INIT' &&
                                rowData.length !== 0 &&
                                !rowData[0]?.newRow) ||
                              (fid === 1 &&
                                rowData.length !== 0 &&
                                !rowData[0]?.newRow)
                            }
                            fieldNames={{
                              idKey: 'id',
                              nameKey: 'store_name',
                            }}
                            handleValueChange={(_: string[], list: any[]) => {
                              const item = list?.[0] || {};
                              console.log(
                                list,
                                _,
                                'sdfghjkl',
                                form.getFieldValue('in_store_id'),
                              );
                              form.setFieldValue('org_name', item.org_name);
                              form.setFieldValue('org_id', item.org_id);
                              form.setFieldValue('in_store_id', _);
                            }}
                            width={180}
                          />
                        </XlbBasicForm.Item>
                        {enable_organization ? (
                          <XlbBasicForm.Item label="调入组织" name="org_name">
                            <XlbInput disabled style={{ width: '180px' }} />
                          </XlbBasicForm.Item>
                        ) : null}
                        <XlbBasicForm.Item label="调出门店" name="store_id">
                          <XlbInputDialog
                            dialogParams={{
                              type: 'store',
                              dataType: 'lists',
                              isMultiple: false,
                              allClear: false,
                              // showDialogByDisabled: getFieldValue('org_ids')?.length,
                              // initCustomValue: '所有门店',
                              data: {
                                skip_filter: true,
                                centerFlag: true,
                                superiors:
                                  !centerStore.isCenter && !toOtherCenter
                                    ? centerStore.deliveryCenterId
                                    : null,
                                center_flag: LStorage.get('userInfo').store
                                  .enable_delivery_center
                                  ? null
                                  : false,
                                management_type:
                                  !unCenterStoreApplication &&
                                  managementType !== null &&
                                  !enableDeliveryCenter
                                    ? managementType == '0'
                                      ? '0'
                                      : '1'
                                    : null,
                              },
                            }}
                            disabled={
                              (fid !== 1 && info.state !== 'INIT') ||
                              (fid !== 1 &&
                                info.state == 'INIT' &&
                                rowData.length !== 0 &&
                                !rowData[0]?.newRow) ||
                              (fid === 1 &&
                                rowData.length !== 0 &&
                                !rowData[0]?.newRow)
                            }
                            fieldNames={{
                              idKey: 'id',
                              nameKey: 'store_name',
                            }}
                            handleValueChange={(_: string[], list: any[]) => {
                              const item = list[0] || {};
                              form.setFieldValue('out_org_name', item.org_name);
                              form.setFieldValue('out_org_id', item.org_id);
                              console.log(form.getFieldValue('store_id'));
                            }}
                            onChange={(e: any) => {
                              console.log(
                                e,
                                'vvvvv',
                                form.getFieldValue('store_id'),
                              );
                            }}
                            width={180}
                          />
                        </XlbBasicForm.Item>
                        {enable_organization ? (
                          <XlbBasicForm.Item
                            label="调出组织"
                            name="out_org_name"
                          >
                            <XlbInput disabled style={{ width: '180px' }} />
                          </XlbBasicForm.Item>
                        ) : null}
                        <XlbBasicForm.Item
                          label="调出仓库"
                          name="storehouse_id"
                        >
                          <XlbSelect
                            style={{ width: 180 }}
                            disabled={
                              (fid !== 1 && info.state !== 'INIT') ||
                              (fid !== 1 &&
                                info.state == 'INIT' &&
                                rowData.length !== 0 &&
                                !rowData[0]?.newRow) ||
                              (fid === 1 &&
                                rowData.length !== 0 &&
                                !rowData[0]?.newRow)
                            }
                          >
                            {stockList.map((v, i) => {
                              return (
                                <XlbSelect.Option key={i} value={v.value}>
                                  {v.label}
                                </XlbSelect.Option>
                              );
                            })}
                          </XlbSelect>
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="单据号" name="fid">
                          <XlbInput
                            size="small"
                            style={{ width: '180px' }}
                            disabled
                            suffix={
                              <SearchOutlined style={{ color: '#F5F5F5' }} />
                            }
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item
                          label="商品部门"
                          name="item_dept_names"
                        >
                          <XlbInput
                            disabled
                            size="small"
                            suffix={
                              <SearchOutlined style={{ color: '#F5F5F5' }} />
                            }
                            style={{ width: '180px' }}
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="调出日期" name="operate_date">
                          <XlbDatePicker
                            style={{ width: '180px' }}
                            disabled={info.state !== 'INIT' || ban}
                          />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="付款日期" name="payment_date">
                          <XlbDatePicker
                            style={{ width: '180px' }}
                            disabled={info.state !== 'INIT' || ban}
                          />
                        </XlbBasicForm.Item>
                        <Tooltip
                          title={
                            <div>
                              {form
                                .getFieldValue('request_order_fids')
                                ?.map((v: any) => <p key={v}>{v}</p>)}
                            </div>
                          }
                        >
                          <XlbBasicForm.Item
                            label="门店补货单"
                            name="request_order_fids"
                          >
                            <XlbInput
                              style={{ width: '180px' }}
                              readOnly
                              disabled={info.state !== 'INIT' || fid !== 1}
                            />
                          </XlbBasicForm.Item>
                        </Tooltip>
                        <XlbBasicForm.Item label="留言备注" name="memo">
                          <XlbInput
                            maxLength={200}
                            style={{ width: '740px', height: 26 }}
                            disabled={info.state !== 'INIT' || ban}
                          />
                        </XlbBasicForm.Item>
                      </div>
                      <div>
                        {info?.state && (
                          <div
                            style={{
                              width: '150px',
                              flexBasis: '150px',
                              display: 'flex',
                              justifyContent: 'center',
                            }}
                          >
                            {info?.state === 'INIT' ? (
                              <img
                                src={
                                  'https://hxl-web-dev.oss-cn-hangzhou.aliyuncs.com/react_web/static/init.4918d249.png'
                                }
                                width={86}
                                height={78}
                              />
                            ) : (
                              <img
                                src={
                                  'https://hxl-web-dev.oss-cn-hangzhou.aliyuncs.com/react_web/static/checking.bdc999ee.png'
                                }
                                width={86}
                                height={78}
                              />
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ),
                },
                {
                  label: '其他信息',
                  key: 'otherInfo',
                  children: (
                    <>
                      <div style={{ marginTop: 12 }} className="row-flex">
                        <XlbBasicForm.Item label="制单人" name="create_by">
                          <XlbInput style={{ width: '180px' }} readOnly />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="制单时间" name="create_time">
                          <XlbInput style={{ width: '180px' }} readOnly />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="审核人" name="audit_by">
                          <XlbInput style={{ width: '180px' }} readOnly />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item label="审核时间" name="audit_time">
                          <XlbInput style={{ width: '180px' }} readOnly />
                        </XlbBasicForm.Item>
                      </div>
                      <div className="row-flex">
                        <XlbBasicForm.Item label={'修改人'} name={'update_by'}>
                          <XlbInput style={{ width: '180px' }} readOnly />
                        </XlbBasicForm.Item>
                        <XlbBasicForm.Item
                          label={'修改时间'}
                          name={'update_time'}
                        >
                          <XlbInput style={{ width: '180px' }} readOnly />
                        </XlbBasicForm.Item>
                        {Object.keys(WmsInfo)?.length ? (
                          <div className={styles.data_box}>
                            <span>总件数：</span>
                            {WmsInfo.total_quantity}
                            <span>件数：</span>
                            {WmsInfo.quantity}
                            <span>拆零件数：</span>
                            {WmsInfo.box_quantity}
                          </div>
                        ) : null}
                      </div>
                    </>
                  ),
                },
              ]}
            ></XlbTabs>
            <div className={'button_box row-flex'} style={{ padding: 0 }}>
              <XlbButton.Group>
                {hasAuth(['调出单', '编辑']) && (
                  <XlbButton
                    type="primary"
                    label="批量添加"
                    disabled={!in_store_id || !(info.state === 'INIT')}
                    onClick={() => handleDialogClick()}
                    icon={<XlbIcon name="jia" />}
                  />
                )}
                {hasAuth(['调出单', '导入']) && (
                  <XlbButton
                    type="primary"
                    label="导入"
                    disabled={!in_store_id || !(info.state === 'INIT')}
                    onClick={() => {
                      importItem();
                    }}
                    icon={<XlbIcon name="daoru" />}
                  />
                )}

                {hasAuth(['调出单', '编辑']) ? (
                  <XlbButton
                    label="单据选择"
                    type="primary"
                    disabled={
                      isLoading ||
                      ban ||
                      !form?.getFieldValue('in_store_id') ||
                      (form.getFieldValue('request_order_fids') &&
                        form.getFieldValue('request_order_fids')[0]) ||
                      (rowData.length &&
                        rowData.findIndex(
                          (v) =>
                            !v.newRow &&
                            (!v?.receive_order_fids ||
                              !v?.receive_order_fids?.length),
                        ) !== -1)
                    }
                    onClick={() => {
                      if (
                        form.getFieldValue('in_store_name') ===
                        form.getFieldValue('store_name')
                      ) {
                        XlbTipsModal({
                          tips: `调出门店与调入门店不可相同！`,
                        });

                        return;
                      }
                      setSelectOrderVisible(true);
                    }}
                  />
                ) : null}
              </XlbButton.Group>
            </div>
          </XlbBasicForm>
        </div>
      )}

      <div className={styles.contractTabs}>
        <XlbTabs
          defaultActiveKey={'detailTab'}
          onChange={(key) => setTabsKey(key)}
          items={[
            {
              label: '商品明细',
              key: 'detailTab',
              children: (
                <XlbShortTable
                  showSearch
                  url={'/erp/hxl.erp.deliveryoutorder.item.page'}
                  data={{
                    store_id: form?.getFieldValue('store_id')?.toString(),
                    in_store_id: getArrayChild(
                      form.getFieldValue('in_store_id')?.[0],
                    )?.toString(),
                    company_id: LStorage.get('userInfo')?.company_id,
                    operator_store_id: form
                      ?.getFieldValue('store_id')
                      ?.toString(),
                    storehouse_id: form
                      .getFieldValue('storehouse_id')
                      ?.toString(),
                    out_store_id: form.getFieldValue('store_id')?.toString(),
                  }}
                  disabled={info.state !== 'INIT' || !in_store_id}
                  tableKey={tableKey}
                  isLoading={isLoading}
                  pagin={pagin}
                  pageChange={pageChange}
                  onSelectRow={(e, v) => {
                    if (v.length === 0) {
                      const data = JSON.parse(JSON.stringify(rowData));
                      data[data.length - 1]._empty = true;
                      setRowData(data);
                    }
                  }}
                  placeholder="请输入关键字后按Enter进行搜索"
                  onChangeData={(e) => {
                    setKeepData(true);
                    const data = JSON.parse(JSON.stringify(e));
                    setRowData(data);
                    // setChooseList(value)
                    if (
                      !forbidenClickRowEvent.current &&
                      tabsKey === 'detailTab'
                    ) {
                      setRowData(data);
                    }
                  }}
                  onChangeSorts={() => {
                    setKeepData(false);
                  }}
                  afterPopupSelect={(oldArr) => {
                    // 选择的数据转换
                    const [item0] = oldArr;
                    setUnits(item0);
                    console.log(item0, 'item0item0item0');
                    return [
                      {
                        ...item0,
                        fid: item0?.id,
                        _edit: false,
                        ratio: item0.ratio || 1,
                        item_name: item0?.name,
                        newRow: false,
                        bar_code: item0.bar_code,
                        item_bar_code: item0.bar_code,
                        item_code: item0.code,
                        item_id: item0.id || item0.item_id,
                        basic_unit: item0.basic_unit || item0.unit,
                        tax_money: item0?.tax_money || 0,
                      },
                    ];
                  }}
                  isFold={isFold}
                  keepDataSource={keepData}
                  // primaryKey={'fids'}
                  selectMode="single"
                  footerDataSource={footerData}
                  selectedNum={chooseList.length}
                  popoverPrimaryKey="id"
                  repeatKey="name"
                  dataSource={rowData}
                  columns={itemArrdetail}
                  primaryKey="fid"
                />
              ),
            },
            {
              label: '商品汇总',
              key: 'totalTab',
              disabled: edit,
              children: (
                <>
                  <XlbTable
                    tableKey={tableKey}
                    isLoading={isLoading}
                    pagin={pagin}
                    pageChange={pageChange}
                    list={rowData}
                    onChangeData={(e) => {
                      console.log(e);
                    }}
                    onChangeSorts={() => {
                      setKeepData(false);
                    }}
                    isFold={isFold}
                    keepDataSource={keepData}
                    selectMode="single"
                    footerData={footerData}
                    selectedNum={chooseList.length}
                    popoverPrimaryKey="item_name"
                    repeatKey="item_name"
                    dataSource={summaryData}
                    columns={itemArr}
                    primaryKey="fid"
                  />
                </>
              ),
            },
          ]}
        ></XlbTabs>
        <div
          style={{
            position: 'absolute',
            left: 97,
            top: 0,
            width: 66,
            height: 44,
            visibility: !edit ? 'hidden' : 'visible',
          }}
          onClick={() =>
            message.warning('请保存商品明细表内容后查看商品汇总！')
          }
        />
      </div>
      <SelectOrder
        handleCancel={handleCancel}
        add={addOrder}
        visible={SelectOrderVisible}
      ></SelectOrder>
    </div>
  );
};

export default DeliveryInOrderItem;
