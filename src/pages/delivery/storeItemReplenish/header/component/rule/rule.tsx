import { XlbModal, XlbTipsModal } from '@xlb/components';
import { safeMath } from '@xlb/utils';
import { Form, Input, message, Select, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { Rules } from '../../../data';
import { allQuery } from '../../../server';
import style from './rule.less';
const { Option } = Select;
const Rule = (props: any) => {
  const {
    visible,
    handleCancel,
    setRowData,
    requestData,
    setPagin,
    setIsLoading,
    setruleLoding,
  } = props;
  const [form] = Form.useForm();
  const [noClick, setNoClick] = useState<any>({ value: false });
  const [loding, setloding] = useState(false);

  const handleOk = async () => {
    setloding(true);
    let regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (
      !regPos.test(form.getFieldValue('number1')) ||
      !regPos.test(form.getFieldValue('number3')) ||
      Math.floor(form.getFieldValue('number1')) !=
        form.getFieldValue('number1') ||
      form.getFieldValue('number1') < 0 ||
      form.getFieldValue('number3') < 0 ||
      form.getFieldValue('number3') > 99
    ) {
      XlbTipsModal({
        tips: '请按照提示规则正确输入数值',
      });
      return false;
    }
    if (!noClick.value) {
      let data = await requestData();
      if (!!!data) {
        XlbTipsModal({
          tips: '最近【】天销量支持录入>=0的整数',
        });
        return false;
      }
      noClick.value = true;
      setIsLoading(true);
      const res = await allQuery({ ...data, page_size: 10000, page_num: 1 });
      setIsLoading(false);
      if (res.code === 0) {
        setruleLoding(false);
        if (res.data.total_elements >= 10000) {
          XlbTipsModal({
            tips: '通过调整规则一次最多调整10000条数据',
          });
          return false;
        }
        res.data.content.forEach((item: any, index: any) => {
          item.point = item.point.toFixed(3);
          item.upper_limit = item.upper_limit.toFixed(3);
          item.quantity = item.quantity.toFixed(3);
          item.base_stock_quantity = item.base_stock_quantity.toFixed(3);
          item.sale_quantity = item.sale_quantity.toFixed(3);
          item.avg_sale_quantity = item.avg_sale_quantity.toFixed(3);
          /** Number(
            item.avg_sale_quantity *
            (Number(form.getFieldValue('number1')) + form.getFieldValue('number2') ? Number(item.purchase_period) : 0) *
            form.getFieldValue('number3') +
            Number(item[form.getFieldValue('rule_name2')] ?? 0)).toFixed(3) */
          item[form.getFieldValue('rule_name1')] = safeMath.add(
            safeMath.multiply(
              safeMath.multiply(
                item.avg_sale_quantity,
                safeMath.add(
                  Number(form.getFieldValue('number1')),
                  form.getFieldValue('number2')
                    ? Number(item.purchase_period)
                    : 0,
                ),
              ),
              form.getFieldValue('number3'),
            ),
            Number(item[form.getFieldValue('rule_name2')] ?? 0),
          );
        });
        setRowData(res.data.content);
        // let index = 0;
        // Promise.resolve().then((i) => {
        //   ruleData.splice(0, ruleData.length)
        //   setRuleData([...[]])
        // }).then(() => {
        //   while (index < res.data.content.length) {
        //     ruleData.push(res.data.content.slice(index, index += 200));
        //   }
        //   setRuleData([...ruleData])
        //   setRowData(ruleData[0])
        setPagin({
          pageSize: 200,
          pageNum: 1,
          total: res.data.content.length,
        });
        if (res.data.total_elements < 10000) {
          handleCancel();
          form.resetFields();
        }
        // })
      }
    } else if (noClick.value) {
      message.warning('Loading...');
    }

    setloding(false);
  };
  useEffect(() => {
    form.setFieldsValue({
      rule_name1: 'point',
      rule_name2: 'point',
      number1: 7,
      number2: 1,
      number3: '1.0',
    });
    noClick.value = false;
    setNoClick(noClick);
  }, [visible]);
  return (
    <XlbModal
      className={style.mmodal}
      title={'调整规则'}
      centered
      visible={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields(), handleCancel();
      }}
      width={800}
      confirmLoading={loding}
    >
      <Form form={form} style={{ margin: '30px 0 15px 0' }}>
        <div className={style.box}>
          <p className={style.title}>调整规则</p>
          <Form.Item name={'rule_name1'}>
            <Select size="small" style={{ width: 110 }} defaultValue={'point'}>
              {Rules.map((item) => (
                <Option value={item.value}>{item.label}</Option>
              ))}
            </Select>
          </Form.Item>
          <span>=日均销量x(</span>
          <Tooltip placement="top" title={'可录入>=0的整数,最大99!'}>
            <Form.Item
              name={'number1'}
              rules={[{ required: true, message: '' }]}
            >
              <Input
                size="small"
                maxLength={2}
                onFocus={(e) => e.target.select()}
              />
            </Form.Item>
          </Tooltip>
          <span>+</span>
          <Form.Item name={'number2'}>
            <Select size="small" style={{ width: 110 }} defaultValue={0}>
              <Option value={1}>{'交货周期'}</Option>
              <Option value={0}>{0}</Option>
            </Select>
          </Form.Item>
          <span>)x安全系数</span>
          <Tooltip placement="top" title={'可录入>=0的数值,最大99!'}>
            <Form.Item
              name={'number3'}
              rules={[{ required: true, message: '' }]}
            >
              <Input size="small" onFocus={(e) => e.target.select()} />
            </Form.Item>
          </Tooltip>
          <span>+</span>
          <Form.Item name={'rule_name2'}>
            <Select size="small" style={{ width: 110 }} defaultValue={'point'}>
              {Rules.map((item) => (
                <Option value={item.value}>{item.label}</Option>
              ))}
              <Option value={0}>{0}</Option>
            </Select>
          </Form.Item>
        </div>
      </Form>
    </XlbModal>
  );
};
export default Rule;
