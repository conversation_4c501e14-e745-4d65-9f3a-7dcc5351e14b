.table_box {
  :global .art-table-body {
    min-height: calc(100vh - 170px);
  }
}
.table_fold_box {
  :global .art-table-body {
    min-height: calc(100vh - 200px);
  }
}

.button_box {
  padding: 6px 0 0 0;
  border-bottom: 1px solid @color_line2;
}
.stockSearch {
  .ant-layout-sider {
    background-color: #fff;
  }
  .ant-layout {
    background-color: #fff;
  }
}
.providerWrap {
  height: calc(100vh - 78px);
  padding: 12px;
  background-color: #f2f3f5;
  :global {
    .xlb-pro-pageContainer {
      padding-top: 12px !important;
    }
  }
  .innerProvider {
    height: 100%;
    overflow: hidden;
    background-color: #fff;
    border-radius: 4px;
  }
}
