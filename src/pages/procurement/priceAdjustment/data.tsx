import { columnWidthEnum } from '@/constants';
import {
  SearchFormType,
  XlbTableColumnProps,
  XlbTooltip,
} from '@xlb/components';
import { LStorage } from '@xlb/utils';
const userInfo = LStorage.get('userInfo');

export const statusTypes = [
  { label: '制单', value: 'INIT', type: 'info' },
  { label: '审核中', value: 'AUDIT', type: 'warning' },
  { label: '已通过', value: 'PASS', type: 'success' },
  { label: '已拒绝', value: 'DENY', type: 'danger' },
];
export const orderTypes = [
  { label: '供应商申请', value: 'SUPPLIER_APPLY' },
  { label: '竞品采价申请', value: 'COMPETITIVE_PRICE' },
  { label: '老品新开调价', value: 'OLD_NEW_PRICE' },
];
export const replyTypeOptions = [
  {
    label: '成本分析不合理',
    value: 'COST_ANALYSIS',
  },
  {
    label: '合同约束',
    value: 'CONTRACTUALLY_BOUND',
  },
  {
    label: '市场竞争',
    value: 'MARKET_COMPETITION',
  },
  {
    label: '不符合采购策略',
    value: 'PROCUREMENT_STRATEGY',
  },
  {
    label: '服务质量问题',
    value: 'SERVICE_QUALITY_PROBLEM',
  },
];

export const formList: SearchFormType[] = [
  {
    width: 372,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'create_date',
    allowClear: false,
  },
  {
    label: '所属组织',
    name: 'org_ids',
    type: 'select',
    multiple: true,
    disabled: !!userInfo?.supplier?.id,
    options:
      userInfo?.query_orgs
        ?.filter((v: any) => v.level == 2)
        ?.map((item: any) => ({ ...item, label: item.name, value: item.id })) ||
      [],
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isMultiple: true,
      data: {
        isNotShowParentSupplier: true,
        status: 1,
        supplier_identities: ['CHILD', 'NORMAL'],
      },
    },
  },
  {
    label: '审批状态',
    name: 'state',
    type: 'select',
    options: statusTypes,
  },
  {
    label: '制单人',
    name: 'create_by',
    type: 'input',
  },
  {
    label: '生效时间',
    name: 'effect_time',
    type: 'datePicker',
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    placeholder: '请输入调价单据',
  },
  {
    label: '调价类型',
    name: 'order_type',
    type: 'select',
    options: orderTypes,
  },
];

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '调价单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    align: 'center',
    features: { sortable: true },
    lock: true,
  },
  {
    name: '所属组织',
    code: 'org_name',
    width: 120,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '供应商',
    code: 'supplier_name',
    align: 'center',
    width: 200,
    features: { sortable: true },
    render: (text, record) => {
      const names = record.suppliers?.map((v: any) => v.supplier_name);
      return (
        <div style={{ display: 'flex', flexDirection: 'row', gap: '8px' }}>
          <div style={{ flexShrink: 0 }} className="info">
            {names?.length ? names[0] : ''}
          </div>
          {names?.length > 1 && (
            <XlbTooltip title={names?.slice(1).join(',')}>
              <span
                style={{
                  width: 32,
                  height: 20,
                  background: '#f2f3f5',
                  lineHeight: '20px',
                  color: '#86909c',
                  textAlign: 'center',
                  display: 'inline-block',
                  borderRadius: '4px',
                  flexShrink: 0,
                }}
              >
                +{names.length - 1}
              </span>
            </XlbTooltip>
          )}
        </div>
      );
    },
  },
  {
    name: '商品数',
    code: 'item_count',
    width: 88,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '制单人',
    code: 'create_by',
    align: 'center',
    width: 116,
    features: { sortable: true },
  },
  {
    name: '审核人',
    code: 'audit_by',
    align: 'center',
    width: 116,
    features: { sortable: true },
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: 180,
    align: 'center',
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '生效时间',
    code: 'effect_time',
    width: 180,
    align: 'center',
    features: { sortable: false, format: 'TIME' },
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 180,
    align: 'center',
    features: { sortable: false, format: 'TIME' },
  },
  {
    name: '处理时间',
    code: 'pass_time',
    width: 180,
    align: 'center',
    features: { sortable: false, format: 'TIME' },
  },
  {
    name: '调价类型',
    code: 'order_type',
    width: 116,
    features: { sortable: true },
    align: 'center',
    render: (text) => {
      return <span>{orderTypes?.find((v) => v.value === text)?.label}</span>;
    },
  },
  {
    name: '备注',
    code: 'memo',
    width: 312,
    align: 'center',
    features: { sortable: true },
  },
  {
    name: '审批状态',
    code: 'state',
    width: 100,
    align: 'center',
    features: { sortable: true },
    lock: true,
    render: (text) => {
      const item = statusTypes?.find((v) => v.value === text);
      return <span className={item?.type}>{item?.label}</span>;
    },
  },
];
