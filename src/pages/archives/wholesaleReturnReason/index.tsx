import { type FC } from 'react'
import { tableColumn } from './data'
import { hasAuth } from '@/utils/kit'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { XlbProPageContainer } from '@xlb/components'

const WholeSaleReturnReason = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [{ id: 'keyword', name: 'keyword', label: '关键字' }],
        initialValues: {
          type: 'WHOLESALE_RETURN'
        }
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.reason.find',
        tableColumn: tableColumn,
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: false,
        immediatePost: true
      }}
      deleteFieldProps={{
        name: '删除',
        showField: 'name',
        url: hasAuth(['批发退货原因', '删除']) ? '/erp/hxl.erp.reason.delete' : ''
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['批发退货原因', '编辑']) ? '/erp/hxl.erp.reason.save' : '',
        beforePost: (data) => {
          return {
            ...data,
            type: 'WHOLESALE_RETURN'
          }
        }
      }}
      details={{
        mode: 'modal',
        isCancel: true,
        width: 450,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>
        },
        hiddenSaveBtn: true,
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap.otherIncomeExpensesName,
                  itemSpan: 24,
                  label: '批发退货原因',
                  rules: [{ required: true, message: '批发退货原因不能为空' }],
                  fieldProps: { maxLength: 20, width: '100%' }
                  // width:180
                },
                {
                  id: ErpFieldKeyMap.erpIsDefault,
                  label: '附件必传',
                  itemSpan: 24,
                  fieldProps: {
                    options: [{ label: '附件必传', value: 'file_required' }]
                  }
                }
              ]
            }
          }
        ],
        updateFieldProps: {
          url: '/erp/hxl.erp.reason.update'
        }
      }}
    />
  )
}

export default WholeSaleReturnReason
