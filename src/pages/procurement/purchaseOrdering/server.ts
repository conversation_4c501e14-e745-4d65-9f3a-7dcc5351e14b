import { XlbFetch } from '@xlb/utils';
import { ChartsUnits } from './component/data';

// 生成采购订单
const createPurchaseOrder = async (data: any) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.purchasereplenishanalysis.purchaseorder.create',
    data,
  );
};
const afterCreatePurchaseOrder = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.purchase.order.analysis.log', data);
};
const switchSupplier = async (data: any) => {
  return await XlbFetch.post('/erp/hxl.erp.storeitemsupplier.switch', data);
};
const supplierrebatepresent = async (data: any) => {
  return await XlbFetch.post(
    '/scm/hxl.scm.suppliercontractorderpresent.find',
    data,
  );
};

interface AnalysisForecastParams {
  store_id: number;
  item_id: number;
  supplier_id: number;
  type: ChartsUnits;
}
const getAnalysisForecast = async (data: AnalysisForecastParams) => {
  return await XlbFetch.post(
    '/erp/hxl.erp.purchase.order.analysis.forecast',
    data,
  );
};

export default {
  createPurchaseOrder,
  afterCreatePurchaseOrder,
  switchSupplier,
  supplierrebatepresent,
  getAnalysisForecast,
};
