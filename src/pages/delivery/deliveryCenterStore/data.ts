import { columnWidthEnum } from '@/data/common/constant'
import type { ArtColumn } from 'ali-react-table'

export const formList: any[] = [
  {
    label: '门店代码',
    value: 'store_code',
    type: 'input',
    clear: true,
    check: true
  },
  {
    label: '门店名称',
    value: 'store_name',
    type: 'input',
    clear: true,
    check: true
  },
  {
    label: '是否默认',
    value: 'default_flag',
    type: 'select',
    clear: true,
    check: true,
    options: [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]
  }
]

export const tableList: ArtColumn[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '操作',
    code: 'operation',
    align: 'center',
    width: 60
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 160,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '门店',
    code: 'store_name',
    width: 280,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '上游配送中心',
    code: 'center_store_name',
    width: 160,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '是否默认',
    code: 'default_flag',
    width: 100,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: ''
  }
]

export const deveveryPriceValues = [
  { label: '上游配送中心配送价', value: 'UPSTREAM_DELIVERY_PRICE' },
  { label: '中转配送中心配送价', value: 'TRANSIT_DELIVERY_PRICE' },
  { label: '共享配送中心配送价', value: 'SHARE_DELIVERY_PRICE' }
]

export const keyMap = {
  UPSTREAM_DELIVERY_PRICE: 'center_store_id',
  TRANSIT_DELIVERY_PRICE: 'transit_store_id',
  SHARE_DELIVERY_PRICE: 'share_store_id'
}

export const batch = [
  { label: '上游配送中心', value: 'center_store_id' },
  { label: '中转配送中心', value: 'transit_store_id' },
  { label: '共享配送中心', value: 'share_store_id' },
  { label: '配送价取值', value: 'delivery_price_val' }
]
