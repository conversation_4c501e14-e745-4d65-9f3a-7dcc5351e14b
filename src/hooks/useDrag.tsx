const useDrag = (dragger: any, draggerBox: any) => {
  const containerList = document.querySelectorAll(draggerBox);
  const titleList = document.querySelectorAll(dragger);

  let qutoContainer: any
  let title: any
  if (containerList?.length > 0) {
    qutoContainer = containerList[containerList.length - 1]
    title = titleList[titleList.length - 1]
  }


    if (title) {
      title.onmousedown = (eventDown: any) => {
        eventDown.preventDefault()
        const left = qutoContainer.offsetLeft;
        const top = qutoContainer.offsetTop;
        const downX = eventDown.clientX;
        const downY = eventDown.clientY;
        document.onmousemove = (eventMove) => {
          eventMove.stopPropagation();
          const moveX = eventMove.clientX - downX;
          const moveY = eventMove.clientY - downY;
          qutoContainer.style.left = `${left + moveX}px`;
          qutoContainer.style.top = `${top + moveY}px`;
        };
      };
      document.onmouseup = () => {
        document.onmousemove = null;
      };
    }
}

export default useDrag
