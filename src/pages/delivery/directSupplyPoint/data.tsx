import { XlbTableColumnProps } from '@xlb/components';
// 列表数据
export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 65,
    align: 'center',
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '配送中心',
    code: 'center_store_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '配送中心仓',
    code: 'storehouse_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '门店',
    code: 'store_name',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '添加人',
    code: 'update_by',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '添加时间',
    code: 'update_time',
    width: 200,
    features: { sortable: true, format: 'TIME' },
  },
];
