import {
  FileList,
  SearchFormType,
  XlbBaseUpload,
  XlbBasicForm,
  XlbBlueBar,
} from '@xlb/components';
import { Row, Typography } from 'antd';
import get from 'lodash/get';
import { FC, ReactNode } from 'react';
import './index.less';

import { DetailSchema } from './type';
const { Text } = Typography;
export interface XlbDetailProps {
  /**
   * 详情页schema
   */
  schema: DetailSchema[];
  /**
   * 详情页数据
   */
  data?: any;
}

const { Item } = XlbBasicForm;

const XlbDetail: FC<XlbDetailProps> = ({ schema, data }) => {
  const renderForm = (formLis: SearchFormType[]): ReactNode => {
    return (
      <Row className="details-row">
        {formLis.map((item, index) => {
          const {
            label,
            name,
            type,
            uploadName,
            width = 250,
            style = { width: 296 },
            render,
            hidden,
            accept,
            ...formItemRest
          } = item;

          let value = undefined;
          if (data) {
            value = get(data, name);
          }
          if (hidden) {
            return null;
          }
          if (typeof render === 'function') {
            return (
              <Item
                key={`${label}_${index}`}
                labelAlign="left"
                label={label}
                className="xlb-detail-form-item"
                style={style}
                {...formItemRest}
              >
                <Text
                  ellipsis={{ tooltip: render(value, data, item) }}
                  style={{ width: width }}
                >
                  {render(value, data, item)}
                </Text>
              </Item>
            );
          }

          if (type === 'upload') {
            let fileList: FileList[] | string[] | undefined;
            if (value) {
              if (!Array.isArray(value)) {
                fileList = [value];
              } else {
                fileList = value;
              }
            }
            return (
              <Item
                key={`${label}_${index}`}
                labelAlign="left"
                label={label}
                className="xlb-detail-form-item details-form-item-upload"
                style={style}
                {...formItemRest}
              >
                <div style={{ width: width }}>
                  <XlbBaseUpload
                    mode="look"
                    showUpload={false}
                    listType="text"
                    accept={accept}
                    width={width}
                    {...formItemRest}
                    name={uploadName}
                    columnNum={1}
                    // disabled
                    fileList={fileList}
                  />
                </div>
              </Item>
            );
          }
          // if (type === 'file') {
          //   let fileList: FileList[] | string[] | undefined;
          //   if (value) {
          //     if (!Array.isArray(value)) {
          //       fileList = [value];
          //     } else {
          //       fileList = value;
          //     }
          //   }
          //   return (
          //     <Item
          //       key={`${label}_${index}`}
          //       labelAlign="left"
          //       label={label}
          //       className="xlb-detail-form-item details-form-item-upload"
          //       style={style}
          //       {...formItemRest}
          //     >
          //       <div style={{ width: width }}>
          //         <XlbUploadFile
          //           width={width}
          //           {...formItemRest}
          //           name={uploadName}
          //           columnNum={1}
          //           disabled
          //           fileList={fileList}
          //         />
          //       </div>
          //     </Item>
          //   );
          // }

          return (
            <Item
              key={`${label}_${index}`}
              labelAlign="left"
              label={label}
              className="xlb-detail-form-item"
              style={style}
              {...formItemRest}
            >
              <Text style={{ width: width }} ellipsis={{ tooltip: value }}>
                {value}
              </Text>
            </Item>
          );
        })}
      </Row>
    );
  };

  const renderSchema = (schemaSetting: DetailSchema, index: number) => {
    const { title, children, subTitle, formList } = schemaSetting;
    let titleNode: ReactNode = null;
    let subTitleNode: ReactNode = null;
    let formNode: ReactNode = null;

    if (title) {
      titleNode = <XlbBlueBar title={title} />;
    }

    if (subTitle) {
      subTitleNode = (
        <XlbBlueBar.SubTitle style={{ marginTop: 12 }} title={subTitle} />
      );
    }

    if (Array.isArray(formList)) {
      formNode = renderForm(formList);
    }

    return (
      <div key={index}>
        {titleNode}
        {subTitleNode}
        {formNode}
        {children?.length ? children.map(renderSchema) : null}
      </div>
    );
  };

  return (
    <div className="details">
      <div>
        <XlbBasicForm
          style={{ display: 'block' }}
          className={'xlb-detail-form-content'}
        >
          {schema.map(renderSchema)}
        </XlbBasicForm>
      </div>
    </div>
  );
};

export default XlbDetail;
