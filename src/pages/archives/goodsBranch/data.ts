import type { XlbTableColumnProps } from '@xlb/components'

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center'
  },
  {
    name: '商品部门名称',
    code: 'name',
    width: 180,
    features: { sortable: true, details: true }
  }
]

export interface ItemDeptResDTO {
  /**
   * @name 序号
   */
  id?: number

  /**
   * @name 商品部门名称
   */
  name?: string
}
