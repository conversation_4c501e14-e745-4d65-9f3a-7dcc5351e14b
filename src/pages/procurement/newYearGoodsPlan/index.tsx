import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils';
import {
  XlbButton,
  XlbIcon,
  XlbModalForm,
  XlbProPageContainer,
} from '@xlb/components';
import dayjs from 'dayjs';
import { FC } from 'react';
import { detailsFormList, tableColumn } from './data';

const NewYearGoodsPlan: FC = () => {
  const tansferRangeToTime = (formValues: Record<any, any>) => {
    const range = formValues?.range;
    let begin_time: string | undefined = undefined,
      end_time: string | undefined = undefined;
    // 时间转换
    if (Array.isArray(range)) {
      [begin_time, end_time] = range;
      begin_time = dayjs(begin_time)
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss');
      end_time = dayjs(end_time).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }
    formValues.begin_time = begin_time;
    formValues.end_time = end_time;
    return formValues;
  };

  return (
    <XlbProPageContainer
      searchFieldProps={{
        order: 0,
        initialValues: {
          create_date: [
            dayjs().format('YYYY-MM-DD HH:mm:ss'),
            dayjs().format('YYYY-MM-DD HH:mm:ss'),
          ],
        },
        formList: [
          {
            id: 'dateCommon',
          },
          {
            id: 'commonInput',
            name: 'name',
            label: '计划名称',
          },
          {
            id: 'statusByBoolean',
            name: 'enable',
            label: '状态',
          },
          {
            id: ErpFieldKeyMap.purchasePlanOrg,
            name: 'org_ids',
          },
        ],
      }}
      addFieldProps={{
        url: hasAuth(['采购计划', '编辑'])
          ? '/erp/hxl.erp.purchaseplan.save'
          : '',
        beforePost(formValues: Record<any, any>) {
          return tansferRangeToTime(formValues);
        },
        order: 10,
      }}
      details={{
        hiddenSaveBtn: true,
        primaryKey: 'id',
        initialValues: {
          enable: true,
        },
        saveFieldProps: {
          beforePost(formValues: Record<any, any>) {
            return tansferRangeToTime(formValues);
          },
          url: hasAuth(['采购计划', '编辑'])
            ? '/erp/hxl.erp.purchaseplan.save'
            : '',
        },
        updateFieldProps: {
          beforePost(formValues: Record<any, any>) {
            return tansferRangeToTime(formValues);
          },
          url: hasAuth(['采购计划', '编辑'])
            ? '/erp/hxl.erp.purchaseplan.update'
            : '',
        },
        mode: 'modal',
        isCancel: true,
        title: (formValues?: Record<any, any>) =>
          formValues?.id ? '编辑' : '新增',
        width: 500,
        itemSpan: 24,
        formList: detailsFormList,
      }}
      extra={({ selectRow, fetchData }) => {
        return hasAuth(['采购计划', '编辑']) ? (
          <XlbModalForm
            title={'编辑'}
            url={'/erp/hxl.erp.purchaseplan.update'}
            beforePost={(formValues: Record<any, any>) => {
              return tansferRangeToTime(formValues);
            }}
            isCancel={true}
            formList={detailsFormList}
            formProps={{
              itemSpan: 24,
              primaryKey: 'id',
              initialValues: {
                ...(selectRow?.[0] || {}),
                range: [
                  selectRow?.[0]?.begin_time,
                  selectRow?.[0]?.end_time,
                ].filter((item) => item),
              },
            }}
            onOk={() => {
              fetchData?.();
            }}
          >
            <XlbButton
              icon={<XlbIcon name="xiugai1" />}
              disabled={!selectRow?.length}
              style={{ order: 20 }}
              type="primary"
            >
              编辑
            </XlbButton>
          </XlbModalForm>
        ) : null;
      }}
      exportFieldProps={{
        url: hasAuth(['采购计划', '导出'])
          ? '/erp/hxl.erp.purchaseplan.export'
          : '',
        fileName: '采购计划.xlsx',
        order: 30,
      }}
      tableFieldProps={{
        url: hasAuth(['采购计划', '查询'])
          ? '/erp/hxl.erp.purchaseplan.page'
          : '',
        tableColumn: tableColumn,
        selectMode: 'single',
        immediatePost: true,
      }}
    />
  );
};

export default NewYearGoodsPlan;
