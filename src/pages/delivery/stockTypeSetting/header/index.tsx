import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import type {
  ContextState,
  XlbProPageContainerRef,
  XlbProPageModalRef,
} from '@xlb/components';
import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbProPageModal,
} from '@xlb/components';
import { useRef, useState } from 'react';
import Item from '../item/index';

const Index = () => {
  const { enable_organization } = useBaseParams((state) => state);
  const refreshRef = useRef<(page?: number) => void>(() => {});
  const [record, setRecord] = useState<any>({});
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbProPageContainerRef>(null);

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'name':
        item.render = (value: string, record: any) => {
          return (
            <span
              className="link cursors"
              onClick={(e) => {
                e.stopPropagation();
                // go('/xlb_erp/stockTypeSetting/item', { ...record });
                setRecord(record);
                pageModalRef.current?.setOpen(true);
              }}
            >
              {value}
            </span>
          );
        };
        break;
      case 'enable':
        item.render = (value: any, record: any, index: number) => {
          return (
            <span className={value ? 'success' : 'danger'}>
              {value ? '是' : '否'}
            </span>
          );
        };

      default:
        break;
    }
    return item;
  };

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer
        ref={pageConatainerRef}
        searchFieldProps={{
          formList: [
            { id: 'keyword', name: 'keyword', label: '关键字' },
            {
              id: ErpFieldKeyMap.erpOrgIdsLevel2,
              name: 'org_ids',
              label: '组织',
              hidden: !enable_organization,
            },
          ],
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.preparetype.find',
          tableColumn: () => {
            return [
              {
                name: '序号',
                code: '_index',
                width: 50,
                align: 'center',
              },
              {
                name: '备货类型',
                code: 'name',
                width: 220,
                features: { sortable: true },
              },
              {
                name: '启用',
                code: 'enable',
                width: 74,
              },
              {
                name: '组织',
                code: 'org_name',
                hidden: !enable_organization,
                width: 220,
              },
            ]?.map((v) => tableRender(v));
          },
          selectMode: 'single',
          keepDataSource: false,
          showColumnsSetting: false,
          immediatePost: true,
        }}
        deleteFieldProps={{
          order: 2,
          name: '删除',
          url: hasAuth(['备货类型设置', '编辑'])
            ? '/erp/hxl.erp.preparetype.delete'
            : '',
          params: (selectRowKeys: string[]) => {
            return { ids: selectRowKeys };
          },
        }}
        extra={(context: ContextState) => {
          refreshRef.current = context.fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['备货类型设置', '编辑']) && (
                <XlbButton
                  style={{ order: 1 }}
                  type="primary"
                  label="新增"
                  onClick={() => {
                    // go('/xlb_erp/stockTypeSetting/item', {
                    //   refresh: context.fetchData,
                    // });
                    setRecord({ id: -1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<XlbIcon size={16} name="jia" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      />
    </>
  );
};

export default Index;
