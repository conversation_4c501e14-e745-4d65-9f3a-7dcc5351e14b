import {
  BaseModalProps,
  XlbBasicForm,
  XlbButton,
  XlbImportModal,
  XlbInputDialog,
  XlbModal,
  XlbRadio,
} from '@xlb/components';
import { message, Space } from 'antd';
import { FC, useEffect } from 'react';
import Api from '../../server';
import style from './batchChange.less';

interface props extends Pick<BaseModalProps, 'visible' | 'onCancel'> {
  onOk: () => void;
}

const Index: FC<props> = ({ visible, onCancel, onOk }) => {
  const [form] = XlbBasicForm.useForm();

  const handleOk = async () => {
    const values = await form.validateFields();
    if (!values) return;
    if (!values?.checkValue) {
      message.error('请选择设置范围');
      return;
    }
    if (
      values?.checkValue === 2 &&
      (!values?.store_area_ids || !values?.store_area_ids?.length)
    ) {
      message.error('请选择指定区域');
      return;
    }

    const params: any = { ...values };
    if (values?.checkValue === 1) {
      params.store_area_ids = [];
    }
    const res = await Api.batchSave(params);
    if (res.code === 0) {
      message.success('操作成功');
    }
    onOk();
  };

  const importItem = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storearea.store.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storecodetemplate.download`,
      title: '门店区域导入',
    });
    if (!res) return;

    const data = res.data?.stores?.map((i: any) => i.id);
    form.setFieldValue('store_ids', data);
  };

  useEffect(() => {
    form.resetFields();
  }, [visible]);

  return (
    <XlbModal
      title={'批量设置'}
      keyboard={false}
      visible={visible}
      onOk={(e) => {
        handleOk();
      }}
      onCancel={onCancel}
      isCancel={true}
      width={420}
    >
      <XlbBasicForm form={form} layout="horizontal">
        <div id={style.box}>
          <p className={style.title}>设置范围</p>
          <XlbBasicForm.Item name="checkValue">
            <XlbRadio.Group style={{ width: '100%' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <XlbRadio value={1}>全部区域</XlbRadio>
                <Space direction="horizontal" style={{ width: '100%' }}>
                  <XlbRadio value={2} style={{ width: '100%' }}>
                    指定区域
                  </XlbRadio>
                  <XlbBasicForm.Item name="store_area_ids">
                    <XlbInputDialog
                      style={{
                        width: 180,
                        height: 28,
                        marginBottom: '0px !important',
                      }}
                      dialogParams={{
                        type: 'storeArea',
                        dataType: 'lists',
                        isLeftColumn: false,
                        isMultiple: true,
                      }}
                    />
                  </XlbBasicForm.Item>
                </Space>
              </Space>
            </XlbRadio.Group>
          </XlbBasicForm.Item>
        </div>
        <div id={style.box}>
          <p className={style.title}>设置内容</p>
          <div style={{ display: 'flex', alignItems: 'center', gap: 15 }}>
            <div style={{ lineHeight: '28px' }}>门店：</div>
            <XlbBasicForm.Item name="store_ids">
              <XlbInputDialog
                style={{
                  width: 140,
                  height: 28,
                  marginBottom: '0px !important',
                }}
                dialogParams={{
                  type: 'store',
                  dataType: 'lists',
                  isMultiple: true,
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name',
                }}
              />
            </XlbBasicForm.Item>
            <XlbButton onClick={importItem}>导入</XlbButton>
          </div>
        </div>
      </XlbBasicForm>
    </XlbModal>
  );
};

export default Index;