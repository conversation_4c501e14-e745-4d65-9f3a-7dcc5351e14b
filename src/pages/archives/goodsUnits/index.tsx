import { type FC } from 'react'
import { tableColumn } from './data'
import { hasAuth } from '@/utils/kit'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { XlbProPageContainer } from '@xlb/components'

const ProForm: FC<{ title: string }> = () => {
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [{ id: 'keyword', name: 'item_unit_name', label: '关键字' }]
      }}
      tableFieldProps={{
        url: '/erp-mdm/hxl.erp.itemunit.find',
        tableColumn: tableColumn,
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: false,
        immediatePost: true
      }}
      deleteFieldProps={{
        name: '删除',
        showField: 'item_unit_name',
        url: hasAuth(['商品单位', '删除']) ? '/erp-mdm/hxl.erp.itemunit.delete' : ''
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['商品单位', '编辑']) ? '/erp-mdm/hxl.erp.itemunit.save' : ''
      }}
      details={{
        mode: 'modal',
        isCancel: true,
        width: 350,
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>
        },
        hiddenSaveBtn: true,
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap.itemUnitName,
                  itemSpan: 24,
                  rules: [{ required: true, message: '商品单位名称不能为空' }],
                  fieldProps: { maxLength: 20, width: '100%' }
                  // width:180
                }
              ]
            }
          }
        ],
        updateFieldProps: {
          url: '/erp-mdm/hxl.erp.itemunit.update'
        }
      }}
    />
  )
}

export default ProForm