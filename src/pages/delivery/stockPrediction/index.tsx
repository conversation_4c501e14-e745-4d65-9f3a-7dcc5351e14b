import useDownload from '@/hooks/useDownload';
import { hasAuth, LStorage } from '@/utils';
import {
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbPageContainer,
  XlbTableColumnProps,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { wujieBus } from '@/wujie/utils';
import { message } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { formList, tableList } from './data';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

/**
 * xlbForm dateRange
 */

const Index = () => {
  const [formListColumn, setFormListColumn] = useState<SearchFormType[]>(
    cloneDeep(formList),
  );
  const [TableColumn, setTableColumn] =
    useState<XlbTableColumnProps<any>[]>(tableList);
  const [preparetypeList, setPreparetypeList] = useState<any[]>([]);
  const [footerData, setFooterData] = useState<any[]>([]);
  const [form] = XlbBasicForm.useForm<any>();
  const userInfo = LStorage.get('userInfo');
  const { downByProgress } = useDownload();
  let refresh = () => {};
  const howShowList = (isShow: boolean, formData: any) => {
    const { category_level } = formData;
    console.log('🚀 ~ howShowList ~ category_level:', category_level);
    const codeList = ['item_code', 'item_bar_code', 'item_name', 'unit'];
    const category = [
      'one_item_category_name',
      'two_item_category_name',
      'three_item_category_name',
    ];
    TableColumn.forEach((item: any) => {
      if (codeList.includes(item.code)) {
        item.hidden = !isShow;
      }
      if (category_level) {
        if (item.code === 'item_category_name') {
          item.hidden = true;
        }
        if (item.code === 'one_item_category_name') {
          item.hidden = !(category_level >= 1);
        }
        if (item.code === 'two_item_category_name') {
          item.hidden = !(category_level >= 2);
        }
        if (item.code === 'three_item_category_name') {
          item.hidden = !(category_level >= 3);
        }
      } else {
        if (item.code === 'item_category_name') {
          item.hidden = false;
        }
        if (category.includes(item.code)) {
          item.hidden = true;
        }
      }
    });
    setTableColumn([...TableColumn]);
  };
  const prevPost = () => {
    const formData = form.getFieldsValue(true);
    const {
      prepare_type_id,
      store_ids,
      summary_types,
      item_ids,
      query_date,
      prepare_date,
      sale_date,
      ...rest
    } = formData;
    if (summary_types == 'CATEGORY') {
      form.setFieldValue('summary_types', ['CATEGORY']);
    }
    if (!prepare_type_id || prepare_type_id?.lenght === 0) {
      message.error('请选择备货类型')
      return false
    }
    if (!store_ids || store_ids?.lenght === 0) {
      message.error('门店不能为空');
      return false;
    }
    // console.log('item_ids', item_ids)
    // if (store_ids?.length > 100 && (!item_ids || item_ids.length === 0)) {
    //   message.error('门店大于200，商品不能为空')
    //   return
    // }

    if (!query_date || query_date?.lenght === 0) {
      message.error('销售取值不能为空');
      return false;
    }
    if (!prepare_date || prepare_date?.lenght === 0) {
      message.error('备货日期不能为空');
      return false;
    }
    if (!sale_date || sale_date?.lenght === 0) {
      message.error('备货销售日期不能为空');
      return false;
    }
    howShowList(
      Array(summary_types) ? summary_types?.includes('ITEM') : false,
      formData,
    );
    const formatDates = (dates: moment.Moment[]) => [
      dates[0].format('YYYY-MM-DD'),
      dates[1].format('YYYY-MM-DD'),
    ];
    const updatedFormData = {
      ...formData,
      query_date: formatDates(query_date),
      prepare_date: formatDates(prepare_date),
      sale_date: formatDates(sale_date),
    };
    console.log('🚀 ~ prevPost ~ updatedFormData:', updatedFormData);
    return {
      ...updatedFormData,
    };
  };

  //导出
  const exportData = async (requestForm: any, setLoading: Function, e: any) => {
    setLoading(true);
    const res = await XlbFetch.post(
      '/erp/hxl.erp.deliveryreport.prepareprediction.export',
      { ...requestForm },
    );
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setLoading(false);
  };

  //备货类型设置
  const getPreparetypeFind = async () => {
    const res = await XlbFetch.post('/erp/hxl.erp.preparetype.find', {});
    if (res?.code === 0) {
      setPreparetypeList(res?.data || []);
      const optionsList =
        res?.data?.length > 0
          ? res?.data
              ?.filter((item: any) => item.enable)
              ?.map((item: any) => ({
                label: item.name,
                value: item.id,
              }))
          : [];
      formList.find((item: any) => item.label == '备货类型')!.options =
        optionsList;
      setFormListColumn([...formList]);
    }
  };
  const onValuesChange = (changedValues: any, allValues: any) => {
    if (!!allValues?.prepare_type_id) {
      const filterList = preparetypeList.filter(
        (item: any) => item.id == allValues?.prepare_type_id,
      );
      if (filterList?.length > 0) {
        const {
          query_start_time,
          query_end_time,
          start_time,
          end_time,
          sale_start_time,
          sale_end_time,
        } = filterList[0];
        const query_date = [moment(query_start_time), moment(query_end_time)];
        const prepare_date = [moment(start_time), moment(end_time)];
        const sale_date = [moment(sale_start_time), moment(sale_end_time)];

        form.setFieldsValue({
          ...form.getFieldsValue(true),
          query_date: query_date,
          prepare_date: prepare_date,
          sale_date: sale_date,
        });
      }
      const labels = ['销量取值范围', '备货日期', '销售日期'];
      formList.forEach((item: any) => {
        if (labels.includes(item.label)) {
          item.disabled = !!allValues?.prepare_type_id;
        }
      });
      setFormListColumn([...formList]);
    }
  };
  // 初始化表单数据
  const getFormData = (data: any) => {
    form.setFieldsValue({
      ...data,
      summary_types: ['ITEM', 'CATEGORY'],
      store_ids: userInfo?.store_id ? [userInfo?.store_id] : undefined,
      coefficient: 1.0,
    });
  };

  /**
   *
   */
  const Render = (item: any) => {
    switch (item.code) {
      case 'avg_sale_quantity':
      // case 'avg_sale_quantity_ratio':
      case 'prepare_quantity':
      case 'theory_prepare_quantity':
      case 'prepare_sale_quantity':
      case 'prepare_delivery_quantity':
        item.render = (value) => {
          return (
            <div className="info overwidth">
              <span>{value ? Number(value || 0).toFixed(3) : '0.000'}</span>
            </div>
          );
        };
        break;
      case 'prepare_difference':
        item.render = (value) => {
          return (
            <div className="info overwidth">
              <span style={{ color: Number(value || 0) > 0 ? 'red' : 'green' }}>
                {value}
              </span>
            </div>
          );
        };
        break;
      default:
        return (item.render = (value) => (
          <div className="info overwidth">{value}</div>
        ));
    }
  };

  TableColumn.map((v) => Render(v));

  useEffect(() => {
    getPreparetypeFind().then();
    getFormData({});
  }, []);

  return (
    <>
      <XlbPageContainer
        url={'/erp/hxl.erp.deliveryreport.prepareprediction.find'}
        tableColumn={TableColumn}
        prevPost={prevPost}
        immediatePost={false}
      >
        <ToolBtn showColumnsSetting>
          {({ fetchData, dataSource, requestForm, loading, setLoading }) => {
            refresh = fetchData;
            return (
              <XlbButton.Group>
                <XlbButton
                  key="query"
                  label="查询"
                  type="primary"
                  disabled={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<span className="iconfont icon-sousuo" />}
                />
                {hasAuth(['备货预测', '导出']) && (
                  <XlbButton
                    key="exports"
                    label="导出"
                    type="primary"
                    disabled={!dataSource?.length || loading}
                    onClick={(e: any) => exportData(requestForm, setLoading, e)}
                    icon={<span className="iconfont icon-daochu" />}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={formListColumn}
            form={form}
            isHideDate={true}
            getFormRecord={() => refresh()}
            onValuesChange={onValuesChange}
          />
        </SearchForm>
        <Table key="id" selectMode="single" />
      </XlbPageContainer>
    </>
  );
};
export default Index;
