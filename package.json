{"name": "xlb_erp", "private": true, "author": "曾涛 <<EMAIL>>", "scripts": {"build": "max build", "build:dev": "cross-env  UMI_ENV=dev max build", "dev": "max dev", "develop": "node node_modules/@xlb/components/bin/develop.mjs", "format": "prettier --cache --write .", "postinstall": "max setup", "openapi": "max openapi", "prepare": "husky install", "setup": "max setup", "start": "npm run dev", "xlbVersion": "npm view @xlb/components versions"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/pro-components": "^2.8.1", "@ebay/nice-modal-react": "^1.2.13", "@tanstack/react-query": "^5.62.11", "@umijs/max": "^4.3.27", "@umijs/max-plugin-openapi": "^2.0.3", "@xlb/components": "2.0.183", "@xlb/datav": "^0.1.125", "@xlb/max": "0.0.15", "@xlb/utils": "2.2.17", "ahooks": "^3.8.1", "ali-react-table": "^2.6.1", "antd": "5.21.4", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "pubsub-js": "^1.9.4", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-flow-builder": "^2.8.0", "umi-plugin-keep-alive": "^0.0.1-beta.35", "umi-request": "^1.4.0", "uuid": "^9.0.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/pubsub-js": "^1.8.6", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "braft-editor": "^2.3.9", "cross-env": "^7.0.3", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.3", "tailwindcss": "^3.4.14", "typescript": "^5.6.3", "uuid": "^11.1.0"}}