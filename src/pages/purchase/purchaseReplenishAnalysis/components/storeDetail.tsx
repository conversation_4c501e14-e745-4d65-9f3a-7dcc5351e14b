import { hasAuth } from '@/utils';
import Download from '@/utils/downloadBlobFile';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
} from '@xlb/components';
import { useEffect, useState } from 'react';
import { storeDetailFormList, storeDetailTableColumn } from '../data';
import { storeDetailExport } from '../server';

interface IProps {
  data: {
    sale_date: any;
    item_id: number;
    store_id: number;
    click_value: any;
    new_store?: boolean;
  };
}
const StoreDetail = (props: IProps) => {
  const [form] = XlbBasicForm.useForm();
  const { sale_date, item_id, store_id, click_value, new_store } = props.data;

  const prevPost = (data: any) => {
    const formatFormValue = (itemCode: string) =>
      form.getFieldValue(itemCode) === null
        ? null
        : Boolean(form.getFieldValue(itemCode));
    const formatData = {
      item_id,
      sale_date,
      new_store,
      business_store: formatFormValue('business_store'),
      order_store: formatFormValue('order_store'),
      out_stock_store: formatFormValue('out_stock_store'),
      sale_store: formatFormValue('sale_store'),
      store_id,
    };
    return {
      ...data,
      ...formatData,
    };
  };

  const initForm = () => {
    form.setFieldsValue({
      [click_value]: 1,
    });
  };

  const [exportLoading, setExportLoading] = useState(false);
  const exportData = async (requestForm: any) => {
    setExportLoading(true);
    const res = await storeDetailExport({
      ...requestForm,
      responseType: 'blob',
    });
    setExportLoading(false);
    const download = new Download();
    download.filename = '采购补货分析门店详情.xlsx';
    download.xlsx(res);
  };

  useEffect(() => {
    initForm();
  }, [click_value]);
  return (
    <XlbPageContainer
      url="/erp/hxl.erp.purchasereplenishanalysis.itemstore.find"
      tableColumn={storeDetailTableColumn}
      prevPost={prevPost}
      immediatePost
    >
      <XlbPageContainer.ToolBtn showColumnsSetting={false}>
        {({
          fetchData,
          loading,
          dataSource,
          requestForm,
        }: ContextState<any>) => {
          return (
            <XlbButton.Group>
              <XlbButton
                label="查询"
                type="primary"
                loading={loading}
                onClick={() => {
                  fetchData();
                }}
                icon={<XlbIcon name="sousuo" />}
              />
              {hasAuth(['采购补货分析', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={exportLoading}
                  disabled={!dataSource?.length}
                  icon={<XlbIcon name="daochu" />}
                  onClick={() => exportData(requestForm)}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>
      <XlbPageContainer.SearchForm>
        <XlbForm form={form} formList={storeDetailFormList} isHideDate />
      </XlbPageContainer.SearchForm>
      <XlbPageContainer.Table style={{ height: 200, flex: '0 1 auto' }} />
    </XlbPageContainer>
  );
};
export default StoreDetail;
