import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import { history } from '@@/core/history';
import type { XlbTableColumnProps } from '@xlb/components';
import {
  XlbIcon as IconFont,
  SearchFormType,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbPageContainer,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useEffect, useState, type FC } from 'react';
import {
  basicTable,
  businessArr,
  categoryName,
  deliveryDate,
  financeCode,
  inStore,
  itemTable,
  outStore,
  purchase_type,
  queryUnit,
  supplier,
} from './data';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const DistributionGross: FC = () => {
  const { enable_organization } = useBaseParams((state) => state)
  const formlist = [
    {
      width: 372,
      type: 'compactDatePicker',
      label: '日期选择',
      name: 'compactDatePicker',
      disabledChecked: true,
      allowClear: false,
    },
    {
      type: 'select',
      name: 'time_type',
      label: '时间类型',
      disabledChecked: true,
      allowClear: false,
      options: [
        {
          label: '制单时间',
          value: 'create_date',
        },
        {
          label: '审核时间',
          value: 'audit_date',
        },
        {
          label: '单据时间',
          value: 'operate_date',
        },
        {
          label: '签收日期',
          value: 'receive_date',
        },
      ],
    },
    {
      label: '调出组织',
      name: 'out_org_ids',
      type: 'select',
      multiple: true,
      allowClear: true,
      hiddenInXlbColumns: true,
      hidden: !enable_organization,
      selectRequestParams: {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans: {
          label: 'name',
          value: 'id',
        },
      },
    },

    {
      label: '调出门店',
      name: 'out_store_ids',
      type: 'inputDialog',
      allowClear: true,
      disabledChecked: true,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          status: true,
        },
      },

      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },
    {
      label: '调出仓库',
      name: 'storehouse_id',
      type: 'select',
      allowClear: true,
      dependencies: ['out_store_ids'],
      dropdownStyle: { width: 200 },
      handleDefaultValue: (data: any, formData: any) => {
        if (data?.length === 0) {
          return null;
        }
        const defaultStoreHouse =
          data.find((item: any) => item.default_flag) || data[0];
        return defaultStoreHouse?.value;
      },
      // @ts-ignore
      disabled: (form: any) => {
        const store_ids = form.getFieldValue('out_store_ids');
        return !store_ids || store_ids?.length > 1;
      },
      // @ts-ignore
      selectRequestParams: (params: any, form: any) => {
        form?.setFieldValue('storehouse_id', null);
        if (params?.out_store_ids?.length == 1) {
          return {
            url: '/erp/hxl.erp.storehouse.store.find',
            postParams: {
              store_id: params?.out_store_ids?.[0],
            },
            responseTrans(data) {
              const options = data
                .filter((v: any) => v.distribution)
                .map((item: any) => ({
                  label: item.name,
                  value: item.id,
                  default_flag: item.default_flag,
                }));
              return options;
            },
          };
        }
      },
    },
    {
      label: '调入组织',
      name: 'in_org_ids',
      allowClear: true,
      type: 'select',
      multiple: true,
      hiddenInXlbColumns: true,
      hidden: !enable_organization,
      selectRequestParams: {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans: {
          label: 'name',
          value: 'id',
        },
      },
    },

    {
      label: '调入门店',
      name: 'in_store_ids',
      type: 'inputDialog',
      allowClear: true,
      disabledChecked: true,
      dialogParams: {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        data: {
          status: true,
        },
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'store_name',
      } as any,
    },

    {
      label: '商品档案',
      name: 'item_ids',
      type: 'inputDialog',
      allowClear: true,

      dialogParams: {
        type: 'goods',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
      },
    },

    {
      label: '商品类别',
      name: 'item_category_ids',
      type: 'inputDialog',
      treeModalConfig: {
          topLevelTreeCheckable: true,
          title: '选择商品分类',  // 标题
          url: '/erp-mdm/hxl.erp.category.find', // 请求地址
          dataType: 'lists',
          checkable: true, // 是否多选
          primaryKey: 'id',
          data: {
              enabled: true
          },
          width: 360, // 模态框宽度
      },
    },

    {
      label: '采购类型',
      name: 'purchase_types',
      type: 'select',
      allowClear: true,
      multiple: true,
      options: [
        {
          label: '集采品',
          value: 'COLLECTIVE_PURCHASE',
        },
        {
          label: '集售品',
          value: 'COLLECTIVE_SALE',
        },
        {
          label: '地采品',
          value: 'GROUND_PURCHASE',
        },
        {
          label: '店采品',
          value: 'SHOP_PURCHASE',
        },
      ],
    },

    {
      label: '配送类型',
      name: 'delivery_type',
      type: 'select',
      allowClear: true,
      check: true,
      options: [
        {
          label: '直营',
          value: 'DIRECT',
        },
        {
          label: '加盟',
          value: 'JOIN',
        },
      ],
    },

    {
      type: 'select',
      label: '供货主体',
      name: 'main_body_id',
      selectRequestParams: {
        url: '/erp-mdm/hxl.erp.suppliermainbody.find',
        responseTrans: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      label: '供应商',
      name: 'supplier_ids',
      type: 'inputDialog',
      dialogParams: {
        type: 'supplier',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      },
    },

    {
      label: '查询单位',
      name: 'unit_type',
      type: 'select',
      allowClear: false,
      check: true,
      disabledChecked: true,
      options: queryUnit,
    },
    {
      label: '汇总条件',
      name: 'summary_types',
      type: 'select',
      disabledChecked: true,
      allowClear: false,
      multiple: true,
      initialValue: ['OUT_STORE'],
      // Change: (value: any) => {},
      options: [
        {
          label: '调出门店',
          value: 'OUT_STORE',
          disabled: false,
        },
        {
          label: '调入门店',
          value: 'IN_STORE',
          disabled: false,
        },
        {
          label: '商品类别',
          value: 'CATEGORY',
          disabled: false,
        },
        {
          label: '商品档案',
          value: 'ITEM',
          disabled: false,
        },
        {
          label: '配送日期',
          value: 'DATE',
          disabled: false,
        },
        {
          label: '供应商',
          value: 'SUPPLIER',
          disabled: false,
        },
        {
          label: '配送类型',
          value: 'DELIVERY_TYPE',
          disabled: false,
        },
      ],
    },
    {
      label: '类别等级',
      name: 'category_level',
      type: 'select',
      allowClear: true,
      check: true,
      disabledChecked: true,
      // hidden:true,
      dependencies: ['summary_types'],
      onDependencies(formValues: any) {
        return !formValues
          ?.getFieldValue('summary_types')
          ?.includes('CATEGORY');
      },
      initialValue: 1,
      options: [
        {
          label: '按一级类别',
          value: 1,
        },
        {
          label: '按二级类别',
          value: 2,
        },
        {
          label: '按三级类别',
          value: 3,
        },
      ],
    },
    {
      label: '业财核算分类',
      name: 'finance_codes',
      multiple: true,
      type: 'select',
      allowClear: true,
      check: true,
      disabledChecked: true,
      dependencies: ['summary_types'],
      onDependencies(formValues: any) {
        return !formValues?.getFieldValue('summary_types')?.includes('ITEM');
      },
      selectRequestParams: {
        url: '/erp-mdm/hxl.erp.settlementcategory.center.find',
        responseTrans: {
          label: 'category_name',
          value: 'code',
        },
      },
    },
  ];

  const [form] = XlbBasicForm.useForm();
  const [footerData, setFooterData] = useState<any>([]);
  const summaryTypes = XlbBasicForm.useWatch('summary_types', form);

  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(basicTable)),
  );
  const [stateFormlist, setFormList] = useState<SearchFormType[]>(formlist);

  const userInfo = LStorage.get('userInfo');

  const [exportParams, setExportParams] = useState({});
  const record = history.location.state as any;

  let refresh = () => {};

  // 导出
  const exportItem = async (requestForm: any, setLoading: Function, e: any) => {
    setLoading(true);
    const params = exportParams;
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliveryreport.deliveryanalyze.export',
      { ...params },
    );

    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setLoading(false)
  }

  const beforeQuery=(e:Event,fetchData:Function)=>{
    e.stopPropagation();
    const {
      out_store_ids,

      in_store_ids,
    } = form.getFieldsValue(true);
    if (!in_store_ids && !out_store_ids) {
      message.error('调出门店和调入门店不能同时为空');
      return;
    } else {
      fetchData();
    }
  };

  //处理查询参数
  const prevPost = (pageInfo:any) => {

    const { compactDatePicker,time_type} = form.getFieldsValue(true);
    const data: any = {
      ...form.getFieldsValue(),
      company_id: LStorage.get('userInfo').company_id,
      operator_store_id: LStorage.get('userInfo').store_id,
      audit_date: time_type === 'audit_date' ? compactDatePicker : undefined,
      create_date: time_type === 'create_date' ? compactDatePicker : undefined,
      operate_date: time_type === 'operate_date' ? compactDatePicker : undefined,
      receive_date: time_type === 'receive_date' ? compactDatePicker : undefined,
    };
    delete data.compactDatePicker;
    setExportParams({
      ...data,
    });

    return { ...data };
  };


  //查询门店下仓库
  // const getStockData = async (id: any) => {
    // const res = await Api.getStock({ 
    //   store_id: id ,
    //   company_id: LStorage.get('userInfo').company_id,
    //   operator_store_id: LStorage.get('userInfo').store_id,
    // })
    // if (res.code === 0) {
    //   const labArr = res.data.map((item: any) => ({
    //     label: item.name,
    //     value: item.id,
    //     default_flag: item.default_flag
    //   }))
    //   stateFormlist.find((item) => item.label === '调出仓库')!.options = labArr
    //   form.setFieldsValue({ storehouse_name: '' })
    //   setTimeout(() => {
    //     setFormList([...stateFormlist])
    //   }, 200)
    // }
  // }





  const itemArrTotalRender = (item: any = itemArr) => {
    switch (item.code) {
      case 'purchase_type':
        item.render = (value: any) => (
          <div>
            {value ? purchase_type.find((v) => v.value == value)?.label : ''}
          </div>
        );
        break;
      case 'out_quantity':
      case 'in_quantity':
      case 'basic_out_quantity':
      case 'basic_in_quantity':
      case 'out_quantity_total':
        item.render = (value: any) => (
          <div>{value ? Number(value).toFixed(3) : '0.000'}</div>
        );
        break;
      case 'out_money':
      case 'no_tax_out_money':
      case 'in_money':
      case 'no_tax_in_money':
      case 'out_money_total':
      case 'out_tax_money_total':
      case 'no_tax_out_money_total':
        item.render = (value: any) => (
          <div>
            {hasAuth(['配送分析/配送价', '查询'])
              ? Number(value || 0).toFixed(2)
              : '*****'}
          </div>
        );
        break;
      case 'out_cost':
      case 'no_tax_out_cost':
      case 'in_cost':
      case 'no_tax_in_cost':
      case 'out_cost_total':
      case 'out_cost_tax_money_total':
      case 'no_tax_out_cost_total':
        item.render = (value: any) => (
          <div>
            {hasAuth(['配送分析/成本价', '查询'])
              ? Number(value || 0).toFixed(2)
              : value}
          </div>
        );
        break;
      case 'delivery_gross_profit':
      case 'delivery_gross_profit_tax_money':
      case 'no_tax_delivery_gross_profit':
      case 'delivery_gross_profit_rate':
      case 'no_tax_delivery_gross_profit_rate':
      case 'sale_gross_profit':
      case 'no_tax_sale_gross_profit':
      case 'sale_gross_profit_rate':
      case 'no_tax_sale_gross_profit_rate':
        item.render = (value: any) => {
          return (
            <div>
              {hasAuth(['配送分析/毛利', '查询'])
                ? Number(value || 0).toFixed(2)
                : value}
            </div>
          );
        };
        break;
      case 'sale_money':
      case 'no_tax_sale_money':
        item.render = (value: any) => (
          <div>
            {hasAuth(['配送分析/零售价', '查询'])
              ? Number(value || 0).toFixed(2)
              : value}
          </div>
        );
        break;
      case 'tax_rate':
        item.render = (value: any, record: any) => (
          <div>
            {hasAuth(['配送毛利', '查询'])
              ? value === '****'
                ? value
                : Number(value).toFixed(2) + '%'
                  ? Number(value).toFixed(2) + '%'
                  : '0.00%'
              : '****'}
          </div>
        );
        break;
    }
    return item;
  };

  useEffect(() => {
    if (record && record?.condition) {
      console.log(record?.condition, '【配送分析】传过来的条件');
      form.setFieldsValue({
        ...record?.condition,
        audit_date: [
          record?.condition.audit_date
            ? dayjs(record?.condition.audit_date[0])
            : dayjs(),
          record?.condition.audit_date
            ? dayjs(record?.condition.audit_date[1])
            : dayjs(),
        ],
      });
      refresh();
    }
  }, [record]);

  // 监听summary_types
  useEffect(() => { 
    const showName = {
      OUT_STORE: outStore,
      IN_STORE: inStore,
      ITEM: itemTable,
      CATEGORY: categoryName,
      DATE: deliveryDate,
      SUPPLIER: supplier,
      DELIVERY_TYPE: businessArr
    }

    const TableName = JSON.parse(JSON.stringify(basicTable))

    const spliceTable: any = []
    summaryTypes?.forEach((v: any) => {
      spliceTable.push(...showName[v])
    })
    if (summaryTypes?.includes('ITEM')) {
      spliceTable.push(...financeCode)
    }
    TableName.splice(1, 0, ...spliceTable)
    setItemArr(TableName)
    setFooterData([])
  }, [summaryTypes])

  const tableContrl = ()=>{
    return itemArr.map(item=>itemArrTotalRender(item))
  }

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.deliveryreport.deliveryanalyze.page'}
      immediatePost={false}
      tableColumn={tableContrl()}
      prevPost={prevPost}
      afterPost={(data) => {
        setFooterData([
          {
            _index: '合计',
            tax_rate: data?.tax_rate || null, // 税率率合计
            in_quantity:
              Number(data?.all_in_quantity_total).toFixed(3) || '000', // 调入数量合计
            basic_in_quantity:
              Number(data?.all_basic_in_quantity_total).toFixed(3) || '000', // 调入基本数量合计
            out_money: hasAuth(['配送分析/配送价', '查询'])
              ? Number(data?.all_out_money_total).toFixed(3) || '0.000'
              : data?.all_out_money_total, // 调出金额合计
            no_tax_out_money: hasAuth(['配送分析/配送价', '查询'])
              ? Number(data?.no_tax_all_out_money_total).toFixed(3) || '0.000'
              : data?.no_tax_all_out_money_total, //	调出数量合计
            in_money: hasAuth(['配送分析/配送价', '查询'])
              ? Number(data?.all_in_money_total).toFixed(2) || '0.00'
              : data?.all_in_money_total, //	调入金额合计
            no_tax_in_money: hasAuth(['配送分析/配送价', '查询'])
              ? Number(data?.no_tax_all_in_money_total).toFixed(3) || '0.000'
              : data?.no_tax_all_in_money_total, //	调出数量合计
            out_money_total: hasAuth(['配送分析/配送价', '查询'])
              ? Number(data?.all_out_money_total_total).toFixed(2) || '0.00'
              : data?.all_out_money_total_total, //	调出金额合计
            out_tax_money_total: hasAuth(['配送分析/配送价', '查询'])
              ? Number(data?.all_out_tax_money_total).toFixed(2) || '0.00'
              : data?.all_out_tax_money_total,
            no_tax_out_money_total: hasAuth(['配送分析/配送价', '查询'])
              ? Number(data?.no_tax_all_out_money_total_total).toFixed(2) ||
                '0.00'
              : data?.no_tax_all_out_money_total_total, //	调出金额合计（不含税）

            out_cost: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.all_out_cost_total).toFixed(2) || '0.00'
              : data?.all_out_cost_total, // 调入数量合计
            no_tax_out_cost: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.no_tax_all_out_cost_total).toFixed(2) || '0.00'
              : data?.no_tax_all_out_cost_total, //	调出数量合计
            in_cost: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.all_in_cost_total).toFixed(3) || '0.000'
              : data?.all_in_cost_total, // 调入成本合计
            no_tax_in_cost: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.no_tax_all_in_cost_total).toFixed(3) || '0.000'
              : data?.no_tax_all_in_cost_total, //	调入成本去税合计
            out_cost_total: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.all_out_cost_total_total).toFixed(3) || '0.000'
              : data?.all_out_cost_total_total, //调出成本合计
            out_cost_tax_money_total: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.all_out_cost_tax_money_total).toFixed(2) || '0.00'
              : data?.all_out_cost_tax_money_total,
            no_tax_out_cost_total: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.no_tax_all_out_cost_total_total).toFixed(3) ||
                '0.000'
              : data?.no_tax_all_out_cost_total_total, //	调出数量合计
            out_quantity_total: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.all_out_quantity_total_total).toFixed(3) || '0.000'
              : data?.all_out_quantity_total_total, //	调出金额合计
            out_quantity: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.all_out_quantity_total).toFixed(2) || '0.00'
              : data?.all_out_quantity_total, //	调出数量
            basic_out_quantity: hasAuth(['配送分析/成本价', '查询'])
              ? Number(data?.all_basic_out_quantity_total).toFixed(3) || '000'
              : data?.all_basic_out_quantity_total, // 调出基本数量
            delivery_gross_profit: hasAuth(['配送分析/毛利', '查询'])
              ? data?.all_delivery_gross_profit_total || null
              : data?.all_delivery_gross_profit_total, // 配送毛利合计
            delivery_gross_profit_tax_money: hasAuth(['配送分析/毛利', '查询'])
              ? Number(data?.all_delivery_gross_profit_tax_money_total).toFixed(
                  2,
                ) || '0.00'
              : data?.all_delivery_gross_profit_tax_money_total,
            no_tax_delivery_gross_profit: hasAuth(['配送分析/毛利', '查询'])
              ? Number(data?.no_tax_all_delivery_gross_profit_total).toFixed(
                  3,
                ) || '0.000'
              : data?.no_tax_all_delivery_gross_profit_total, //	调出数量合计
            delivery_gross_profit_rate: hasAuth(['配送分析/毛利', '查询'])
              ? data?.all_delivery_gross_profit_rate_total || null
              : data?.all_delivery_gross_profit_rate_total, // 配送毛利率合计
            no_tax_delivery_gross_profit_rate: hasAuth([
              '配送分析/毛利',
              '查询',
            ])
              ? Number(
                  data?.no_tax_all_delivery_gross_profit_rate_total,
                ).toFixed(3) || '0.000'
              : data?.no_tax_all_delivery_gross_profit_rate_total, //	调出数量合计
            sale_gross_profit: hasAuth(['配送分析/毛利', '查询'])
              ? Number(data?.sale_gross_profit_total).toFixed(2) || '0.00'
              : data?.sale_gross_profit_total,
            no_tax_sale_gross_profit: hasAuth(['配送分析/毛利', '查询'])
              ? Number(data?.no_tax_sale_gross_profit_total).toFixed(2) ||
                '0.00'
              : data?.no_tax_sale_gross_profit_total, // 零售毛利（去税）合计
            sale_gross_profit_rate: hasAuth(['配送分析/毛利', '查询'])
              ? Number(data?.sale_gross_profit_rate_total).toFixed(2) || '0.00'
              : data?.sale_gross_profit_rate_total,
            no_tax_sale_gross_profit_rate: hasAuth(['配送分析/毛利', '查询'])
              ? Number(data?.no_tax_sale_gross_profit_rate_total).toFixed(2) ||
                '0.00'
              : data?.no_tax_sale_gross_profit_rate_total, // 零售毛利率（去税）合计

            sale_money: hasAuth(['配送分析/零售价', '查询'])
              ? Number(data?.sale_money_total).toFixed(2) || '0.00'
              : data?.sale_money_total,
            no_tax_sale_money: hasAuth(['配送分析/零售价', '查询'])
              ? Number(data?.no_tax_sale_money_total).toFixed(3) || '0.000'
              : data?.no_tax_sale_money_total, //	零售金额去税合计
          },
        ]);
      }}
      // ref={pageRef}
      isShowTemplate={true}
    >
      <SearchForm>
        <XlbForm
          isHideDate
          formList={stateFormlist}
          form={form}
          getFormRecord={refresh}
          initialValues={{
            time_desc: 0,
            compactDatePicker: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
            out_store_ids: [userInfo?.store?.id],
            unit_type: 'ORDER',
            time_type: 'audit_date',
          }}
        />
      </SearchForm>
      <ToolBtn
        formSaveOrigin
        showColumnsSetting
        originFormList={formlist}
        formList={formlist}
        onFormChange={(forms) => {
          setFormList(forms);
        }}
      >
        {({
          fetchData,
          dataSource,
          requestForm,
          loading,
          setLoading,
        }: // setCurrentIndex,
        any) => {
          refresh = fetchData;

          return (
            <XlbButton.Group>
              {hasAuth(['配送分析', '查询']) && (
                <XlbButton
                  loading={loading}
                  label="查询"
                  type="primary"
                  onClick={(e) => {
                    beforeQuery(e, fetchData);
                  }}
                  icon={
                    <IconFont name="sousuo" color="currentColor" size={16} />
                  }
                />
              )}
              {hasAuth(['配送分析', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={loading || !dataSource?.length}
                  onClick={(e) => exportItem(requestForm, setLoading, e)}
                  icon={
                    <IconFont name="daochu" color="currentColor" size={16} />
                  }
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table primaryKey={'id'} footerDataSource={footerData} />
    </XlbPageContainer>
  );
};

export default DistributionGross;