import { XlbFetch as ErpRequest } from '@xlb/utils';

// 获取采购参数
export const purchaseParams = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.purchaseparam.read', data);
};

// 批量添加
export const batchCreateItem = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereplenishanalysis.item.add',
    data,
  );
};

// 导出
export const exportPurchaseReplenishAnalysis = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereplenishanalysis.export',
    data,
  );
};

//  生成采购订单
export const createPurcahseOrder = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereplenishanalysis.purchaseorder.create',
    data,
  );
};
//  批量生成采购订单
export const batchCreatePurcahseOrder = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereplenishanalysis.purchaseorder.batchcreate',
    data,
  );
};

// 在订量弹窗
export const findUnReceiveQuantity = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereplenishanalysis.findunreceivequantity',
    data,
  );
};

// 获取补货分析展示
export const purchasereplenishanalysis = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereplenishanalysis.read',
    data,
  );
};

export const supplierrebatepresent = async (data: any) => {
  return await ErpRequest.post(
    '/scm/hxl.scm.suppliercontractorderpresent.find',
    data,
  );
};

// 门店详情导出
export const storeDetailExport = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.purchasereplenishanalysis.itemstore.export',
    data,
  );
};
