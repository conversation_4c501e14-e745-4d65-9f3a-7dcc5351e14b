import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { exportPage } from '@/services/system';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { HistoryOutlined } from '@ant-design/icons';
import {
  XlbButton,
  XlbImportModal,
  XlbInput,
  XlbMessage,
  XlbProPageContainer,
  XlbProPageContainerRef,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import IconFont from '@xlb/components/dist/components/icon';
import { useEffect, useRef, useState } from 'react';
import BactchChange from './component/batchChange/batchChange';
import Copy from './component/copy/copy';
import History from './component/history';
import { goodsType, queryUnit, types, types1 } from './data';
import { Update } from './server';

const StoreDeliveryPrice = () => {
  const enable_organization = useBaseParams(
    (state) => state.enable_organization,
  );
  const [batchVisible, setBatchVisible] = useState<boolean>(false); //批量设置弹框
  //复制弹框
  const [copyVisible, setCopyVisible] = useState<boolean>(false);
  const [historyVisible, setHistoryVisible] = useState<boolean>(false); //修改记录
  const formValuesRef = useRef<any>(null);
  const formRef = useRef<any>(null);
  const storeRef = useRef<any>(null);
  const pageRef = useRef<XlbProPageContainerRef>(null);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const inputChange = (e: any, record: any, index: any) => {
    record!.value = Number(e.target.value);
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    if (record.type === 'RATIO' && reg1.test(e.target.value)) {
      record.price = (
        record.purchase_price *
        (1 + Number(e.target.value) / 100)
      ).toFixed(4);
    } else if (record.type === 'MONEY' && reg1.test(e.target.value)) {
      record.price = (
        Number(record.purchase_price) + Number(e.target.value)
      ).toFixed(4);
    } else if (record.type === 'FIXED_MONEY' && reg1.test(e.target.value)) {
      record.price = Number(e.target.value).toFixed(4);
    }
  };
  const inputBasicChange = (e: any, record: any, index: any) => {
    record!.value = Number(e.target.value);
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    if (record.type === 'RATIO' && reg1.test(e.target.value)) {
      record.basic_unit_price = (
        record.purchase_price *
        (1 + Number(e.target.value) / 100)
      ).toFixed(4);
    } else if (record.type === 'MONEY' && reg1.test(e.target.value)) {
      record.basic_unit_price = (
        Number(record.purchase_price) + Number(e.target.value)
      ).toFixed(4);
    } else if (record.type === 'FIXED_MONEY' && reg1.test(e.target.value)) {
      record.basic_unit_price = Number(e.target.value).toFixed(4);
    }
  };
  const outBasicFocus = async (e: any, record: any, index: any) => {
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    if (!reg1.test(e.target.value) || Number(e.target.value) >= *********) {
      await XlbTipsModal({
        tips: '数值请输入大于等于零且小于等于*********的数字',
        isConfirm: true,
        isCancel: true,
      });
      record.value = '0.0000';
      return;
    }
    const data = {
      company_id: 1000,
      operator: JSON.parse(localStorage.userInfo).value.account,
      store_id: record.store_id,
      item_id: record.item_id,
      basic_unit_value: Number(e.target.value),
      type: record.type,
      unit_type: formValuesRef.current?.unit_type,
    };
    const res = await Update(data);
    if (res.code === 0) {
      XlbMessage.success('更新成功');
      pageRef?.current?.pageContainerRef?.current?.fetchData()
    }
  };
  //input失去焦点
  const outFocus = async (e: any, record: any, index: any) => {
    const reg1 = /^\d+(?=\.{0,1}\d+$|$)/;
    if (!reg1.test(e.target.value) || Number(e.target.value) >= *********) {
      await XlbTipsModal({
        tips: '数值请输入大于等于零且小于等于*********的数字',
        isConfirm: true,
        isCancel: true,
      });
      record.value = '0.0000';
      return;
    }
    const data = {
      company_id: 1000,
      operator: JSON.parse(localStorage.userInfo).value.account,
      store_id: record.store_id,
      item_id: record.item_id,
      value: Number(e.target.value),
      type: record.type,
      unit_type: formValuesRef.current?.unit_type,
    };
    const res = await Update(data);
    if (res.code === 0) {
      XlbMessage.success('更新成功');
      pageRef?.current?.pageContainerRef?.current?.fetchData()
    }
  };
  //下拉改变
  const selectChange = async (e: any, record: any, index: any) => {
    const _index = index['index'];
    record.type = e;
    if (e === 'NULL') {
      record.price = 0;
      record.value = 0;
    } else if (e === 'RATIO') {
      record.price = (record.purchase_price * (1 + record.value / 100)).toFixed(
        4,
      );
    } else if (e === 'MONEY') {
      record.price = (record.purchase_price + Number(record?.value)).toFixed(4);
    } else if (e === 'FIXED_MONEY') {
      record.price = Number(record?.value).toFixed(4);
    }
    const data = {
      company_id: 1000,
      operator: JSON.parse(localStorage.userInfo).value.account,
      store_id: record.store_id,
      item_id: record.item_id,
      type: e,
      value: record.value,
      unit_type: formValuesRef.current?.unit_type,
    };
    const res = await Update(data);
    if (res.code === 0) {
      XlbMessage.success('更新成功');
    }
  };

  //导入
  const imports = async () => {
    const bool = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.storedeliveryprice.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.storedeliverypricetemplate.download`,
      templateName: '下载模版',
      callback: () => {
        if (bool) {
          pageRef?.current?.pageContainerRef?.current?.fetchData();
        }
      },
    });
  };
  //导出
  const exportItem = async (
    pagin: { pageSize: number; pageNum: number; total: number },
    form: any,
  ) => {
    const data = {
      ...form.getFieldsValue(true),
      page_size: pagin.pageSize,
      page_number: pagin.pageNum - 1,
      item_types: form.getFieldValue('item_types'),
      item_category_ids:
        form.getFieldValue('item_category_ids') &&
        form.getFieldValue('item_category_ids'),
      item_ids:
        form.getFieldValue('item_ids') && form.getFieldValue('item_ids'),
      store_ids:
        form.getFieldValue('store_ids') && form.getFieldValue('store_ids'),
      type: form.getFieldValue('type') ? form.getFieldValue('type') : null,
      unit_type: form.getFieldValue('unit_type'),
      symbol: form.getFieldValue('two'),
      business_scope_ids: form.getFieldValue('business_scope_ids')
        ? form.getFieldValue('business_scope_ids')
        : [],
      org_ids: form.getFieldValue('org_ids')
        ? form.getFieldValue('org_ids')
        : [],
    };
    const res = await exportPage(
      '/erp/hxl.erp.storedeliveryprice.export',
      data,
      { responseType: 'blob' },
    );
    const download = new Download();
    download.filename = '门店配送价.xlsx';
    download.xlsx(res?.data);
  };
  useEffect(() => {
    const storage = LStorage.get('storeDeliveryPrice');
    if (storage) formRef?.current?.setFieldsValue({ ...storage });
  }, []);

  return (
    <>
      <BactchChange
        visible={batchVisible}
        enableOrganization={enable_organization}
        handleCancel={() => setBatchVisible(false)}
        getData={pageRef?.current?.pageContainerRef?.current?.fetchData}
      />
      <Copy
        visible={copyVisible}
        handleCancel={() => setCopyVisible(false)}
        getData={pageRef?.current?.pageContainerRef?.current?.fetchData}
      />
      <History
        visible={historyVisible}
        id={selectedRows}
        onCancel={() => setHistoryVisible(false)}
        unitType={formValuesRef.current?.unit_type}
      />
      <XlbProPageContainer
        ref={pageRef}
        searchFieldProps={{
          formList: [
            {
              id: ErpFieldKeyMap?.erpOrgIds,
              label: '所属组织',
              name: 'org_ids',
              dependencies: ['enable_organization'],
              hidden: !enable_organization,
            },
            {
              id: ErpFieldKeyMap?.erpStoreDeliveryPriceId,
              label: '门店',
              rules: [
                {
                  required: true,
                  message: '门店不能为空',
                  validator: (rule, value) => {
                    storeRef.current = value;
                    if (!Array.isArray(value) || value?.length == 0) {
                      return Promise.reject(new Error('门店不能为空'));
                    }
                    return Promise.resolve();
                  },
                },
              ],
            },
            {
              id: ErpFieldKeyMap?.erpStoreDeliveryPriceItemId,
              label: '商品档案',
              // rules: [
              //   {
              //     required: false,
              //     message: '门店查询数量超过十个时，请单选商品档案',
              //     validator: (rule, value) => {
              //       if (
              //         storeRef?.current?.length > 10 &&
              //         (value?.length !== 1 || !Array.isArray(value))
              //       ) {
              //         return Promise.reject(
              //           new Error('门店查询数量超过十个时，请单选商品档案'),
              //         );
              //       }
              //       return Promise.resolve();
              //     },
              //   },
              // ],
            },
            {
              id: ErpFieldKeyMap?.erpStoreDeliveryPriceItemCategoryIds,
              label: '商品类别',
              name: 'item_category_ids',
            },
            {
              id: 'erpCommonSelect',
              label: '配送价类型',
              name: 'type',
              fieldProps: {
                allowClear: true,
                options: types,
              },
            },
            {
              id: ErpFieldKeyMap?.erpStoreDeilveryPriceBusinessScope,
              name: 'business_scope_ids',
            },
            {
              id: 'erpCommonSelect',
              label: '单位',
              name: 'unit_type',
              fieldProps: {
                allowClear: false,
                options: queryUnit,
              },
            },
            {
              id: 'erpCommonSelect',
              label: '商品类型',
              name: 'item_types',
              fieldProps: {
                allowClear: true,
                mode: 'multiple',
                options: [
                  {
                    label: '主规格商品',
                    value: 'MAINSPEC',
                  },
                  {
                    label: '多规格商品',
                    value: 'MULTIPLESPEC',
                  },
                  {
                    label: '标准商品',
                    value: 'STANDARD',
                  },
                  {
                    label: '组合商品',
                    value: 'COMBINATION',
                  },
                  {
                    label: '成分商品',
                    value: 'COMPONENT',
                  },
                  {
                    label: '制单组合',
                    value: 'MAKEBILL',
                  },
                ],
              },
            },
            {
              id: ErpFieldKeyMap?.erpStoreDeliveryGroupId,
              itemSpan: 16,
            },
            {
              id: ErpFieldKeyMap?.erpStoreDeilveryPriceShowPanel,
              label: '其他条件',
              name: 'isShow',
            },
          ],
          initialValues: {
            unit_type: 'DELIVERY',
            one: 'price',
            three: 'purchasePrice',
          },
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.storedeliveryprice.page',
          selectMode: 'single',
          immediatePost: false,
          tableColumn: [
            {
              name: '序号',
              code: '_index',
              width: 80,
              align: 'center',
              lock: true,
            },
            {
              name: '组织',
              code: 'org_name',
              width: 140,
              hidden: !enable_organization,
              features: { sortable: true },
            },
            {
              name: '门店',
              code: 'store_name',
              width: 180,
              features: { sortable: true },
            },
            {
              name: '商品代码',
              code: 'code',
              width: 110,
              features: { sortable: true },
            },
            {
              name: '商品条码',
              code: 'bar_code',
              width: 140,
              features: { sortable: true },
            },
            {
              name: '商品名称',
              code: 'name',
              width: 280,
              features: { sortable: true },
            },
            {
              name: '采购规格',
              code: 'purchase_spec',
              width: 160,
              features: { sortable: true },
            },
            {
              name: '商品类型',
              code: 'item_type',
              width: 100,
              features: { sortable: true },
              render: (value: any, record: any) => {
                return (
                  <div className="info">
                    {goodsType.find((e) => e.value === value)?.label}
                  </div>
                );
              },
            },
            {
              name: '商品类别',
              code: 'category_name',
              width: 140,
              features: { sortable: true },
            },
            {
              name: '单位',
              code: 'unit',
              width: 80,
              features: { sortable: true },
            },
            {
              name: enable_organization ? '组织采购价' : '档案采购价',
              code: 'purchase_price',
              width: 120,
              align: 'right',
              features: { sortable: true },
              render: (value: any, record: any) => {
                return (
                  <div className="info">
                    {hasAuth(['门店配送价/档案采购价', '查询'])
                      ? Number(value)
                        ? Number(value).toFixed(4)
                        : value
                      : value}
                  </div>
                );
              },
            },
            {
              name: enable_organization ? '组织配送价' : '档案配送价',
              code: 'delivery_price',
              width: 120,
              align: 'right',
              features: { sortable: true },
              render: (value: any, record: any) => {
                return (
                  <div className="info">
                    {hasAuth(['门店配送价/档案配送价', '查询'])
                      ? Number(record.item_delivery_price)
                        ? Number(record.item_delivery_price).toFixed(4)
                        : record.item_delivery_price
                      : record.item_delivery_price}
                  </div>
                );
              },
            },
            {
              name: '配送价类型',
              code: 'type',
              width: 130,
              features: { sortable: true },
              render: (value: any, record: any, index: any) => {
                return record._click &&
                  hasAuth(['门店配送价/配送价', '编辑']) ? (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <XlbSelect
                      style={{ width: '100%' }}
                      defaultValue={value}
                      onChange={(e) => selectChange(e, record, index['index'])}
                    >
                      {record.store_number !== 99
                        ? types1.map((v: any, ind: number) => {
                            return (
                              <XlbSelect.Option key={ind} value={v.value}>
                                {v.label}
                              </XlbSelect.Option>
                            );
                          })
                        : types1.slice(1).map((v: any, ind: number) => {
                            return (
                              <XlbSelect.Option key={ind} value={v.value}>
                                {v.label}
                              </XlbSelect.Option>
                            );
                          })}
                    </XlbSelect>
                  </div>
                ) : (
                  <div className="info ">
                    {types1.find((e) => e.value === value)?.label}
                  </div>
                );
              },
            },
            {
              name: '数值',
              code: 'value',
              width: 100,
              align: 'right',
              features: { sortable: true },
              render: (value: any, record: any, index: any) => {
                return hasAuth(['门店配送价/配送价', '编辑']) &&
                  record._click &&
                  record.type !== 'NULL' &&
                  record.type !== null ? (
                  <XlbInput
                    key={record['value'] + index['index']}
                    className="full-box"
                    defaultValue={Number(value).toFixed(4)}
                    onFocus={(e) => e.target.select()}
                    onBlur={(e) => outFocus(e, record, index['index'])}
                    onChange={(e) => inputChange(e, record, index['index'])}
                  />
                ) : (
                  <div className="info ">
                    {hasAuth(['门店配送价/配送价', '查询'])
                      ? record.type === 'NULL' || record.type === null
                        ? '--'
                        : Number(value)?.toFixed(4)
                      : '****'}
                  </div>
                );
              },
            },
            {
              name: '配送价',
              code: 'price',
              width: 120,
              align: 'right',
              features: { sortable: true },
              render: (value: any, record: any, index: any) => {
                return (
                  <div className=" cursors">
                    {hasAuth(['门店配送价/配送价', '查询'])
                      ? record.type === 'NULL' || record.type === null
                        ? '--'
                        : Number(value)?.toFixed(4)
                      : value}
                  </div>
                );
              },
            },
            {
              name: '基本单位',
              code: 'basic_unit',
              width: 100,
              features: { sortable: true },
            },
            {
              name: '基本单位数值',
              code: 'basic_unit_value',
              width: 140,
              align: 'right',
              features: { sortable: true },
              render: (value: any, record: any, index: any) => {
                return hasAuth(['门店配送价/配送价', '编辑']) &&
                  record._click &&
                  record.type !== 'NULL' &&
                  record.type !== null ? (
                  <XlbInput
                    key={record['basic_unit_value'] + index['index']}
                    className="full-box"
                    defaultValue={value ? Number(value).toFixed(4) : ''}
                    onFocus={(e) => e.target.select()}
                    onBlur={(e) => outBasicFocus(e, record, index['index'])}
                    onChange={(e) =>
                      inputBasicChange(e, record, index['index'])
                    }
                  />
                ) : (
                  <div className="info ">
                    {hasAuth(['门店配送价/配送价', '查询'])
                      ? record.type === 'NULL' || record.type === null
                        ? '--'
                        : value
                          ? Number(value)?.toFixed(4)
                          : ''
                      : '****'}
                  </div>
                );
              },
            },
            {
              name: '基本单位配送价',
              code: 'basic_unit_price',
              width: 140,
              align: 'right',
              features: { sortable: true },
              render: (value: any, record: any, index: any) => {
                return (
                  <div className=" cursors">
                    {hasAuth(['门店配送价/配送价', '查询'])
                      ? record.type === 'NULL' || record.type === null
                        ? '--'
                        : Number(value)?.toFixed(4)
                      : value}
                  </div>
                );
              },
            },
            {
              name: '最后修改人',
              code: 'update_by',
              width: 150,
              features: { sortable: true },
            },
            {
              name: '最后修改时间',
              code: 'update_time',
              width: 180,
              features: { sortable: true, format: 'TIME' },
            },
          ],
          onSelectRow: (selectedRowKeys: any, selectedRows: any) => {
            setSelectedRows(selectedRows);
          },
        }}
        extra={(context) => {
          const {
            dataSource,
            loading,
            selectRow,
            requestForm,
            formValues,
            form,
            pagin,
          } = context;
          formValuesRef.current = formValues;
          formRef.current = form;

          return (
            <XlbButton.Group>
              {hasAuth(['门店配送价', '导入']) ? (
                <XlbButton
                  type="primary"
                  label="导入"
                  disabled={loading}
                  onClick={imports}
                  icon={
                    <IconFont name="daoru" color="currentColor" size={16} />
                  }
                ></XlbButton>
              ) : null}
              {hasAuth(['门店配送价', '导出']) ? (
                <XlbButton
                  label="导出"
                  type="primary"
                  disabled={!dataSource?.length || loading}
                  onClick={() => exportItem(pagin, form)}
                  icon={
                    <IconFont name="daochu" color="currentColor" size={16} />
                  }
                />
              ) : null}
              {hasAuth(['门店配送价', '编辑']) ? (
                <XlbButton
                  label="批量修改"
                  type="primary"
                  disabled={loading}
                  onClick={() => setBatchVisible(true)}
                  icon={
                    <IconFont name="xiugai" color="currentColor" size={16} />
                  }
                />
              ) : null}
              {hasAuth(['门店配送价', '编辑']) ? (
                <XlbButton
                  label="复制"
                  type="primary"
                  disabled={loading}
                  onClick={() => setCopyVisible(true)}
                  icon={
                    <IconFont name="fuzhi" color="currentColor" size={16} />
                  }
                />
              ) : null}
              <XlbButton
                label="修改记录"
                type="primary"
                disabled={!selectRow?.length}
                onClick={() => setHistoryVisible(true)}
                icon={<HistoryOutlined />}
              />
            </XlbButton.Group>
          );
        }}
      ></XlbProPageContainer>
    </>
  );
};

export default StoreDeliveryPrice;
