import { hasAuth } from '@/utils';
import { useIRouter } from '@/wujie/utils';
import { useLocation } from '@umijs/max';
import {
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbIcon,
  XlbInput,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { Image, message } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { f_advice, f_state, form_basic, form_price } from './data';
import styles from './index.less';
import Api from './server';

const PurchasePriceItem = () => {
  const { navigate } = useIRouter();
  const [form] = XlbBasicForm.useForm();
  const [details, setDetails] = useState<any>({});

  const auditClick = (type: string) => {
    XlbTipsModal({
      isCancel: true,
      title: type === 'pass' ? '审核通过' : '审核拒绝',
      tips: (
        <XlbBasicForm form={form} layout="inline">
          {type === 'refuse' && (
            <XlbBasicForm.Item
              label={'批复建议：'}
              name="advice"
              rules={[{ required: true, message: '请选择批复建议' }]}
            >
              <XlbSelect style={{ width: 210 }}>
                <XlbSelect.Option value={'BUDGET_NOT_ENOUGH'}>
                  内部预算不足
                </XlbSelect.Option>
                <XlbSelect.Option value={'VALUE_MAXIMIZE'}>
                  价值效益最大化
                </XlbSelect.Option>
                <XlbSelect.Option value={'OTHER'}>其他</XlbSelect.Option>
              </XlbSelect>
            </XlbBasicForm.Item>
          )}
          <XlbBasicForm.Item label={'备注：'} name="memo">
            <XlbInput.TextArea
              style={{ width: 210 }}
              placeholder="请输入"
              maxLength={300}
              autoSize={{ minRows: 4 }}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      ),
      onCancel: async () => {
        form.resetFields();
      },
      onOkBeforeFunction: async () => {
        try {
          await form.validateFields();
        } catch (err: any) {
          return false;
        }
        const data = {
          fid: details?.fid,
          reply_type:
            type === 'refuse' ? form.getFieldValue('advice') : undefined,
          audit_memo: form.getFieldValue('memo'),
        };

        const res: any =
          type === 'pass'
            ? await Api.passItem(data)
            : await Api.refuseItem(data);

        if (res.code === 0) {
          getDetails(details?.fid);
          message.success('操作成功');
          return true;
        }

        form.resetFields();
        return true;
      },
    });
  };

  // 获取数据
  const getDetails = async (id: string) => {
    const res = await Api.getItem({
      fid: id,
    });
    if (res?.code === 0) {
      const resultData = {
        ...res?.data,
        statistic_time: res?.data?.statistic_time
          ? moment(res?.data?.statistic_time).format('YYYY-MM-DD hh:mm')
          : '',
      };
      setDetails(resultData || {});
    }
  };

  const record = useLocation().state as any;
  useEffect(() => {
    getDetails(record?.id);
  }, [record]);

  const photoRender = (photolist: []) => {
    return (
      <div
        style={{
          width: '100%',
          display: 'flex',
          flexWrap: 'wrap',
          justifyContent: 'left',
          alignItems: 'center',
        }}
      >
        {photolist.map((e: any) => {
          return (
            <div style={{ display: 'inline-block', marginLeft: '12px' }}>
              <Image src={e.url} width={65} key={e.id} />
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div style={{ paddingLeft: '16px' }}>
      <div className={styles.itemButton}>
        <XlbButton.Group>
          {hasAuth(['采价计划', '批复']) ? (
            <XlbButton
              key="audit"
              type="primary"
              label="审核"
              disabled={details?.state !== 'INIT'}
              onClick={() => {
                auditClick('pass');
              }}
              icon={<XlbIcon name="shenhe" color="currentColor" size={16} />}
            />
          ) : null}

          {hasAuth(['采价计划', '批复']) ? (
            <XlbButton
              key="refuse"
              label="拒绝"
              type="primary"
              disabled={details?.state !== 'INIT'}
              onClick={() => {
                auditClick('refuse');
              }}
              icon={<XlbIcon name="shenhe" color="currentColor" size={16} />}
            />
          ) : null}
          <XlbButton
            key="return"
            label="返回"
            type="primary"
            onClick={() => navigate('/xlb_erp/PurchasePrice/index', false)}
            icon={<XlbIcon name="fanhui" color="currentColor" size={16} />}
          />
        </XlbButton.Group>
      </div>
      <XlbBlueBar title={'基本信息'} hasMargin />
      <div style={{ margin: '12px 0', position: 'relative' }}>
        <img
          style={{
            position: 'absolute',
            right: 20,
            top: -10,
            height: 78,
            width: 90,
          }}
          src={require(
            details?.state === 'INIT'
              ? './images/audit.png'
              : details?.state === 'REFUSE'
                ? './images/refuse.png'
                : './images/pass.png',
          )}
        />

        <div className={styles.header}>
          <div className={styles.headerIcon}>
            <span
              className="iconfont iconfont icon-shangpin"
              style={{ color: '#fff', fontSize: 20 }}
            />
          </div>
          <div>
            <span className={styles.notextTitle} style={{ marginRight: 10 }}>
              {details?.item_name}
            </span>
          </div>
        </div>
        <div className={styles.form_content} style={{ width: '100%' }}>
          {form_basic?.map((v, index) => (
            <div
              className={styles.s_form}
              key={index}
              style={{ width: 100 / 4 + '%' }}
            >
              <span style={{ fontSize: 14, color: '#86909C' }}>{v?.label}</span>
              <span style={{ fontSize: 14, color: '#1D2129' }}>
                {details[v?.value] || ''}
              </span>
            </div>
          ))}
        </div>
      </div>
      <XlbBlueBar title={'采价信息'} hasMargin />
      <div className={styles.form_content} style={{ width: '100%' }}>
        {form_price?.map((v, index) => (
          <div
            className={styles.s_form}
            key={index}
            style={{
              width: v?.label === '店铺位置：' ? '100%' : 100 / 3 + '%',
            }}
          >
            <span style={{ fontSize: 14, color: '#86909C' }}>{v?.label}</span>

            <span
              style={{
                fontSize: 14,
                color: v?.label?.includes('价格') ? '#F53F3F' : '#1D2129',
              }}
            >
              {v?.label?.includes('价格')
                ? hasAuth(['采价计划/零售价', '查询'])
                  ? details[v?.value]
                  : '****'
                : v?.label === '备注：'
                  ? details[v?.value] || '-'
                  : details[v?.value] || ''}
            </span>
          </div>
        ))}
        <div className={styles.s_form} style={{ width: '100%' }}>
          <span style={{ fontSize: 14, color: '#86909C' }}>图片：</span>
          {photoRender(details?.files || [])}
          {/* <XlbNewUploadFile
            columnNum={1}
            name="上传文件"
            disabled
            showUpload={false}
            fileList={details?.files || []}
            deleteByServer={false}
            accept={['image']}
            listType={'picture'}
          /> */}
        </div>
      </div>
      {details?.state !== 'INIT' && (
        <>
          <XlbBlueBar title={'审批信息'} hasMargin />
          <div className={styles.form_content} style={{ width: '50%' }}>
            <div className={styles.s_form} style={{ width: 100 / 2 + '%' }}>
              <span style={{ fontSize: 14, color: '#86909C' }}>审批结果：</span>
              <span
                style={{
                  fontSize: 14,
                  color: details?.state === 'APPROVE' ? '#00B42B' : '#F53F3F',
                }}
              >
                {f_state![details?.state] || ''}
              </span>
            </div>
            {details?.state === 'REFUSE' && (
              <div className={styles.s_form} style={{ width: 100 / 2 + '%' }}>
                <span style={{ fontSize: 14, color: '#86909C' }}>
                  批复建议：
                </span>
                <span
                  style={{
                    fontSize: 14,
                    color: '#1D2129',
                  }}
                >
                  {f_advice![details?.reply_type] || ''}
                </span>
              </div>
            )}
            <div className={styles.s_form} style={{ width: 100 / 2 + '%' }}>
              <span style={{ fontSize: 14, color: '#86909C' }}>备注：</span>
              <span
                style={{
                  fontSize: 14,
                  color: '#1D2129',
                }}
              >
                {details?.audit_memo || '-'}
              </span>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default PurchasePriceItem;
