import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbDatePicker,
  XlbInput,
  XlbModal,
  XlbSelect,
  XlbTable,
  type XlbTableColumnProps,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { Row, Select, Tooltip } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { itemTableListDetail, Options1 } from './data';
const { Option } = Select;
const DeliveryOrderItem = (props: any) => {
  console.log('🚀 ~ DeliveryOrderItem ~ props:', props);
  const modal = NiceModal.useModal();
  const { orderId } = props;
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    itemTableListDetail.map((i) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == 'operation' ? true : i.hidden,
    })) as any[],
  );
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const formBox = useRef<HTMLDivElement | null>(null);
  const [footerData, setFooterData] = useState<any[]>([]);

  const [form] = XlbBasicForm.useForm();
  const [info, setInfo] = useState({
    state: 'INIT',
  });
  const InvoiceRender = (item: XlbTableColumnProps<any>) => {
    switch (item.code) {
      // case 'index':
      //   item.render = (value: any, record: any, index: number) => {
      //     return value === '合计' ? (
      //       <div className="info overwidth">{value}</div>
      //     ) : (
      //       <div className="info overwidth">{(pagin.pageNum - 1) * pagin.pageSize + 1 + index}</div>
      //     )
      //   }
      //   break
      case 'item_name':
        item.render = (value, record, index) => {
          return (
            <div>
              <span>{value}</span>
              <span
                style={{
                  fontSize: 12,
                  color: '#ff8400',
                  border: '1px solid #ff8400',
                  borderRadius: 3,
                  marginLeft: 5,
                  padding: '1px 2px',
                  visibility: !record.special_fid ? 'hidden' : 'visible',
                }}
              >
                {'特价'}
              </span>
            </div>
          );
        };
        break;

      case 'tax_money':
      case 'cost_money':
      case 'no_tax_cost_money':
        item.render = (value, record, index) => {
          return (
            <div>{hasAuth(['调出单/成本价', '查询']) ? value : '****'}</div>
          );
        };
        break;
      case 'money': //金额(含税)
        item.render = (value, record, index) => {
          return (
            <div>{hasAuth(['调出单/配送价', '查询']) ? value : '****'}</div>
          );
        };
        break;
      case 'sale_price':
        item.render = (value, record, index) => (
          <div>{hasAuth(['调出单/零售价', '查询']) ? value : '****'}</div>
        );
        break;
      case 'price': //单价(含税)
      case 'basic_price': //基本单价(含税)
        item.render = (value, record, index) => (
          <div>{hasAuth(['调出单/配送价', '查询']) ? value : '****'}</div>
        );
        break;
      case 'origin_price':
        item.render = (value, record, index) => {
          return info.state !== 'INIT' && record.basic_original_price ? (
            hasAuth(['调出单/配送价', '查询']) ? (
              <div>
                {Number(
                  safeMath.multiply(
                    Number(record.basic_original_price),
                    Number(record.ratio),
                  ),
                ).toFixed(4)}
              </div>
            ) : (
              '****'
            )
          ) : null;
        };
        break;
      case 'special_fid':
        item.render = (value, record, index) => {
          return info.state !== 'INIT' && record.special_fid ? (
            <div>
              <span
                className="link cursors"
                //     onClick={(e) => {
                //       e.stopPropagation()
                //       go('/xlb_erp/deliverySpecialPrice/item', { fid: value})
                // }}
              >
                {value}
              </span>
            </div>
          ) : null;
        };
        break;
      case 'memo':
        item.render = (value) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div>{value}</div>
            </Tooltip>
          );
        };
        break;
    }
  };

  //读取信息
  const readinfo = async (fid: any, summary: boolean = false) => {
    setIsLoading(true);
    const data = {
      fid: fid,
      summary: summary,
    };
    const res = await XlbFetch.post('/erp/hxl.erp.deliveryoutorder.read', data);
    setIsLoading(false);
    if (res.code === 0) {
      res.data.details.map((v: any) => {
        v.money = v.money?.toFixed(2);
        v.price = v.price?.toFixed(4);
        v.tax_rate = v.tax_rate ? v.tax_rate : 0;
        v.tax_money = v.tax_money?.toFixed(2);
        v.tare = v.tare ? v.tare.toFixed(3) : '0.000';
        v.ratio = v.ratio.toFixed(3);
        v.basic_price = v.basic_price?.toFixed(4);
        v.quantity = v.quantity?.toFixed(3);
        v.basic_quantity = v.basic_quantity?.toFixed(3);
        v.present_quantity = v.present_quantity
          ? v.present_quantity?.toFixed(3)
          : '0.000';
        v.basic_stock_quantity = v.basic_stock_quantity
          ? v.basic_stock_quantity.toFixed(3)
          : '0.000';
        v.basic_available_stock_quantity = v.basic_available_stock_quantity
          ? v.basic_available_stock_quantity.toFixed(3)
          : '0.000';
        const basic_unit = JSON.stringify({ name: v.basic_unit, ratio: 1 });
        const delivery_unit = JSON.stringify({
          name: v.delivery_unit,
          ratio: v.delivery_ratio,
        });
        const purchase_unit = JSON.stringify({
          name: v.purchase_unit,
          ratio: v.purchase_ratio,
        });
        const stock_unit = JSON.stringify({
          name: v.stock_unit,
          ratio: v.stock_ratio,
        });
        const wholesale_unit = JSON.stringify({
          name: v.wholesale_unit,
          ratio: v.wholesale_ratio,
        });
        v.units = Array.from(
          new Set([
            basic_unit,
            delivery_unit,
            purchase_unit,
            stock_unit,
            wholesale_unit,
          ]),
        );
      });
      setInfo({ state: res.data.state });

      setRowData(res.data.details);
      setPagin({
        ...pagin,
        total: res.data.details.length,
      });
      form.setFieldsValue({
        in_store_name: res.data.in_store_name,
        in_store_id: res.data.in_store_id,
        store_name: res.data.store_name,
        store_id: res.data.store_id,
        storehouse_name: res.data.storehouse_name,
        item_dept_names: res.data.item_dept_names,
        fid: res.data.fid,
        operate_date: res.data.operate_date
          ? moment(res.data.operate_date)
          : null,
        payment_date: res.data.payment_date
          ? moment(res.data.payment_date)
          : null,
        store_order: res.data.request_orders?.map((v: any) => v.fid).join(','),
        memo: res.data.memo,
        create_by: res.data.create_by,
        create_time: res.data.create_time?.slice(0, 10),
        audit_by: res.data.audit_by,
        audit_time: res.data.audit_time?.slice(0, 10),
        updata_by: res.data.updata_by,
        updata_time: res.data.updata_time?.slice(0, 10),
      });
    }
  };

  useEffect(() => {
    // 设置合计行
    footerData[0] = {};
    footerData[0].index = '合计';
    footerData[0].money = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.money), 0)
      .toFixed(2);
    footerData[0].quantity = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.quantity), 0)
      .toFixed(3);
    footerData[0].tax_money = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.tax_money), 0)
      .toFixed(2);
    footerData[0].basic_quantity = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.basic_quantity), 0)
      .toFixed(3);
    footerData[0].present_quantity = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.present_quantity), 0)
      .toFixed(3);
    setFooterData([...footerData]);
  }, [JSON.stringify(rowData)]);

  useEffect(() => {
    itemArrdetail.map((v) => {
      v.name === '操作' ? (v.hidden = true) : null;
    });
    readinfo(orderId);
  }, []);

  itemArrdetail.map((v) => InvoiceRender(v));

  return (
    <XlbModal
      width={1100}
      open={modal.visible}
      title={'调出单详情'}
      isCancel={true}
      onOk={async () => {
        modal.hide();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
      maskClosable
      footer={null}
    >
      <div
        className="modelBox col-flex"
        style={{ height: '100%', overflowY: 'auto', overflowX: 'hidden',margin: '10px 0' }}
      >
        <header>
          <div ref={formBox}>
            <XlbBasicForm colon form={form} autoComplete="off" layout="inline">
              <div className="row-flex" style={{ width: '1300px' }}>
                <div style={{ width: '90%' }}>
                  <Row>
                    <XlbBasicForm.Item label="调入门店" name="in_store_name">
                      <XlbInput
                        style={{ width: '180px', padding: '0 11px' }}
                        disabled={true}
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="调出门店" name="store_name">
                      <XlbInput
                        style={{ width: '180px', padding: '0 11px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="调出仓库" name="storehouse_name">
                      <XlbSelect style={{ width: 180 }} disabled>
                        {/* {[].map((v, i) => {
                            return (
                              <Option key={i} value={v.value}>
                                {v.label}
                              </Option>
                            )
                          })} */}
                      </XlbSelect>
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="单据号" name="fid">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="单据状态">
                      <XlbInput
                        disabled
                        size="small"
                        value={
                          Options1.find((s) => s.value === info.state)?.label
                        }
                        style={{ width: '180px' }}
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="商品部门" name="item_dept_names">
                      <XlbInput
                        disabled
                        size="small"
                        style={{ width: '180px' }}
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="调出日期" name="operate_date">
                      <XlbDatePicker style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="付款日期" name="payment_date">
                      <XlbDatePicker style={{ width: '180px' }} disabled />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="门店补货单" name="store_order">
                      <XlbInput
                        style={{ width: '180px', padding: '0 11px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="留言备注" name="memo">
                      <XlbInput style={{ width: '480px' }} disabled />
                    </XlbBasicForm.Item>
                  </Row>
                </div>
                <div style={{ width: '10%' }}></div>
              </div>
            </XlbBasicForm>
          </div>
        </header>
        <XlbTable
          isLoading={isLoading}
          dataSource={rowData}
          columns={itemArrdetail}
          total={rowData?.length ?? 0}
          keepDataSource={false}
          showSearch
          style={{ flex: 1 }}
        />
      </div>
    </XlbModal>
  );
};

export default DeliveryOrderItem;
