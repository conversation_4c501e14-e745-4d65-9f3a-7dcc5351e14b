import React from 'react'

/**
 * 流文件转化为对应格式的文件
 */
class Download {
  public blob?: Blob;

  [x: string]: string
  constructor() {
    this.blob = undefined
    this.filename = ''
  }

  public zip(res: any) {
    this.blob = new Blob([res], { type: 'zip;charset=utf-8' })
    this.down()
  }

  public xlsx(res: any) {
    this.blob = new Blob([res], { type: 'application/vnd.ms-excel' })
    this.down()
  }

  public down() {
    if (this.blob) {
      let url = window.URL.createObjectURL(this.blob)
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.download = `${this.filename}`
      document.body.appendChild(link)
      link.click()
      window.URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
  }

  public downByProgress(e: React.MouseEvent) {
    const dom = e.target as HTMLElement
    const domPositionInfo = dom.getBoundingClientRect()
    const { x, y, width, height } = domPositionInfo
    console.log(x, y, width, height)
    const div = document.createElement('div')
    div.className = 'xlb-download-progress-container'

    const target = document.querySelector('#header-download')!
    const targetInfo = target.getBoundingClientRect()

    div.style.position = 'fixed'
    div.style.left = x + 'px'
    div.style.top = y + 'px'
    div.style.width = '40px'
    div.style.height = '40px'
    div.style.borderRadius = '50%'
    div.style.backgroundColor = '#F2F3F5'
    div.style.display = 'flex'
    div.style.justifyContent = 'center'
    div.style.alignContent = 'center'
    div.style.transition = 'left .6s linear, top .6s cubic-bezier(0.5, -0.5, 1, 1)'

    const span = document.createElement('span')
    span.className = 'iconfont icon-xiazai'
    span.style.color = '#3D66FE'
    span.style.fontSize = '20px'

    div.appendChild(span)
    document.body.appendChild(div)

    div.addEventListener(
      'transitionend',
      () => {
        document.body.removeChild(div)
      },
      { once: true }
    )

    setTimeout(() => {
      div.style.left = targetInfo.x + 'px'
      div.style.top = targetInfo.y + 'px'
    }, 200)
  }
}

export default Download
