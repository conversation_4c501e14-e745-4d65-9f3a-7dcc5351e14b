.imgModel_Box {
  display: flex;
  min-height: 297px;
  padding-bottom: 32px;
  .left_Box {
    flex-shrink: 0;
    width: 300px;
    height: 240px;
    margin-top: 20px;
    text-align: center;
    .img_item {
      display: flex;
    }
    .count {
      margin-top: 20px;
      font-size: 20;
    }
    .null_Box {
      margin: 15px auto;
      font-size: 36px;
      text-align: center;
    }
    .img_lunbo {
      display: flex;
      align-items: center;
      width: calc(100% - 16px); //
      margin-top: 12px;
      & > .img_list {
        flex: 1;
        overflow: hidden;
      }
    }
    .icon_img_action {
      cursor: pointer;
      &:hover {
        color: #3d66fe;
      }
    }
  }
  .right_box {
    width: 500px;
    padding-left: 10px;
    .title {
      margin-top: 20px;
      margin-bottom: 16px;
      padding-left: 10px;
      color: #1f2126;
      font-weight: 500;
      font-size: 20px;
      font-family: PingFang SC;
      line-height: 26px;
      letter-spacing: 0px;
    }
    .detail_content {
      display: flex;
      flex-wrap: wrap;
      padding-left: 10px;
      row-gap: 12px;
      & > p {
        width: 50%;
      }
    }
    .text_label {
      padding-right: 4px;
      color: #86909c;
      font-weight: normal;
      font-size: 14px;
      font-family: PingFang SC;
      line-height: 20px;
      letter-spacing: 0px;
    }
    .text {
      color: #1d2129;
      font-weight: normal;
      font-size: 14px;
      font-family: PingFang SC;
      line-height: 20px;
      letter-spacing: 0px;
    }
  }
  .img_list_div {
    display: flex;
  }
  .img_item_span {
    display: inline-block;
    display: flex;
    justify-content: center;
    width: 36px;
    height: 36px;
    margin-right: 6px;
    text-align: center;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
  }
  .img_lunbo_item {
    width: 36px;
    height: 36px;
    object-fit: cover;
    background: #d8d8d8;
  }
  .label_style {
    color: #86909c;
    font-weight: normal;
    font-size: 14px;
    font-family: PingFang SC;
    line-height: 20px;
    letter-spacing: 0px;
  }
}
