import { XlbIcon, XlbTableColumnProps } from '@xlb/components';
import { Tooltip } from 'antd';
import styles from './index.less';
export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 70,
    lock: true,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
  },
  {
    name: '公司抬头',
    code: 'title',
    features: { sortable: true, details: true },
    width: 190,
    render: (text: string, record: any) => {
      return (
        <div className={styles.companyTitle}>
          <Tooltip title={text}>
            <a
              className={styles.companyTitleText}
              //   onClick={(e) => {
              //     e.stopPropagation();
              //     // handleChangeItem('update', record);
              //   }}
            >
              {text}
            </a>
          </Tooltip>
          {!record?.is_default ? null : (
            <Tooltip title="默认抬头">
              <span>
                <XlbIcon name="xingshi" color={'#FCB90F'} />
              </span>
            </Tooltip>
          )}
        </div>
      );
    },
  },
  {
    name: '公司税号',
    code: 'tax_num',
    features: { sortable: true },
    width: 312,
  },
  {
    name: '法人姓名',
    code: 'legal_person',
    features: { sortable: true },
    width: 102,
  },
  // {
  //   name: "开户行",
  //   code: "bank",
  //   features: { sortable: true },
  //   width: 116,
  // },
  // {
  //   name: "银行账号",
  //   code: "bank_account",
  //   features: { sortable: true },
  //   width: 256,
  // },
  {
    name: '开票员',
    code: 'clerk',
    features: { sortable: true },
    width: 112,
  },
  {
    name: '备注',
    code: 'memo',
    features: { sortable: true },
    width: 312,
  },
];
