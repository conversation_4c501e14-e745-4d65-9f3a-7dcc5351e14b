import {XlbFetch as ErpRequest } from '@xlb/utils'

// 根据门店ids获取仓库信息
const getStoreHouseList = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.storehouse.page', data)
}

// 汇总导出
const exportSummary = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockexpiredchecksummary.export', data)
}

// 明细导出
const exportDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockexpiredcheck.export', data)
}

export default {
  getStoreHouseList,
  exportSummary,
  exportDetail
}
