import { XlbFetch as ErpRequest } from '@xlb/utils'
import NiceModal from '@ebay/nice-modal-react'
import type { BaseModalProps } from '@xlb/components'
import { XlbBasicForm, XlbModal, XlbTree } from '@xlb/components'
import { Skeleton, Spin } from 'antd'
import type { FC } from 'react'
import { useEffect, useRef, useState } from 'react'

interface Props extends Pick<BaseModalProps, 'title'> {
  selectKeys: any[]
  org_names: string
  postData?: any
  isMultiple?: boolean
}

const Modal: FC<Props> = ({
  title = '选择组织',
  selectKeys,
  postData,
  org_names,
  isMultiple = true
}) => {
  const [form] = XlbBasicForm.useForm()
  const modal = NiceModal.useModal()
  const [treeData, setTreeData] = useState<any[]>([])
  const [checkedKeys, setCheckedKeys] = useState<any[]>(selectKeys)
  const [isShowTree, setIsShowTree] = useState(true)
  const getTreeData = async () => {
    setIsShowTree(true)
    const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.tree', {
      data: postData || {}
    })
    if (res.code === 0) {
      const sortedData = res.data?.sort((a: any, b: any) => a.name.localeCompare(b.name))
      setTreeData(sortedData)
      if (selectKeys?.length) {
        const item = res.data?.find((i: any) => i.id === selectKeys[0])
        form.setFieldsValue({
          level: item?.level
        })
      }
    }
    setIsShowTree(false)
  }

  useEffect(() => {
    if (modal.visible) {
      setCheckedKeys(selectKeys)
      getTreeData()
    }
  }, [modal.visible])

  return (
    <XlbModal
      width={390}
      open={modal.visible}
      title={title}
      isCancel={true}
      keyboard={false}
      onOk={async () => {
        modal.resolve(form.getFieldsValue(true))
        modal.hide()
      }}
      onCancel={() => {
        modal.resolve(false)
        modal.hide()
      }}
    >
      {isShowTree ? (
        <div
          style={{
            height: 445,
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <Spin size="large" />
        </div>
      ) : (
        <div style={{ height: 445, width: '100%' }}>
          <XlbBasicForm
            form={form}
            style={{ width: '100%', margin: '12px 0 -8px 0' }}
            layout="horizontal"
            initialValues={{
              org_ids: selectKeys,
              org_names: org_names
            }}
          >
            <XlbBasicForm.Item dependencies={['level']}>
              {({ getFieldValue }) => (
                <div style={{ height: '405px' }}>
                  <XlbTree
                    style={{ maxHeight: 370, overflowY: 'hidden' }}
                    hideHeaderRight={false}
                    dataSource={
                      treeData?.length
                        ? postData?.level
                          ? [...treeData?.filter((item) => item.level === postData?.level)]
                          : [...treeData]
                        : []
                    }
                    dataType="lists"
                    treeConfig={{
                      multiple: isMultiple,
                      checkable: isMultiple,
                      checkedKeys: getFieldValue('org_ids')
                    }}
                    onSelect={(selectedKeys) => {
                      console.log('🚀 ~ selectedKeys:', selectedKeys)
                      if (!isMultiple) {
                        setCheckedKeys([selectedKeys.id])
                        form.setFieldsValue({
                          org_ids: [selectedKeys.id],
                          org_names: selectedKeys.name
                        })
                      }
                    }}
                    onCheck={(data) => {
                      if (isMultiple) {
                        const ids = data?.map((i: any) => i.id)
                        setCheckedKeys(ids)
                        form.setFieldsValue({
                          org_ids: ids,
                          org_names: data?.map((i: any) => i.name).join(',')
                        })
                      }
                    }}
                  />
                </div>
              )}
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      )}
    </XlbModal>
  )
}

export default Modal