import { XlbFetch as ErpRequest, LStorage } from '@xlb/utils';
const userInfo = LStorage.get('userInfo');
export default {
  //新增区域
  addInfo: async (data: any) => {
    return await ErpRequest.post(
      '/erp-mdm/hxl.erp.delivery.cargo.owner.conf.save',
      data,
    );
  },
  // 更新
  updateInfo: async (data: any) => {
    return await ErpRequest.post(
      '/erp-mdm/hxl.erp.delivery.cargo.owner.conf.update',
      data,
    );
  },
  // 批量删除
  batchDelete: async (data: any) => {
    return await ErpRequest.post(
      '/erp-mdm/hxl.erp.delivery.cargo.owner.conf.batchdelete',
      data,
    );
  },
  // 复制
  copyInfo: async (data: any) => {
    return await ErpRequest.post(
      '/erp-mdm/hxl.erp.delivery.cargo.owner.conf.copy',
      data,
    );
  },

  // 同步
  syncData: async (data: any) => {
    console.log(userInfo);
    return await ErpRequest.post(
      '/erp-mdm/hxl.erp.delivery.cargo.owner.conf.sync',
      { ...data, company_id: userInfo.company_id },
    );
  },
};
