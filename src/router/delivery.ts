interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/xlb_erp/saleParam/index',
    component: '@/pages/delivery/saleParam/index',
    title: '配送参数',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'saleParam',
  },
  {
    path: '/xlb_erp/stockTypeSetting/index',
    component: '@/pages/delivery/stockTypeSetting/header/index',
    title: '备货类型设置',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'stockTypeSetting',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/forceDeliveryRule/index',
    component: '@/pages/delivery/forceDeliveryRule/index',
    title: '自动统配规则',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'forceDeliveryRule',
  },
  {
    path: '/xlb_erp/receivingReason/index',
    component: '@/pages/delivery/receivingReason/index',
    title: '领用原因',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'receivingReason',
  },
  {
    path: '/xlb_erp/receivingApplication/index',
    component: '@/pages/delivery/receivingApplication/index',
    title: '领用申请单',
    subTitle: '业务操作',
    subMenu: 'distribution',
    tabClass: 'receivingApplication',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/returnRateStatistics/index',
    component: '@/pages/delivery/returnRateStatistics/index',
    title: '退货率统计',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'returnRateStatistics',
  },
  {
    path: '/xlb_erp/directSupplyPoint/index',
    component: '@/pages/delivery/directSupplyPoint/index',
    title: '直供加点配置',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'directSupplyPoint',
  },
  {
    path: '/xlb_erp/storeOrderSetting/index',
    component: '@/pages/delivery/storeOrderSetting/header/index',
    title: '门店订单配置',
    subTitle: '业务设置',
    isWujie: true,
    wujiePath: 'xlb_erp+storeOrderSetting+index',
    subMenu: 'distribution',
    tabClass: 'storeOrderSetting',
  },
  {
    path: '/xlb_erp/storeOrderingDate/index',
    component: '@/pages/delivery/storeOrderingDate/header/index',
    title: '门店下单日',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'storeOrderingDate',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/storeDeliveryDay/index',
    component: '@/pages/delivery/storeDeliveryDay/index',
    title: '门店配送日',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'storeDeliveryDay',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/transferWay/index',
    component: '@/pages/delivery/transferWay/index',
    title: '物资在途',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'transferWay',
  },
  {
    path: '/xlb_erp/strongDataAnalysis/index',
    component: '@/pages/delivery/strongDataAnalysis/index',
    title: '统配数据分析',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'strongDataAnalysis',
  },
  {
    path: '/xlb_erp/replenishGoodsAnalysis/index',
    component: '@/pages/delivery/replenishGoodsAnalysis/index',
    title: '补货商品分析',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'replenishGoodsAnalysis',
  },
  {
    path: '/xlb_erp/stockPrediction/index',
    component: '@/pages/delivery/stockPrediction/index',
    title: '备货预测',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'stockPrediction',
  },
  {
    path: '/xlb_erp/stockForecasts/index',
    component: '@/pages/delivery/stockForecasts/index',
    title: '备货预测系数',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'stockForecasts',
  },
  {
    path: '/xlb_erp/deliveryanalysis/index',
    component: '@/pages/delivery/deliveryanalysis/index',
    title: '到货率分析',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'deliveryanalysis',
  },
  {
    path: '/xlb_erp/storeDeliveryPrice/index',
    component: '@/pages/delivery/storeDeliveryPrice/index',
    title: '门店配送价',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'storeDeliveryPrice',
  },
  {
    path: '/xlb_erp/deliverySpecialPrice/index',
    component: '@/pages/delivery/deliverySpecialPrice/header/index',
    title: '配送特价',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'deliverySpecialPrice',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/basketStat/index',
    component: '@/pages/delivery/basketStat/index',
    title: '物资统计',
    subTitle: '数据查询',
    subMenu: 'distribution',
    // hidden: true,
    tabClass: 'basketStat',
  },
  {
    path: '/xlb_erp/deliverySpecialPriceAnalysis/index',
    component: '@/pages/delivery/deliverySpecialPriceAnalysis/index',
    title: '配送特价分析',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'deliverySpecialPriceAnalysis',
  },
  {
    path: '/xlb_erp/goodsway/index',
    component: '@/pages/delivery/goodsway/index',
    title: '商品在途',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'goodsway',
  },
  {
    path: '/xlb_erp/supplyAnalyze/index',
    component: '@/pages/delivery/supplyAnalyze/index',
    title: '直供分析',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'supplyAnalyze',
  },
  {
    path: '/xlb_erp/deliveryDetails/index',
    component: '@/pages/delivery/deliveryDetails/index',
    title: '配送商品明细',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'deliveryDetails',
  },
  {
    path: '/xlb_erp/transferDocument/index',
    component: '@/pages/delivery/transferDocument/index',
    title: '物资进出单',
    subTitle: '物资管理',
    subMenu: 'distribution',
    tabClass: 'transferDocument',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/distributionGross/index',
    component: '@/pages/delivery/distributionGross/index',
    title: '配送分析',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'distributionGross',
  },
  {
    path: '/xlb_erp/deliveryPriceMange/index',
    component: '@/pages/delivery/deliveryPriceMange/header/index',
    title: '配送价调整',
    subTitle: '业务设置',
    subMenu: 'distribution',
    wrappers: ['@/pages/public/proPageProvide'],
    tabClass: 'deliveryPriceMange',
  },
  {
    path: '/xlb_erp/collectDocument/index',
    component: '@/pages/delivery/collectDocument/index',
    title: '领用进出单',
    subTitle: '业务操作',
    subMenu: 'distribution',
    tabClass: 'collectDocument',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/warehouseStoreRelation/index',
    component: '@/pages/delivery/warehouseStoreRelation/index',
    title: '仓店关系记录',
    subTitle: '数据查询',
    subMenu: 'distribution',
    tabClass: 'warehouseStoreRelation',
  },
  {
    path: '/xlb_erp/returnReason/index',
    component: '@/pages/delivery/returnReason/index',
    title: '退货申请原因',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'returnReason',
  },
  {

    path: '/xlb_erp/storeItemReplenish/index',
    component: '@/pages/delivery/storeItemReplenish/header/index',
    title: '补货参考值',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'storeItemReplenish',
  },
  {
    path: '/xlb_erp/deliveryCenterStore/index',
    component: '@/pages/delivery/deliveryCenterStore/index',
    title: '配送组织设置',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'deliveryCenterStore',
  },
  {
    path: '/xlb_erp/advancePosition/index',
    component: '@/pages/delivery/advancePosition/header/index',
    title: '前置仓配置',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'advancePosition',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/replenishTemplate/index',
    component: '@/pages/delivery/replenishTemplate/header/index',
    title: '补货模板',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'replenishTemplate',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/transferBasket/index',
    component: '@/pages/delivery/transferBasket/header/index',
    title: '物资载具',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'transferBasket',
  },
  {
    path: '/xlb_erp/storeOrders/index',
    component: '@/pages/delivery/storeOrders/index',
    title: '门店订单',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'storeOrders',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/marketingCampaign/index',
    component: '@/pages/delivery/marketingCampaign/index',
    title: '配送营销活动',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'marketingCampaign',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/warehousingDistribution/index',
    component: '@/pages/delivery/warehousingDistribution/index',
    title: '仓配单位维护',
    subTitle: '业务设置',
    subMenu: 'distribution',
    tabClass: 'warehousingDistribution',
  },
];

export { routeList as deliveryRouteList };