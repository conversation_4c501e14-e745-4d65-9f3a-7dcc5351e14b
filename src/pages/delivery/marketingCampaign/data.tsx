import type { XlbTableColumnProps } from '@xlb/components'
import { columnWidthEnum } from '@/data/common/constant'
export const InputDialogColumns: XlbTableColumnProps<any>[] = [
  {
    name: '代码',
    code: 'store_code',
    width: 80
  },
  {
    name: '名称',
    code: 'store_name',
    width: 150
  },
  {
    name: '营业执照名称',
    code: 'license_name',
    width: 240
  },
  {
    name: '执照类型',
    code: 'license_type',
    width: 100,
    render(text) {
      const obj: any = {
        COMPANY: '公司',
        PERSONAL: '个人'
      }
      return obj[text]
    }
  },
  {
    name: '门店分组',
    code: 'store_group',
    width: 100,
    render(text,record) {
      return text?.name || record?.store_group_name || ''
    }
  },
  {
    name: '配送类型',
    code: 'delivery_type',
    width: 80,
    render(text) {
      const obj: any = {
        JOIN: '加盟',
        DIRECT: '直营'
      }
      return obj[text]
    }
  },
  {
    name: '经营类型',
    code: 'management_type',
    width: 80,
    render(text) {
      const obj: any = {
        0: '直营',
        1: '加盟'
      }
      return obj[text]
    }
  }
]

//单据状态
//INIT 制单
//AUDIT 审核
//HANDLE 处理通过
//HANDLE_REFUSE 处理拒绝
//EFFECT 生效
//EXPIRE 失效
//TERMINATE 处理中止
//INVALID 作废
export const Options1 = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
  {
    label: '处理通过',
    value: 'HANDLE',
    type: 'success',
  },
  {
    label: '处理拒绝',
    value: 'HANDLE_REFUSE',
    type: 'danger',
  },
  {
    label: '生效',
    value: 'EFFECT',
    type: 'success',
  },
  {
    label: '失效',
    value: 'EXPIRE',
    type: 'danger',
  },
  {
    label: '处理中止',
    value: 'TERMINATE',
    type: 'danger',
  },
  {
    label: '作废',
    value: 'INVALID',
    type: 'danger',
  },
];
// 时间类型
export const Options3 = [
  {
    label: '制单时间',
    value: 'create_date'
  },
  {
    label: '审核时间',
    value: 'audit_date'
  },
  {
    label: '活动开始时间',
    value: 'start_date'
  },
  {
    label: '活动结束时间',
    value: 'end_date'
  }
]
export const priceType = [
  {
    label: '按比例',
    value: 'RATIO'
  },
  {
    label: '按金额',
    value: 'MONEY'
  },
  {
    label: '固定金额',
    value: 'FIXED_MONEY'
  }
]
//商品类型
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  }
]

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '应用门店',
    code: 'stores',
    width: 160,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '单据状态',
    code: 'campaign_status',
    width: columnWidthEnum.ORDER_STATE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '应用状态',
    code: 'supply_state',
    width: 100,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '应用时间',
    code: 'supply_date',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '制单人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '处理人',
    code: 'handle_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '处理时间',
    code: 'handle_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '留言备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '失效时间',
    code: 'invalid_time',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '失效人',
    code: 'invalid_by',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left'
  }
]

//商品明细
export const itemTableListDetail: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '操作',
    code: '_operator',
    align: 'center',
    width: 80
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true, showShort: true },
    align: 'left'
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true }
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 120,
    features: { sortable: true }
  },
  {
    name: '配送单位',
    code: 'unit',
    width: 110,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '组织采购价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '配送价类型',
    code: 'type',
    width: 130,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '数值',
    code: 'value',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '配送价',
    code: 'price',
    width: 120,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left'
  }
]
//明细表格
export const dateGoodsColumns: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '配送单位',
    code: 'unit',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '档案采购价',
    code: 'purchase_price',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '配送价类型',
    code: 'type',
    width: 120,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '数值',
    code: 'value',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '配送价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right'
  }
]
