import NiceModal from '@ebay/nice-modal-react';
import { XlbModal } from '@xlb/components';
import './activeModal.less';

interface IProps {
  activeList: string[];
}
export const activeModal = NiceModal.create((props: IProps) => {
  const { activeList } = props;
  const modal = NiceModal.useModal();

  return (
    <XlbModal
      title="促销活动"
      width={600}
      open={modal.visible}
      isCancel={true}
      footer={null}
      maskClosable
      onCancel={() => {
        modal.hide();
      }}
    >
      <div className="active-modal-container">
        {activeList.map((item, index) => (
          <span key={index}>{item}</span>
        ))}
      </div>
    </XlbModal>
  );
});
