import { hasAuth } from '@/utils/kit';
import {
  XlbButton,
  XlbCheckbox,
  XlbIcon,
  XlbSelect,
  XlbTooltip,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message, TimePicker } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { addNewBuyparam, getBuyparam } from '../server';
import styles from './index.less';

const BuyParam = () => {
  const format = 'HH:mm';
  const selectDom = useRef<HTMLSelectElement | null>(null);
  //配送日规则

  const [ischecked1, setchecked1] = useState(false);
  const [
    is_not_available_stock_disable_order,
    setnot_available_stock_disable_order,
  ] = useState(false);
  const [no_display_no_valid_stock_item, setno_display_no_valid_stock_item] =
    useState(false);
  //批发订单有效天数
  const [days, setDays] = useState(0);
  //批发销售单制单有效天数
  const [days2, setDays2] = useState(0);
  // 超期未订天数
  const [overdue, setOverdue] = useState<number>(2);
  const [isLoading, setisLoading] = useState<boolean>(false);

  const [ischecked2, setchecked2] = useState(true);
  const [ischecked3, setchecked3] = useState(false);
  const [bookOrderShowCenterStock, setBookOrderShowCenterStock] =
    useState(false);
  const [headerItem, setHeaderItem] = useState({
    value11: '23:59',
  });
  const [dayselect, setdayselect] = useState<any>(0);
  const [wholesale_order_price_type, setwholesale_order_price_type] =
    useState<any>(1);
  const [orgIds, setOrgIds] = useState<number[]>([]);
  const [orgList, setOrgList] = useState<any[]>([]);

  const [info, setInfo] = useState<any>({});

  const getbuyParameter = async () => {
    const ress = await getBuyparam({});
    if (ress.code == '0') {
      const _data = ress.data;
      setchecked1(_data.book_order_check_stock);
      setnot_available_stock_disable_order(
        _data.not_available_stock_disable_order,
      );
      setno_display_no_valid_stock_item(_data?.no_display_no_valid_stock_item);
      setchecked2(_data.return_order_use_cost_price_in);
      setchecked3(_data?.book_order_audit_create_sale_order);
      setHeaderItem({
        ...headerItem,
        value11: _data.delivery_time ? _data.delivery_time : '23:59',
      });
      setDays(_data.book_order_valid_days);
      setDays2(_data.sale_order_valid_days);
      setdayselect(_data.rule === '规则一' ? '0' : '1');
      setwholesale_order_price_type(
        parseInt(_data?.wholesale_order_price_type),
      );
      setBookOrderShowCenterStock(_data?.book_order_show_center_stock);
      setInfo({
        book_order_batch_number: _data?.book_order_batch_number,
        book_order_center_stock: _data?.book_order_center_stock,
        book_order_period: _data?.book_order_period,
      });
      setOverdue(_data?.un_order_day ?? 2);
      setOrgIds(_data?.use_org_item_range_by_org_ids || []);
    }
  };

  // 校验参数
  function validateInput(value: number, label: string) {
    if (!value || value.toString().replace(/\s*/g, '') == '') {
      message.error(`${label}请输入0-99整数`);
      return false;
    }
    if (!Number.isInteger(value - 0) || value < 0 || value > 99) {
      message.error(`${label}请输入0-99整数`);
      return false;
    }
    return true;
  }

  // 保存
  const saveOrder = async () => {
    const isValid =
      validateInput(days, '批发订单有效天数') &&
      validateInput(days2, '批发销售单制单有效天数') &&
      validateInput(overdue, '超期未订天数');
    if (!isValid) return;

    const data = {
      book_order_check_stock: ischecked1,
      book_order_valid_days: Math.floor(days),
      delivery_time: headerItem.value11,
      // "operator_store_id": 0,
      return_order_use_cost_price_in: ischecked2,
      book_order_audit_create_sale_order: ischecked3,
      rule: dayselect == '0' ? '规则一' : '规则二',
      sale_order_valid_days: Math.floor(days2),
      book_order_show_center_stock: bookOrderShowCenterStock,
      not_available_stock_disable_order: is_not_available_stock_disable_order,
      no_display_no_valid_stock_item: no_display_no_valid_stock_item,
      wholesale_order_price_type: wholesale_order_price_type,
      un_order_day: Math.floor(overdue),
      use_org_item_range_by_org_ids: orgIds,
      ...info,
    };
    const res = await addNewBuyparam(data);
    if (res.code == 0) {
      message.success('操作成功');
    }
  };

  const parameterSetting = (name: string, value: boolean) => {
    setInfo({
      ...info,
      [name]: value,
    });
  };

  //时间
  const onDatePicker = (_: any, value: any, name: any) => {
    // console.log(_,value,name)
    setHeaderItem({
      ...headerItem,
      value11: value,
    });
    // console.log(value,'111')
  };
  //规则
  const ruleOnchange = (v: any) => {
    console.log(v, 'ruleOnchange');
    // // console.log(selectDom)
    // if (selectDom.current?.value == '0') {
    //   setdayselect('0')
    // } else {
    //   setdayselect('1')
    // }
    setdayselect(v);
  };

  // 获取二级组织
  const getOrgList = async () => {
    const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.find', {
      data: { level: 2 },
    });
    if (res.code == 0) {
      setOrgList(
        res.data?.map((item: any) => ({ label: item.name, value: item.id })),
      );
    }
  };

  useEffect(() => {
    getbuyParameter();
    getOrgList();
  }, []);
  return (
    <div className="container">
      <div className={'button_box row-flex'}>
        {hasAuth(['批发参数', '编辑']) ? (
          <div style={{ width: '90%' }} className="row-flex">
            <XlbButton
              label="保存"
              loading={isLoading}
              onClick={saveOrder}
              type="primary"
              icon={<XlbIcon size={16} name="baocun" />}
            />
          </div>
        ) : null}
      </div>
      <div className={styles.code}>
        批发订单有效天数
        <input
          onChange={(e: any) => setDays(e.target.value)}
          type="text"
          value={days}
          className={styles.period_input}
        />
        天
      </div>

      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked1}
          onChange={(e: any) => setchecked1(e.target.checked)}
        >
          批发订单校验可用库存
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        配送日时间:&nbsp;
        <TimePicker
          style={{ height: '26px', fontSize: '14px' }}
          allowClear={false}
          format={format}
          value={
            (headerItem.value11, format)
              ? moment(headerItem.value11, format)
              : null
          }
          onChange={(moment, dateString) =>
            onDatePicker(moment, dateString, 'headerItem.value11')
          }
        />
      </div>
      <div className={styles.code}>
        配送日规则:
        {/* <select
          ref={selectDom}
          className={styles.automatic1_select}
          value={dayselect}
          style={{ width: '124px', height: '26px', margin: ' 0 10px 0 5px' }}
          onChange={ruleOnchange}
        >
          <option value={0}>规则一</option>
          <option value={1}>规则二</option>
        </select> */}
        <XlbSelect
          // ref={selectDom}
          onChange={ruleOnchange}
          value={dayselect}
          allowClear={false}
          size="small"
          // defaultValue={dayselect}
          style={{ width: '124px' }}
          // showSearch
          placeholder="请选择"
          options={[
            { label: '规则一', value: '0' },
            { label: '规则二', value: '1' },
          ]}
        />
      </div>
      <div className={styles.code}>
        批发销售单制单有效天数
        <input
          onChange={(e: any) => setDays2(e.target.value)}
          type="text"
          value={days2}
          className={styles.period_input}
        />
        天
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked2}
          onChange={(e: any) => setchecked2(e.target.checked)}
        >
          批发退货门店按成本价入库
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={ischecked3}
          onChange={(e: any) => setchecked3(e.target.checked)}
        >
          批发订单审核自动生成制单状态批发销售单
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={bookOrderShowCenterStock}
          onChange={(e: any) => setBookOrderShowCenterStock(e.target.checked)}
        >
          批发订单显示中心可用库存
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        批发订单显示参考字段:
        <XlbCheckbox
          checked={!!info.book_order_center_stock}
          disabled={!bookOrderShowCenterStock}
          onChange={(e: any) =>
            parameterSetting('book_order_center_stock', e.target.checked)
          }
          style={{ marginLeft: '10px' }}
        >
          中心库存
        </XlbCheckbox>
        <XlbCheckbox
          checked={!!info.book_order_period}
          onChange={(e: any) =>
            parameterSetting('book_order_period', e.target.checked)
          }
          style={{ marginLeft: '10px' }}
        >
          保质期
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        超期未订天数
        <input
          onChange={(e: any) => setOverdue(e.target.value)}
          type="text"
          value={overdue}
          className={styles.period_input}
        />
        天
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={no_display_no_valid_stock_item}
          onChange={(e: any) =>
            setno_display_no_valid_stock_item(e.target.checked)
          }
        >
          批发订单隐藏无可用库存商品
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        <XlbCheckbox
          checked={is_not_available_stock_disable_order}
          onChange={(e: any) =>
            setnot_available_stock_disable_order(e.target.checked)
          }
        >
          APP批发订单无可用库存不允许下单
        </XlbCheckbox>
      </div>
      <div className={styles.code}>
        批发订单生成的批发销售单价格取值
        <XlbTooltip title={'仅使用wms时有效'}>
          <XlbIcon
            color="#979faa"
            style={{ margin: '0 8px' }}
            hoverColor="#3D66FE"
            name="bangzhu"
            size={16}
          />
        </XlbTooltip>
        {/* <select
          ref={selectDom}
          className={styles.automatic1_select}
          value={wholesale_order_price_type}
          style={{ width: '124px', height: '26px', margin: ' 0 10px 0 5px' }}
          onChange={() => {
            setwholesale_order_price_type(selectDom.current?.value)
          }}
        >
          <option value={1}>批发订单价</option>
          <option value={0}>实时批发价</option>
        </select> */}
        <XlbSelect
          // ref={selectDom}
          value={wholesale_order_price_type}
          allowClear={false}
          onChange={(v) => {
            setwholesale_order_price_type(v);
          }}
          size="small"
          style={{ width: '124px' }}
          // showSearch
          placeholder="请选择"
          options={[
            { label: '批发订单价', value: 1 },
            { label: '实时批发价', value: 0 },
          ]}
        />
      </div>

      <div className={styles.code}>
        批发订单/批发销售单/批发售后单/批发退货单商品范围取组织商品档案
        <XlbSelect
          value={orgIds}
          mode="multiple"
          allowClear
          size="small"
          style={{ width: '124px', marginLeft: '8px' }}
          placeholder="请选择"
          options={orgList}
          onChange={setOrgIds}
        />
      </div>
    </div>
  );
};

export default BuyParam;
