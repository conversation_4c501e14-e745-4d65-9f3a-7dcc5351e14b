import { getTree, hasAuth } from '@/utils';
import { SaveOutlined } from '@ant-design/icons';
import {
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbInput,
  XlbMessage,
  XlbPageContainer,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { useEffect, useRef, useState } from 'react';
import { itemColumn } from '../data';
import Api from '../server';

const StockForecastsDetail = ({ rowData, onClose }: any) => {
  const [form] = XlbBasicForm.useForm();
  const pageRef = useRef<XlbPageContainerRef>(null);

  const [loading, setLoading] = useState<boolean>(false);
  const [list, setList] = useState<any>([]);
  const transList = (preList: any, nextList: any) => {
    const l = JSON.parse(JSON.stringify(preList));
    l.forEach((v: any) => {
      const findItem = nextList.find((s: any) => v.id === s.item_category_id);
      if (findItem) {
        v.coefficient = findItem.coefficient;
      }
    });
    return l;
  };
  const getData = async (record: any) => {
    setLoading(true);
    const res = await Api.getQuery({
      id: record.id,
    });
    const classRes = await Api.getClassList();
    if (res?.code === 0 && classRes?.code === 0) {
      setList(res.data);
      pageRef?.current?.setDataSource(
        getTree(transList(classRes.data, res.data)),
      );
    }
    setLoading(false);
  };

  const saveOrder = async () => {
    const findItem = list.find(
      (v: any) =>
        v.coefficient &&
        (parseFloat(v.coefficient) <= 0 || parseFloat(v.coefficient) > 99.99),
    );
    if (findItem) {
      return XlbMessage.error(
        `${findItem.item_category_name}只能输入大于0和小于等于99.99的数值`,
      );
    }
    setLoading(true);
    const res = await Api.saveUpdate({
      store_id: rowData.id,
      details: list,
    });
    setLoading(false);
    if (res.code === 0) {
      XlbMessage.success('保存成功');
      onClose();
    }
  };
  useEffect(() => {
    if (rowData.id > 0) {
      getData(rowData).then();
      form.setFieldValue('store_name', rowData.store_name);
    }
  }, []);

  useEffect(() => {
    if (list.length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-use-before-define
      pageRef?.current?.setColumns(itemColumn.map((item) => tableRender(item)));
    }
  }, [list]);

  const tableRender = (item: any) => {
    switch (item?.code) {
      case 'coefficient':
        item.render = (value: any, record: any) => {
          return (
            <div>
              {record.name === '所有分类' ? null : (
                <XlbInput
                  style={{ width: 100 }}
                  defaultValue={record.coefficient}
                  onInput={(e: any) => {
                    const Reg = /^[0-9.]+$/;
                    if (!Reg.test(e.target.value)) {
                      e.target.value = '';
                    }
                  }}
                  onChange={(e) => {
                    record.coefficient = e.target.value;
                    const findItem = list.find(
                      (v: any) => v.item_category_id === record.id,
                    );
                    if (findItem) {
                      findItem.coefficient = e.target.value;
                    }
                    setList([...list]);
                    // pageRef?.current?.setDataSource(getTree(transList(classList, list)));
                  }}
                />
              )}
            </div>
          );
        };
    }
    return item;
  };

  return (
    <XlbPageContainer
      ref={pageRef}
      isOldBtn={true}
      tableColumn={itemColumn.map((item) => tableRender(item))}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting={false}>
        {() => {
          return (
            <XlbButton.Group>
              {hasAuth(['备货预测系数', '编辑']) ? (
                <XlbButton
                  type={'primary'}
                  label={'保存'}
                  onClick={saveOrder}
                  icon={<SaveOutlined />}
                  loading={loading}
                />
              ) : null}
              <XlbButton
                label={'返回'}
                loading={loading}
                type={'primary'}
                icon={<XlbIcon name={'fanhui'} />}
                onClick={() => onClose()}
              />
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>
      <XlbPageContainer.SearchForm hideToggle={true}>
        <XlbBlueBar title={'基本信息'} hasMargin />
        <XlbForm
          form={form}
          formList={[
            {
              label: '门店名称',
              name: 'store_name',
              type: 'input',
              disabled: true,
              customWidth: 348,
            },
          ]}
          isHideDate
        />
        <XlbBlueBar title={'列表信息'} hasMargin />
      </XlbPageContainer.SearchForm>
      <XlbPageContainer.Table
        keepDataSource={false}
        primaryKey={'id'}
        defaultOpenKeys={[0]}
        treeMode
      />
    </XlbPageContainer>
  );
};

export default StockForecastsDetail;
