import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 72,
    align: 'center',
  },
  {
    name: '配送中心',
    code: 'store_name',
    width: 172,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 312,
    features: { sortable: true },
  },
  {
    name: '单据货主',
    code: 'cargo_source_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '共享货主',
    code: 'share_cargo_source_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '优先级',
    code: 'sort',
    width: 144,
    features: { sortable: true },
  },
];

export const searchFormList: SearchFormType[] = [
  {
    label: '配送中心',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      url: '/erp-mdm/hxl.erp.store.cargoownerdelivery.short.page',
      isMultiple: true,
      primaryKey: 'id',
      // data: {
      //   status: true,
      //   center_flag: true,
      // },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
    width: 200,
  },
  {
    label: '单据货主',
    name: 'cargo_owner_ids',
    type: 'inputDialog',
    dialogParams: {
      dataType: 'lists',
      primaryKey: 'id',
      type: 'cargoOwner',
      isMultiple: true,
      isLeftColumn: false,
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'source_name',
    } as any,
    width: 200,
  },
  {
    label: '共享货主',
    name: 'share_cargo_owner_ids',
    type: 'inputDialog',
    dialogParams: {
      dataType: 'lists',
      primaryKey: 'id',
      type: 'cargoOwner',
      isMultiple: true,
      isLeftColumn: false,
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'source_name',
    } as any,
    width: 200,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    width: 200,
  },
];
