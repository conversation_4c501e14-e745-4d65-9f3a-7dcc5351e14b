import { XlbFetch as ErpRequest } from '@xlb/utils';

// 门店汇总
const deliveryAnalyzePage = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.wholesalereport.wholesaleanalyze.page',
    { data },
  );
};

//  账号管理仓库查询
const getStock = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.storehouse.store.find', { data });
};

// 获取商品标签
const getItemLabelRequest = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.itemlabel.find', { data });
};

export default {
  getStock,
  getItemLabelRequest,
  deliveryAnalyzePage,
};
