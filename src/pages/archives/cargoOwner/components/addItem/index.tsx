import type { BaseModalProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbInputDialog,
  XlbModal,
  XlbRadio,
  XlbTipsModal,
} from '@xlb/components';
import type { FC } from 'react';
import Api from '../../server';

import NiceModal from '@ebay/nice-modal-react';
import { message } from 'antd';

interface Props extends Pick<BaseModalProps, 'title'> {
  fetchData?: any;
}

const AddItem: FC<Props> = ({ fetchData }) => {
  const [form] = XlbBasicForm.useForm();
  const modal = NiceModal.useModal();

  // 新增货主信息
  const handleOk = async (values: any) => {
    console.log(values, 'handleOk');
    const { owner_type, orgs, suppliers } = values;
    if (!owner_type) {
      XlbTipsModal({
        tips: `请选择组织货主供应商`,
        isCancel: true,
        title: '提示',
      });
      return;
    }
    if (owner_type === 'ORGANIZATION' && !orgs?.length) {
      XlbTipsModal({
        tips: `请选择组织`,
        isCancel: true,
        title: '提示',
      });
      return;
    }
    if (owner_type === 'SUPPLIER' && !suppliers?.length) {
      XlbTipsModal({
        tips: `请选择供应商`,
        isCancel: true,
        title: '提示',
      });
      return;
    }
    const items = owner_type === 'ORGANIZATION' ? orgs : suppliers;
    const params = {
      owner_type,
      cargo_owner_details: items?.map((i: any) => {
        return {
          owner_type,
          source_code: i?.code,
          source_name: i?.name,
        };
      }),
    };
    const res = await Api.addInfo(params);
    if (res?.code === 0) {
      form.resetFields();
      modal.hide();
      fetchData();
      message.success('操作成功');
    }
  };
  return (
    <XlbModal
      width={450}
      open={modal.visible}
      title="货主信息选择"
      isCancel={true}
      onOk={async () => {
        form.submit();
      }}
      onCancel={() => {
        form.resetFields();
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div style={{ marginLeft: '12px' }}>
        <XlbBasicForm
          form={form}
          style={{ margin: '20px 0' }}
          onFinish={() => {
            const values = form.getFieldsValue(true);
            handleOk(values);
          }}
        >
          <XlbBasicForm.Item name="owner_type" style={{ marginTop: 15 }}>
            <XlbRadio.Group>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <XlbRadio
                  style={{ marginBottom: '12px', width: '76px' }}
                  value="ORGANIZATION"
                >
                  组织
                </XlbRadio>
                <XlbBasicForm.Item name="org_ids">
                  <XlbInputDialog
                    style={{ width: '180px' }}
                    treeModalConfig={{
                      title: '选择组织',
                      url: '/erp-mdm/hxl.erp.org.find',
                      dataType: 'lists',
                      checkable: true, // 是否多选
                      primaryKey: 'id',
                      afterPost: (data: any) => {
                        return data.map((item: any) => {
                          return {
                            ...item,
                            disabled: item?.level !== 3,
                          };
                        });
                      },
                    }}
                    onChange={(_, arr, form) => {
                      // console.log('onChange', e, f, l);
                      form.setFieldsValue({
                        orgs: arr,
                      });
                    }}
                  />
                </XlbBasicForm.Item>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <XlbRadio
                  style={{ marginBottom: '12px', width: '76px' }}
                  value="SUPPLIER"
                >
                  供应商
                </XlbRadio>
                <XlbBasicForm.Item name="suppliers">
                  <XlbInputDialog
                    width={180}
                    placeholder="请选择供应商"
                    dialogParams={{
                      type: 'supplier',
                      isMultiple: true,
                    }}
                    onChange={(_, arr, form) => {
                      // console.log('onChange', e, f, l);
                      form.setFieldsValue({
                        suppliers: arr,
                      });
                    }}
                  />
                </XlbBasicForm.Item>
              </div>
            </XlbRadio.Group>
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
export default NiceModal.create(AddItem);