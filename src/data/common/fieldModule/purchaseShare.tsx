export const PurchaseShareKeyMap = {
  erpPurchaseShare: 'erpPurchaseShare',
  erpActualStore: 'erpActualStore'
}

export const purchaseShareConfig: any[] = [
  {
    tag: 'ERP',
    label: '采购共享中心',
    id: PurchaseShareKeyMap.erpPurchaseShare,
    name: 'store_ids',
    fieldProps: () => {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            center_flag: true,
            enable_organization: 0
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '实际门店',
    id: PurchaseShareKeyMap.erpActualStore,
    name: 'share_store_ids',
    fieldProps: () => {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            center_flag: true,
            skip_filter: true,
            enable_organization: 0
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  }
]
