
export interface IdReqDTO {
      
    /**
     * @name ID
     */
    id: number;
  
}

export interface KeywordReqDTO {
      
    /**
     * @name 关键字
     */
    keyword?: string;
  
    /**
     * @name 排序
     */
    orders?: OrderParam[];
  
    /**
     * @name 页号
     */
    page_number?: number;
  
    /**
     * @name 每页条数
     */
    page_size?: number;
  
}

export interface OrderParam {

    /**
     * @name 方向
     */
    direction?: string;
  
    /**
     * @name 属性
     */
    property?: string;
  
}

export interface MainBodySaveReqDTO {
      
    /**
     * @name 供货主体名称
     */
    name?: string;
  
}

export interface MainBodyUpdateReqDTO {
      
    /**
     * @name 供货主体ID
     */
    id: number;
  
    /**
     * @name 供货主体名称
     */
    name?: string;
  
}
