import safeMath from '@/utils/safeMath';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { XlbIcon, XlbTable, XlbTipsModal } from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import type { ModalProps } from 'antd';
import { Button, Modal, Progress } from 'antd';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';

export interface XlbProgressModalProps extends ModalProps {
  /**
   * @description: 进度条请求接口
   */
  requestApi: string;
  /**
   * @description: 进度条请求接口参数
   */
  items: [] | any;
  /**
   * @description: 进度条操作的内容
   */
  titleList?: [];
  /**
   * @description: 进度条标题
   * @default '正在复制'
   */
  promptTitle?: string;
  /**
   * @description: 启用批量请求 建议5个请求接口
   * @default '批量请求'
   */
  batchNumber?: 5;
  /**
   * @description: 是否展示错误信息
   * @default false
   */
  showError?: boolean;
  delayTime?: number;
  /**
   * @description: 标题
   */
  title?: string;
  /**
   * 是否显示取消按钮
   */
  showCancelButton?: boolean;
}

const Index: FC<XlbProgressModalProps> = ({
  promptTitle = '正在复制：',
  requestApi,
  items,
  batchNumber,
  titleList,
  delayTime = 0,
  showError = false,
  showCancelButton = false,
  title,
  ...rest
}) => {
  const abortController = useRef(new AbortController());
  const modal = useModal();
  // 接口返回的进度条标题
  const [progressTitle, setProgressTitle] = useState('');
  // 当前进度条的索引
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  // 进度条
  const [percentNum, setProgress] = useState(0);
  // 批次请求次数
  // const totalBatches = Math.ceil(items.length / 10)
  const [currentBatch, setCurrentBatch] = useState<any>([]);
  // 收集错误信息
  const [errInfo, setErrInfo] = useState<any[]>([]);
  // 确认按钮loading
  const [confirmLoading, setConfirmLoading] = useState<boolean>(true);
  /**
   * @description: 根据索引请求接口
   * @param {*} index
   * @return {*}
   */
  const handleRequest = async (index = 0, err_info: any[] = []) => {
    const requestParameters: any = items[index];
    const length = items.length; //需要循环的次数
    const apiCallsCount = index + 1;
    const number =
      (length as number) == 1
        ? 66
        : safeMath.divide(Number(apiCallsCount) || 1, Number(length)) * 100;
    setProgress(number);
    const params = {
      ...(requestParameters as object),
      signal: abortController.current.signal,
    };
    const res: any = await XlbFetch.post(requestApi, params);
    const { code, data } = res;
    if (data?.fid && showError) {
      err_info.push(data);
      setErrInfo(err_info);
    }
    if (!data?.state && showError) {
      let errorArr = data?.errors || data?.error_message || [];
      setErrInfo((prevErrInfo) => [...prevErrInfo, ...errorArr]);
    }
    if (code === 0) {
      setCurrentItemIndex(apiCallsCount);
      const progressTitle = typeof data === 'string' ? data : '';
      setProgressTitle(progressTitle);
      if (apiCallsCount >= length) {
        setCurrentItemIndex(0);
        if (!showError) {
          modal.resolve(res);
          modal.hide();
        } else {
          (length as number) == 1 ? setProgress(100) : null;
          setConfirmLoading(false);
        }
      } else {
        await delayTime;
        handleRequest(apiCallsCount, err_info);
      }
    } else {
      modal.resolve(requestParameters);
      modal.hide();
    }
  };
  /**
   * @description: 数据量大按批次请求
   * @param {*} index
   * @return {*}
   */
  const batchRequest = async (index = 0, storeNames: any) => {
    const length = currentBatch.length;
    const apiCallsCount = index + 1;
    const number =
      length === 1
        ? 66
        : safeMath.divide(Number(apiCallsCount) || 1, Number(length)) * 100;
    setProgress(number);

    const promises = currentBatch[index].map(async (id: number) => {
      const requestParameters: any = items[id];
      const params = {
        data: { ...(requestParameters as object) },
        signal: abortController.current.signal,
      };
      const response = await XlbFetch.post(requestApi, params);
      return response;
    });

    const titleList = currentBatch[index]
      .map((id: number) => storeNames[id])
      .join(',');
    setProgressTitle(titleList);

    try {
      const values = await Promise.all(promises);
      setCurrentItemIndex(apiCallsCount);
      if (apiCallsCount >= length) {
        setCurrentItemIndex(0);
        setCurrentBatch([]);
        modal.resolve(values);
        modal.hide();
      } else {
        await batchRequest(apiCallsCount, storeNames);
      }
    } catch (error) {
      modal.resolve(error);
      modal.hide();
    }
  };

  function ensureArray(value: any) {
    // 判断是否已经是数组
    if (Array.isArray(value)) {
      return value;
    } else {
      // 尝试将其转换为字符串，然后以逗号为分隔符生成数组
      const strValue = String(value);
      const splitValues = strValue.split(',');
      // 过滤掉空字符串项，因为.split(',')会产生空字符串
      return splitValues.filter((item) => item !== '');
    }
  }

  useEffect(() => {
    if (modal.visible) {
      abortController.current = new AbortController();
      if (batchNumber && batchNumber == 5) {
        const array = Array.from({ length: items.length }, (_, index) => index);
        for (let i = 0; i < array.length; i += batchNumber) {
          currentBatch.push(array.slice(i, i + batchNumber));
        }
        const storeNames = ensureArray(items[0]?.store_names);
        setCurrentBatch(currentBatch);
        batchRequest(0, storeNames);
      } else {
        if (showError) {
          setConfirmLoading(true);
        }
        handleRequest();
      }
    }
  }, [modal.visible]);
  const handleCancel = () => {
    XlbTipsModal({
      title: `中止`,
      tips: '中止修改不可恢复，是否继续？',
      isCancel: true,
      getContainer: false,
      zIndex: 2015,
      onOkBeforeFunction: () => {
        abortController.current.abort(); // 取消请求
        modal.hide();
        return true;
      },
    });
  };
  return (
    <Modal
      {...rest}
      title={
        title
          ? `${title}${Number(percentNum) >= 100 ? '完成' : '中'}`
          : undefined
      }
      open={modal.visible}
      centered
      keyboard={false}
      width={showError ? 850 : 450}
      wrapClassName={'xlbDialog'}
      closable={false}
      maskClosable={false}
      zIndex={1000}
      bodyStyle={{ paddingBottom: 0 }}
      footer={
        <>
          {showError ? (
            <Button
              type="primary"
              loading={confirmLoading}
              onClick={() => {
                if (showError) {
                  setErrInfo([]);
                  modal.resolve({ showMessage: !errInfo?.length });
                  modal.hide();
                }
              }}
            >
              确认
            </Button>
          ) : null}
          {showCancelButton && (
            <Button type="primary" onClick={handleCancel}>
              中止
            </Button>
          )}
        </>
      }
    >
      {!showError ? (
        <div style={{ padding: '8px' }} className="col-flex">
          <div>
            <span
              style={{
                color:
                  showError && Number(percentNum) >= 100
                    ? '#00B42B'
                    : '#3D66FE',
              }}
            >
              {showError && Number(percentNum) >= 100 ? '已完成' : promptTitle}
            </span>
            {showError && Number(percentNum) >= 100 ? null : (
              <span
                style={{
                  overflow: 'hidden',
                }}
              >
                {titleList ? titleList[currentItemIndex] : progressTitle}
              </span>
            )}
          </div>
          <Progress
            percent={percentNum}
            status="active"
            showInfo={false}
            size={'default'}
          />
          <span style={{ color: '#000', textAlign: 'center' }}>
            {Number(percentNum).toFixed(2)} %
          </span>
        </div>
      ) : (
        <div style={{ padding: '8px' }} className="col-flex">
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              flexDirection: 'row-reverse',
              alignItems: 'flex-end',
            }}
          >
            <div>
              {titleList
                ? titleList[currentItemIndex]
                  ? titleList[currentItemIndex]
                  : titleList[currentItemIndex - 1]
                : progressTitle}
            </div>
            {Number(percentNum) >= 100 ? (
              <XlbIcon style={{ color: '#215cdc' }} name="tongguo-baibian" />
            ) : (
              <div style={{ color: '#86909C', textAlign: 'center', width: 70 }}>
                {Number(percentNum).toFixed(2)} %
              </div>
            )}
            <Progress
              percent={percentNum}
              status="active"
              showInfo={false}
              size={'default'}
            />
          </div>

          <div
            style={{ maxHeight: 350, display: 'flex', flexDirection: 'column' }}
          >
            <div style={{ fontWeight: 500, fontSize: 16, paddingBottom: 10 }}>
              失败列表({errInfo.length})
            </div>
            <XlbTable
              style={{ flex: 1 }}
              dataSource={errInfo}
              columns={[
                { name: '单号', width: 170, code: 'fid' },
                {
                  name: '原因',
                  width: 600,
                  code: 'error_message',
                  render: (text) => <span>{text}</span>,
                },
              ]}
              total={errInfo.length}
            />
          </div>
        </div>
      )}
    </Modal>
  );
};
export default NiceModal.create(Index);
