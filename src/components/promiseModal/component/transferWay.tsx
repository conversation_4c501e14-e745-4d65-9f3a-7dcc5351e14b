import { XlbBasicForm, XlbForm, XlbTable } from '@xlb/components';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { basketorder } from '../server';

const TransferWay = (props) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [tableLoading, setTableLoading] = useState<any>(false);
  const [flag, setFlag] = useState<any>(null);

  const openRefOrder = _.debounce(async (fid) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = await basketorder({ fid: fid });
    if (res.code == 0) {
      formModel.setFieldsValue(res?.data);
      setFidDataList(res?.data?.details);
      setFlag(res?.data?.flag);
    }
    setTableLoading(false);
  }, 50);
  useEffect(() => {
    openRefOrder(fid);
  }, []);
  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={[
          {
            type: 'input',
            disabled: true,
            name: 'out_store_name',
            label: '发货门店',
            width: 180,
          },
          {
            type: 'input',
            disabled: true,
            name: 'store_name',
            label: '收货门店',
            width: 180,
          },
          {
            type: 'select',
            disabled: true,
            name: 'flag',
            label: '进出方向',
            width: 180,
            options: [
              {
                label: '发货',
                value: false as any,
              },
              {
                label: '退货',
                value: true as any,
              },
            ],
          },
          {
            type: 'input',
            disabled: true,
            name: 'fid',
            width: 180,
            label: '单据号',
          },
          {
            type: 'select',
            disabled: true,
            name: 'state',
            width: 180,
            label: '单据状态',
            options: [
              {
                label: '制单',
                value: 'INIT',
              },
              {
                label: '审核',
                value: 'AUDIT',
              },
              {
                label: '处理通过',
                value: 'HANDLE',
              },
              {
                label: '批复通过',
                value: 'APPROVE',
              },
            ],
          },
          {
            type: 'input',
            disabled: true,
            name: 'memo',
            label: '留言备注',
            width: 480,
          },
        ]}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 'calc(100vh - 490px)', maxHeight: 400, overflowY: 'scroll' }}
        // hideOnSinglePage={true}
        columns={[
          {
            code: '_index',
            name: '序号',
          },
          {
            name: '载具名称',
            code: 'basket_name',
            width: 140,
            features: { sortable: true },
          },
          {
            name: '单价',
            code: 'price',
            width: 110,
            align: 'right',
            features: { sortable: true, format: 'PRICE' },
          },
          // @ts-ignore
          ...(flag
            ? [
                {
                  name: '门店确认数量',
                  code: 'quantity',
                  width: 120,
                  align: 'right',
                  features: { sortable: true },
                },
                {
                  name: '司机取件数量',
                  code: 'driver_confirm_quantity',
                  width: 120,
                  align: 'right',
                  features: { sortable: true },
                },
                {
                  name: '实收数量',
                  code: 'out_quantity',
                  width: 110,
                  align: 'right',
                  features: { sortable: true },
                  render(text: any, record: any, index: any) {
                    if (formModel.getFieldValue('state') === 'HANDLE') {
                      return <div>-</div>;
                    }
                    return <div>{text}</div>;
                  },
                },
                {
                  name: '仓库确认金额',
                  code: 'out_money',
                  width: 120,
                  align: 'right',
                  features: { sortable: true, format: 'MONEY' },
                  render(text: any, record: any, index: any) {
                    if (formModel.getFieldValue('state') === 'HANDLE') {
                      return <div>-</div>;
                    }
                    return <div>{text}</div>;
                  },
                },
                {
                  name: '门店确认金额',
                  code: 'money',
                  width: 120,
                  align: 'right',
                  features: { sortable: true, format: 'MONEY' },
                },
              ]
            : [
                {
                  name: '司机取件数量',
                  code: 'driver_confirm_quantity',
                  width: 120,
                  align: 'right',
                  features: { sortable: true },
                },
                {
                  name: '仓库确认数量',
                  code: 'quantity',
                  width: 120,
                  align: 'right',
                  features: { sortable: true },
                },
                {
                  name: '留店数量',
                  code: 'out_quantity',
                  width: 110,
                  align: 'right',
                  features: { sortable: true },
                  render(text: any, record: any, index: any) {
                    if (formModel.getFieldValue('state') === 'HANDLE') {
                      return <div>-</div>;
                    }
                    return <div>{text}</div>;
                  },
                },
                {
                  name: '仓库确认金额',
                  code: 'money',
                  width: 120,
                  align: 'right',
                  features: { sortable: true, format: 'MONEY' },
                },
                {
                  name: '门店确认金额',
                  code: 'out_money',
                  width: 120,
                  align: 'right',
                  features: { sortable: true, format: 'MONEY' },
                  render(text: any, record: any, index: any) {
                    if (formModel.getFieldValue('state') === 'HANDLE') {
                      return <div>-</div>;
                    }
                    return <div>{text}</div>;
                  },
                },
              ]),
        ]}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default TransferWay;
