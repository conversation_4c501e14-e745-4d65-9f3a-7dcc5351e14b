import { XlbFetch as ErpRequest } from '@xlb/utils';

export default {
  //新增区域
  addInfo: async (data: any) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.cargo.owner.save', data);
  },
  // 批量删除
  batchDelete: async (data: any) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.cargo.owner.batchdelete', data);
  },
  // 批量停用
  batchDisabled: async (data: any) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.cargo.owner.disabled', data);
  },
  // 批量启用
  batchEnabled: async (data: any) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.cargo.owner.enabled', data);
  },
};
