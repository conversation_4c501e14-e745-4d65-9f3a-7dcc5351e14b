import { hasAuth } from '@/utils/kit';
import { useIRouter, wujieBus } from '@/wujie/utils';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbPageContainer,
  XlbTableColumnProps,
} from '@xlb/components';
import dayjs from 'dayjs';
import { FC, useEffect, useRef, useState } from 'react';
import { FORM_LIST, tableColumn } from './data';
import { exportGoods } from './server';
import { useStore } from './store';

const Index: FC = () => {
  // form
  const [form] = XlbBasicForm.useForm();

  // table format
  const prevPost = () => ({
    ...form.getFieldsValue(true),
  });

  // init
  useEffect(() => {
    form.setFieldsValue({
      create_date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    });
  }, []);

  // operate
  const [exportLoading, setExportLoading] = useState(false);
  const exportItem = async (e: any, requestForm: any) => {
    setExportLoading(true);
    const res = await exportGoods(requestForm);
    setExportLoading(false);
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };
  const { setRecord, isFresh, setIsFresh } = useStore();
  const formatTableCoulmn = (columns: XlbTableColumnProps<any>[]) => {
    return columns.map((item) => {
      if (item.code === 'fid') {
        return {
          ...item,
          render: (value: any, record: any) => (
            <span
              className="link"
              onClick={(e) => {
                e.stopPropagation();
                setRecord(record);
                const { navigate } = useIRouter();
                navigate('/xlb_erp/mustSellGoodsManagement/item', record);
              }}
            >
              {value}
            </span>
          ),
        };
      }
      return item;
    });
  };

  const fetchDataRef = useRef<any>();
  useEffect(() => {
    if (isFresh) {
      fetchDataRef.current?.();
      setIsFresh(false);
    }
  }, [isFresh]);

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.mustsellitemassortment.page'}
      tableColumn={formatTableCoulmn(tableColumn)}
      immediatePost
      prevPost={prevPost}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource,
          requestForm,
        }: ContextState<any>) => {
          fetchDataRef.current = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['必卖品货盘管理', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['必卖品货盘管理', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  disabled={!dataSource?.length}
                  loading={exportLoading}
                  icon={<XlbIcon name="daochu" />}
                  onClick={(e) => exportItem(e, requestForm)}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm form={form} formList={FORM_LIST} isHideDate />
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table key="fid" primaryKey="fid" />
    </XlbPageContainer>
  );
};
export default Index;
