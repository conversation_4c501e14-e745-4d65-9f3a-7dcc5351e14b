export const bussinessRefundKeyMap = {
  bussinessrefundtype: 'bussinessrefundtype',
  erpBussinessSupplierIds: 'erpBussinessSupplierIds',
  erpBussinessShareStatus: 'erpBussinessShareStatus',
  erpBussinessRefundStatus: 'erpBussinessRefundStatus'
}

export const bussinessRefundConfig: any[] = [
  {
    tag: 'ERP',
    label: '返款类型',
    id: bussinessRefundKeyMap.bussinessrefundtype,
    name: 'refund_type',
    componentType: 'select',
    fieldProps: {
      options: [
        {
          label: '代销分账返款',
          value: 'AGENCY'
        }
      ]
    }
  },
  {
    tag: 'ERP',
    label: '供应商',
    id: bussinessRefundKeyMap.erpBussinessSupplierIds,
    name: 'supplier_ids',
    fieldProps: (form: any) => {
      return {
        dialogParams: {
          type: 'supplier',
          dataType: 'lists',
          isLeftColumn: true,
          isMultiple: true,
          primaryKey: 'id',
          data: {
            enabled: true,
            collaboration_mode: form?.getFieldValue('refund_type') == 'AGENCY' ? 'AGENCY' : null
          }
        }
      }
    },
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      return formValues
    },
    handleDefaultValue: (data: any) => {
      if (data.refund_type?.length > 0) {
        return null
      }
    },
    dependencies: ['summary_types', 'refund_type'],
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '分账状态',
    id: bussinessRefundKeyMap.erpBussinessShareStatus,
    name: 'share_state',
    componentType: 'select',
    dependencies: ['summary_types'],
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      return [
        ...(formValues?.summary_types?.includes('ITEM')
          ? [
              { label: '已分账', value: 'SUCCESS', color: '#00B42B' },
              { label: '部分分账', value: 'PARTIAL', color: '#FF7D01' },
              { label: '未分账', value: 'INIT', color: '#1D2129' },
              { label: '分账失败', value: 'FAIL', color: '#FF0000' },
              { label: '分账中', value: 'DOING', color: '#FF7D01' }
            ]
          : [
              { label: '已分账', value: 'SUCCESS', color: '#00B42B' },
              { label: '部分分账', value: 'PARTIAL', color: '#FF7D01' },
              { label: '未分账', value: 'INIT', color: '#1D2129' }
            ])
      ]
    }
  },
  {
    tag: 'ERP',
    label: '返款状态',
    id: bussinessRefundKeyMap.erpBussinessRefundStatus,
    name: 'refund_state',
    componentType: 'select',
    dependencies: ['summary_types'],
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      return [
        ...(formValues?.summary_types?.includes('ITEM')
          ? [
              { label: '已返款', value: 'SUCCESS', color: '#00B42B' },
              { label: '部分返款', value: 'PARTIAL', color: '#FF7D01' },
              { label: '未返款', value: 'INIT', color: '#1D2129' },
              { label: '返款失败', value: 'FAIL', color: '#FF0000' },
              { label: '返款中', value: 'DOING', color: '#FF7D01' }
            ]
          : [
              { label: '已返款', value: 'SUCCESS', color: '#00B42B' },
              { label: '部分返款', value: 'PARTIAL', color: '#FF7D01' },
              { label: '未返款', value: 'INIT', color: '#1D2129' }
            ])
      ]
    }
  }
]
