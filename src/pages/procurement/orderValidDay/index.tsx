import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import { LStorage } from '@/utils/storage';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbInputNumber,
  XlbMessage,
  XlbPageContainer,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useEffect, useState } from 'react';
import { formList, tableList } from './data';
import { addNewPost, deletePost } from './server';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const OrderEffectiveDays = () => {
  const [form] = XlbBasicForm.useForm<any>();
  const [orgList, setOrgList] = useState<any[]>([]);
  const enable_organization = Boolean(
    useBaseParams((state) => state).enable_organization,
  );
  const userInfo = LStorage.get('userInfo');

  let refresh = () => {};

  const prevPost = () => {
    const formData = form.getFieldsValue(true);
    return {
      ...formData,
    };
  };

  const deleteRow = (selectRowKeys: string[], fetchData: Function) => {
    XlbTipsModal({
      title: `删除`,
      tips: '是否删除该配置？',
      isCancel: true,
      getContainer: false,
      zIndex: 2015,
      onOkBeforeFunction: async () => {
        const res = await deletePost({ id: selectRowKeys[0] });
        if (res.code == 0) {
          XlbMessage.success('删除成功');
          fetchData();
          return true;
        } else {
          return false;
        }
      },
    });
  };

  const addNew = (fetchData: Function) => {
    XlbTipsModal({
      title: `新增`,
      tips: (
        <XlbBasicForm
          autoComplete="off"
          colon={true}
          form={form}
          initialValues={{ valiDay: 1 }}
        >
          <XlbBasicForm.Item
            label="组织"
            name="org_id"
            rules={[{ required: true, message: '组织不能为空！' }]}
          >
            <XlbSelect
              style={{ width: 260 }}
              options={orgList}
              allowClear
              placeholder="请选择"
            />
          </XlbBasicForm.Item>

          <XlbBasicForm.Item
            label="采购订单有效天数"
            name="valid_day"
            rules={[{ required: true, message: '采购订单有效天数不能为空！' }]}
          >
            <XlbInputNumber
              style={{ width: 260 }}
              precision={0}
              step={1}
              max={100}
              min={1}
              placeholder="请输入采购订单有效天数"
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      ),
      isCancel: true,
      getContainer: false,
      zIndex: 2015,
      onOkBeforeFunction: async () => {
        try {
          await form.validateFields();
        } catch (err: any) {
          return false;
        }
        const data = form.getFieldsValue(true);
        const orgName = orgList.find(
          (e) => e.value == form.getFieldValue('org_id'),
        ).label;
        data.org_name = orgName;
        data.update_by = userInfo.userName;
        const res = await addNewPost(data);
        if (res.code == 0) {
          XlbMessage.success('新增成功');
          fetchData();
          return true;
        }
        return false;
      },
    });
  };

  const getOrgList = async () => {
    const data = { level: 2 };
    const res = await XlbFetch.post('/erp-mdm/hxl.erp.org.find', data);
    if (res.code == 0) {
      const org_list = res.data.map((i: any) => ({
        value: i.id,
        label: i.name,
      }));
      setOrgList(org_list);
    }
  };

  useEffect(() => {
    getOrgList();
  }, []);

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.purchaseordervalidday.page'}
      tableColumn={tableList}
      prevPost={prevPost}
      immediatePost={false}
    >
      <ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource = [],
          selectRowKeys = [],
          requestForm,
        }) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['订单有效天数', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}

              {hasAuth(['订单有效天数', '编辑']) && (
                <XlbButton
                  label="新增"
                  type="primary"
                  onClick={() => {
                    addNew(fetchData);
                  }}
                  icon={<XlbIcon name="jia" />}
                />
              )}

              {hasAuth(['订单有效天数', '删除']) && (
                <XlbButton
                  label="删除"
                  type="primary"
                  disabled={!selectRowKeys.length}
                  onClick={() => {
                    deleteRow(selectRowKeys, fetchData);
                  }}
                  icon={<XlbIcon name="shanchu" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <SearchForm>
        <XlbForm
          formList={formList(enable_organization)}
          form={form}
          isHideDate={true}
        />
      </SearchForm>
      <Table primaryKey="id" selectMode="single" />
    </XlbPageContainer>
  );
};
export default OrderEffectiveDays;
