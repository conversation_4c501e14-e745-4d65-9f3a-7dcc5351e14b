import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
// import { useKeepAliveRefresh } from '@/hooks';
import useDownload from '@/hooks/useDownload';
import { hasAuth } from '@/utils';
import { EditOutlined, MergeCellsOutlined } from '@ant-design/icons';
import {
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbMessage,
  XlbModal,
  XlbProPageContainer,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbRadio,
  XlbTable,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { useNavigation } from '@xlb/max';
import { XlbFetch as ErpRequest, XlbFetch } from '@xlb/utils';
import { FormInstance, message, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { FC, useRef, useState } from 'react';
import BatchOrderIndex from './components/batchOrder/batchOrder';
import DeliveryOrder from './components/deliveryOrder/deliveryOrder';
import { Columns, goodDataColumns, itemLogisticsMode } from './data';
import Item from './item/index';
import { lockDetail } from './item/server';
import { batchaudit, invalid, mergeInfo, replenishment, storeorderCopy } from './server';
import StoreItem from './storeItem/index';
import { wujieBus } from '@/wujie/utils';

const Index: FC = () => {
  const { navigate } = useNavigation();
  const fetchDataRef = useRef<any | null>(null);
  const dataSourceRef = useRef<any | null>(null);
  const [record, setRecord] = useState<any>({});
  const [form] = XlbBasicForm.useForm();
  const { downByProgress } = useDownload();
  // 多单合并弹窗
  const [visibleMerge, setVisibleMerge] = useState(false);
  const [goodDataListMerge, setGoodDataListMerge] = useState<any[]>([]);
  // 下游单据弹窗
  const [visibleOrderFid, setVisibleOrderFid] = useState(false);
  const [fidDataList, setFidDataList] = useState<any[]>([]);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  // 其他弹窗
  const [batchOrderVisible, setBatchOrderVisible] = useState<boolean>(false);
  const pageModalItemRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);
  const deliveryTypeRef = useRef<any>(null);
  const [deliveryOrderVisible, setDeliveryOrderVisible] =
    useState<boolean>(false); //仓配单modal
  // 复制弹窗
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [visibleCopy, setVisibleCopy] = useState(false);
  const [goodDataListCopy, setGoodDataListCopy] = useState<any[]>([]);
  const deliveryState = [
    {
      label: '待收单',
      value: 'INIT',
      type: 'info',
    },
    {
      label: '已收单',
      value: 'AUDIT',
      type: 'danger',
    },
    {
      label: '配货中',
      value: 'PICKING',
      type: 'warning',
    },
    {
      label: '已配货',
      value: 'PICKED',
      type: 'danger',
    },
    {
      label: '已装车',
      value: 'LOADED',
      type: 'danger',
    },
    {
      label: '已发车',
      value: 'DELIVERY',
      type: 'danger',
    },
    {
      label: '已完成',
      value: 'FINISH',
      type: 'success',
    },
  ];
  const fidState = [
    { label: '制单', value: 'INIT', type: 'info' },
    { label: '待支付', value: 'PAID', type: 'info' },
    { label: '审核', value: 'AUDIT', type: 'warning' },
    { label: '作废', value: 'INVALID', type: 'danger' },
  ];
  // 导出明细
  const exportDetailItem = async (e: any, selectRow: any, setLoading: any) => {
    setLoading(true);
    const data = {
      fids: selectRow?.map((i: { fid: any }) => i.fid),
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.storeorder.detail.batchexport',
      data,
    );
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success(res?.data);
    }
    setLoading(false);
  };
  const exportItem = async (
    type: number = 1,
    e: any = null,
    requestForm: any,
    setLoading: any,
  ) => {
    if (type === 1) {
      setLoading(true);
      const data = {
        ...requestForm,
      };
      const res = await ErpRequest.post('/erp/hxl.erp.storeorder.export', data);
      if (res.code === 0) {
        wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
        XlbMessage.success(res?.data);
      }
      setLoading(false);
    } else if (type === 2) {
      const data = {
        ...requestForm,
      };
      const res = await ErpRequest.post(
        '/erp/hxl.erp.storereforder.export',
        data,
      );
      if (res.code === 0) {
        await downByProgress(e);
        XlbMessage.success(res?.data);
      }
      setLoading(false);
    }
  };

  const requestBatchaudit = async (selectRow: any, setLoading: any) => {
    XlbTipsModal({
      tips: `是否确认审核${selectRow.length}条数据?`,
      isCancel: true,
      onOk: async () => {
        setLoading(true);
        const res = await batchaudit({
          fids: selectRow.map((i: { fid: any }) => i.fid),
        });
        if (res.code === 0) {
          if (res.code === 0) {
            if (!res?.data?.fail_msg?.length) {
              message.success('审核成功');
            }
            if (res?.data?.fail_msg?.length > 0) {
              await XlbTipsModal({
                tips: (
                  <>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: 5,
                      }}
                    >
                      {res?.data?.fail_msg?.map((v) => {
                        return (
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              flexDirection: 'column',
                              alignItems: 'center',
                            }}
                          >
                            <span>{v}</span>
                          </div>
                        );
                      })}
                    </div>
                  </>
                ),
              });
            }
            fetchDataRef.current?.(1);
          }
        }
        setLoading(false);
      },
    });
  };

  const requestInvalid = async (obj: any, update: any, setLoading: any) => {
    await XlbTipsModal({
      tips: `是否确认作废${obj.fid}数据?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        setLoading(true);
        const res = await invalid({ fid: obj.fid });
        if (res.code === 0) {
          XlbMessage.success('作废成功');
          update();
        }
        setLoading(false);
        return true;
      },
    });
  };

  // 复制
  const copySelectRow = useRef<any | null>(null);
  const copySetLoading = useRef<any | null>(null);
  const copyItem1 = async (chooseList: any, setLoading: any) => {
    await XlbTipsModal({
      tips: `是否确认复制${chooseList[0].fid}?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        setLoading(true);
        const res = await storeorderCopy({
          fid: chooseList[0].fid,
          check: true,
        });
        if (res.code === 0) {
          if (res.data?.details && res.data?.details?.length) {
            copySelectRow.current = chooseList[0];
            copySetLoading.current = setLoading;
            setGoodDataListCopy(res.data?.details);
            setVisibleCopy(true);
          } else {
            XlbMessage.success('复制成功');
            fetchDataRef.current?.(1);
            setLoading(false);
          }
        } else {
          setLoading(false);
        }
        return true;
      },
    });
  };
  // 继续复制
  const copyItem2 = _.debounce(async (type = true) => {
    if (type) {
      setIsLoading(true);
      const res = await storeorderCopy({
        fid: copySelectRow.current?.fid,
        check: false,
      });
      if (res?.code === 0) {
        XlbMessage.success('复制成功');
        fetchDataRef.current?.(1);
      }
      setIsLoading(false);
    }
    copySetLoading.current(false);
    setVisibleCopy(false);
  }, 1000);

  const goDetail = async (record, index) => {
    if (record.state === 'INIT') {
      const res = await lockDetail({
        fid: record.fid,
      });
      if (res.code === 0) {
        deliveryTypeRef.current = 2;
        form?.setFieldsValue({
          delivery_type: 2,
        });
        setRecord({ fid: record.fid });
        pageModalItemRef.current?.setOpen(true);
      }
    } else {
      deliveryTypeRef.current = 2;
      form?.setFieldsValue({
        delivery_type: 2,
      });
      setRecord({ fid: record.fid });
      pageModalItemRef.current?.setOpen(true);
    }
  };

  // 合并
  const mergeSelectRow = useRef<any | null>(null);
  const mergeSetLoading = useRef<any | null>(null);
  const mergeItem = async (selectRow: any, setLoading: any) => {
    if (selectRow.length <= 1) {
      XlbTipsModal({
        tips: '请选择至少2张单据进行多单合并！',
      });
      return;
    } else {
      await XlbTipsModal({
        tips: `已选择${selectRow.length}张单据，是否确认合并!`,
        onOk: async () => {
          setLoading(true);
          const res = await mergeInfo({
            fids: selectRow?.map((i: { fid: any }) => i.fid),
            check: true, // 校验合并，无误则合并
          });
          if (res.code === 0) {
            if (res.data?.details && res.data?.details?.length) {
              mergeSelectRow.current = selectRow;
              mergeSetLoading.current = setLoading;
              setGoodDataListMerge(res.data?.details);
              setVisibleMerge(true);
            } else {
              XlbMessage.success(`已合并${selectRow.length}张单据!`);
              fetchDataRef.current?.(1);
              setLoading(false);
            }
          } else {
            setLoading(false);
          }
          return true;
        },
      });
    }
  };
  // 继续合并
  const mergeItem2 = async (type = true) => {
    if (type) {
      const res = await mergeInfo({
        fids: mergeSelectRow.current?.map((i: { fid: any }) => i.fid),
        check: false,
      });
      if (res?.code === 0) {
        XlbMessage.success(`已合并${mergeSelectRow.current?.length}张单据!`);
        fetchDataRef.current?.(1);
      }
    }
    mergeSetLoading.current(false);
    setVisibleMerge(false);
  };

  // 生成缺货补货单
  const generateOutOrder = async (selectRow: any, setLoading: any) => {
    // if (selectRow.length > 1) {
      const storeIds = new Set(selectRow?.map((item: any) => item.store_id));
      if (storeIds?.size > 1) {
        XlbMessage.info(`收货门店不一致，无法生成`)
        return false;
      }
      let fl = selectRow?.filter((t: any) => t.type !== 'STOREHOUSE_DELIVERY')
      if (fl?.length) {
        XlbMessage.info(<div style={{ maxWidth: 1000, overflow: 'hidden', wordWrap: 'break-word' }}>
          单据[{fl?.map((t: any) => t.fid)?.join(',')}]为直供单，不支持生成缺货补货单
        </div>);
        return false;
      }
      fl = selectRow?.filter((t: any) => t.state !== 'AUDIT');
      if (fl?.length) {
        XlbMessage.info(<div style={{ maxWidth: 1000, overflow: 'hidden', wordWrap: 'break-word' }}>
          单据[{fl?.map((t: any) => t.fid)?.join(',')}]状态不为审核状态，无法生成
        </div>)
        return false;
      }
      setLoading(true);
      const res = await replenishment({
        fids: selectRow?.map((i: { fid: any }) => i.fid),
      });
      if (res.code === 0) {
        fetchDataRef.current?.(1);
      }
      setLoading(false);
  };
  const openRefOrder = _.debounce(async (data) => {
    setVisibleOrderFid(true);
    setTableLoading(true);
    setFidDataList([]);
    const fids = data?.ref_order_fid.split(',');
    const res = await XlbFetch.post(
      process.env.ERP_URL + '/erp/hxl.erp.requestorder.page',
      {
        fids: fids,
      },
    );
    if (res.code == 0) {
      setFidDataList(res?.data?.content);
    }
    setTableLoading(false);
  }, 50);
  return (
    <>
      <BatchOrderIndex
        open={batchOrderVisible}
        setOpen={setBatchOrderVisible}
      />
      <DeliveryOrder
        open={deliveryOrderVisible}
        setOpen={setDeliveryOrderVisible}
      />
      <XlbModal
        width={800}
        open={visibleCopy}
        title={'复制单据后，下列商品会自动移除或调整数量，是否继续复制？'}
        isCancel={true}
        keyboard={false}
        onOk={async () => {
          if (isLoading) return;
          await copyItem2();
        }}
        onCancel={async () => {
          await copyItem2(false);
        }}
      >
        <XlbTable
          style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
          columns={goodDataColumns}
          total={goodDataListCopy?.length}
          dataSource={goodDataListCopy}
          key={goodDataListCopy?.length}
        ></XlbTable>
      </XlbModal>
      <XlbModal
        width={800}
        open={visibleOrderFid}
        title={'下游单据'}
        isCancel={true}
        keyboard={false}
        onOk={async () => {
          setVisibleOrderFid(false);
        }}
        onCancel={() => {
          setVisibleOrderFid(false);
        }}
      >
        <XlbTable
          isLoading={tableLoading}
          style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
          columns={[
            {
              name: '序号',
              code: '_index',
              width: 80,
            },
            {
              name: '下游单据号',
              code: 'fid',
              width: 150,
              render: (text: any, record: any, index: any) => {
                return (
                  <span
                    className="link cursors"
                    onClick={(e) => {
                      setVisibleOrderFid(false);
                      e.stopPropagation();
                      navigate(
                        '/xlb_erp/storeSupplyOrder/item',
                        {
                          ...record,
                        },
                        'xlb_erp',
                        true,
                      );
                    }}
                  >
                    {text}
                  </span>
                );
              },
            },
            {
              name: '统配单号',
              code: 'relation_force_delivery_fid',
              width: 150,
              render: (text: any, record: any, index: any) => {
                return text ? (
                  <span
                    className="link cursors"
                    onClick={(e) => {
                      setVisibleOrderFid(false);
                      e.stopPropagation();
                      navigate(
                        '/xlb_erp/storeSupplyOrder/item',
                        {
                          ...record,
                        },
                        'xlb_erp',
                        true,
                      );
                    }}
                  >
                    {text}
                  </span>
                ) : (
                  '-'
                );
              },
            },
            {
              name: '配送日期',
              code: 'delivery_date',
              width: 150,
              features: { format: 'TIME' },
            },
            {
              name: '单据状态',
              code: 'state',
              render: (text: any) => {
                const item = fidState.find((v) => v.value === text);
                return <div>{item ? item.label : ''}</div>;
              },
            },
            {
              name: '配送状态',
              code: 'delivery_state',
              render: (text: any, record: any, index: any) => {
                const item = deliveryState.find((v) => v.value === text);
                return <div>{item ? item.label : ''}</div>;
              },
            },
            {
              name: '发货门店',
              code: 'out_store_name',
              width: 150,
            },
            {
              name: '发货仓库',
              code: 'storehouse_name',
              width: 150,
            },
            {
              name: '体积（cm³）',
              code: 'volume',
              width: 100,
              render: (text: any, record: any, index: any) => {
                return <div>{text * 1000000}</div>;
              },
            },
            {
              name: '商品物流模式',
              code: 'item_logistics_mode',
              width: 110,
              features: { sortable: false },
              align: 'left',
              render: (text: any, record: any, index: any) => {
                return (
                  <div className={`overwidth`}>
                    {text
                      ? itemLogisticsMode?.find((v) => v.value == text)?.label
                      : ''}{' '}
                  </div>
                );
              },
            },
            {
              name: '同步WMS',
              code: 'has_wms_fid',
              width: 100,
              render: (text: any, record: any, index: any) => {
                const showColor = text ? 'success' : 'danger';
                return (
                  <div className={`overwidth ${showColor}`}>
                    {' '}
                    {text ? '是' : '否'}{' '}
                  </div>
                );
              },
            },
            {
              name: '生成调出单',
              code: 'has_out_order',
              width: 110,
              features: { sortable: false },
              align: 'left',
              render: (text: any, record: any, index: any) => {
                const showColor = text == '是' ? 'success' : 'danger';
                return (
                  <div className={`overwidth ${showColor}`}>
                    {' '}
                    {text == '是' ? '是' : '否'}{' '}
                  </div>
                );
              },
            },
            {
              name: '生成缺货补货单',
              code: 'replenish_fid',
              width: 134,
              features: { sortable: true },
              align: 'left',
              render: (text: any, record: any, index: any) => {
                const showColor = text ? 'success' : 'danger';
                return (
                  <div className={`overwidth ${showColor}`}>
                    {' '}
                    {text ? '是' : '否'}{' '}
                  </div>
                );
              },
            },
            {
              name: '是否生成直配单',
              code: 'has_direct_delivery_order',
              width: 130,
              features: { sortable: false },
              align: 'left',
              render: (text: any, record: any, index: any) => {
                const showColor = text == '是' ? 'success' : 'danger';
                return (
                  <div className={`overwidth ${showColor}`}>
                    {' '}
                    {text == '是' ? '是' : '否'}{' '}
                  </div>
                );
              },
            },
          ]}
          total={fidDataList?.length}
          dataSource={fidDataList}
          key={fidDataList?.length}
        ></XlbTable>
      </XlbModal>
      <XlbModal
        width={800}
        open={visibleMerge}
        title={'合并单据后,下列商品会自动移除或调整数量,是否继续合并?'}
        isCancel={true}
        keyboard={false}
        onOk={async () => {
          await mergeItem2();
        }}
        onCancel={async () => {
          await mergeItem2(false);
        }}
      >
        <XlbTable
          style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
          columns={goodDataColumns}
          total={goodDataListMerge?.length}
          dataSource={goodDataListMerge}
          key={goodDataListMerge?.length}
        ></XlbTable>
      </XlbModal>
      <XlbProPageModal
        ref={pageModalItemRef}
        Content={({ onClose }) => {
          return (
            <>
              {deliveryTypeRef?.current == 2 ? (
                // 仓配详情
                <StoreItem
                  onBack={(back: boolean) => {
                    if (back) {
                      pageConatainerRef?.current?.fetchData?.();
                    }
                    pageModalItemRef.current?.setOpen(false);
                  }}
                  record={record}
                ></StoreItem>
              ) : (
                // 直供详情
                <Item
                  onBack={(back: boolean) => {
                    if (back) {
                      pageConatainerRef?.current?.fetchData?.();
                    }
                    pageModalItemRef.current?.setOpen(false);
                  }}
                  record={record}
                  setRecord={setRecord}
                ></Item>
              )}
            </>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer
        timeTypeValueEnum={{
          0: 'create_date',
          1: 'audit_date',
          2: 'delivery_date',
          3: 'invalid_date',
          4: 'valid_date',
        }}
        searchFieldProps={{
          initialValues: {
            date: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
            time_type: 0,
            create_date: [
              dayjs().format('YYYY-MM-DD'),
              dayjs().format('YYYY-MM-DD'),
            ],
          },
          formList: [
            { id: 'dateCommon', label: '日期范围', format: 'YYYY-MM-DD' },
            {
              id: 'timeType',
              label: '时间类型',
              name: 'time_type',
              fieldProps: {
                allowClear: false,
                options: [
                  { label: '制单时间', value: 0 },
                  { label: '审核时间', value: 1 },
                  { label: '配送时间', value: 2 },
                  { label: '作废时间', value: 3 },
                  { label: '有效时间', value: 4 },
                ],
              },
            },
            {
              id: ErpFieldKeyMap?.erpBillState,
              label: '单据状态',
              name: 'state',
              fieldProps: {
                options: [
                  { label: '制单', value: 'INIT', type: 'info' },
                  { label: '审核', value: 'AUDIT', type: 'warning' },
                  { label: '作废', value: 'INVALID', type: 'error' },
                ],
              },
            },
            {
              id: ErpFieldKeyMap?.erpBillState,
              label: '确认状态',
              name: 'confirm_state',
              fieldProps: {
                options: [
                  //INIT(未确认),SUPPLIER_CONFIRM(供应商已确认),STORE_CONFIRM(门店已确认),INVALID(已作废)
                  { label: '未确认', value: 'INIT', type: 'info' },
                  {
                    label: '供应商已确认',
                    value: 'SUPPLIER_CONFIRM',
                    type: 'warning',
                  },
                  {
                    label: '已确认收货',
                    value: 'STORE_CONFIRM',
                    type: 'warning',
                  },
                  { label: '已作废', value: 'INVALID', type: 'error' },
                ],
              },
            },
            { id: ErpFieldKeyMap?.erpFidTooltip, label: '单据号', name: 'fid' },
            {
              id: 'erpCommonSelect',
              label: '单据类型',
              name: 'type',
              fieldProps: {
                options: [
                  {
                    label: '直供',
                    value: 1,
                  },
                  {
                    label: '仓配',
                    value: 2,
                  },
                ],
              },
              onChange: (value: any, form: FormInstance) => {
                if (value === 1) {
                  form.setFieldsValue({
                    order_types: [
                      'DIRECT_SUPPLY_CAR_SALE',
                      'DIRECT_SUPPLY_RESERVE',
                    ],
                  });
                }
                if (value == 2) {
                  form.setFieldsValue({
                    order_types: ['STOREHOUSE_DELIVERY'],
                  });
                }
                if (!value) {
                  form.setFieldsValue({
                    order_types: [],
                  });
                }
              },
            },
            {
              id: 'commonInput',
              label: '',
              name: 'order_types',
              hidden: true,
              dependencies: ['type'],
            },
            {
              id: ErpFieldKeyMap?.erpDeliveryType,
              label: '配送类型',
              name: 'delivery_type',
            },
            {
              id: ErpFieldKeyMap?.erpDownstreamDocuments,
              label: '下游单据',
              name: 'ref_order_type',
            },
            {
              id: ErpFieldKeyMap?.erpDocumentNumber,
              label: '下游单据号',
              name: 'ref_order_fid',
              onChange: (e, form) => {
                if (!e?.target?.value) {
                  form?.setFieldValue('ref_order_fid', null);
                }
              },
            },
            {
              id: ErpFieldKeyMap?.erpOutOrgIds,
              label: '收货组织',
              name: 'org_ids',
            },
            {
              id: ErpFieldKeyMap?.erpReceivingStore,
              label: '收货门店',
              name: 'store_id',
            },
            {
              id: ErpFieldKeyMap?.erpShippingOrganization,
              label: '发货组织',
              name: 'out_org_ids',
            },
            {
              id: ErpFieldKeyMap?.outStoreIdsNoOrg,
              label: '发货门店',
              name: 'out_store_ids',
              onChange: (e, form) => {
                if (!e) {
                  form?.setFieldValue('out_storehouse_id', null);
                  form?.setFieldValue('out_storehouse_ids', null);
                }
              },
            },
            {
              id: ErpFieldKeyMap?.erpOutStorehouseId,
              label: '发货仓库',
              name: 'out_storehouse_id',
              onChange: (e, form) => {
                if (!!e) {
                  form?.setFieldValue('out_storehouse_ids', [e]);
                } else {
                  form?.setFieldValue('out_storehouse_ids', null);
                }
              },
            },
            {
              id: ErpFieldKeyMap?.erpSupplierIds,
              label: '供应商',
              name: 'supplier_ids',
            },
            { id: 'erpitemIds', label: '商品档案', name: 'item_ids' },
            {
              id: ErpFieldKeyMap?.erpCreateBy,
              label: '制单人',
              name: 'create_by',
              onChange: (e, form) => {
                if (!e?.target?.value) {
                  form?.setFieldValue('create_by', null);
                }
              },
            },
            {
              id: ErpFieldKeyMap?.erpAuditBy,
              label: '审核人',
              name: 'audit_by',
              onChange: (e, form) => {
                if (!e?.target?.value) {
                  form?.setFieldValue('audit_by', null);
                }
              },
            },
            {
              id: ErpFieldKeyMap?.erpAuditBy,
              label: '确认人',
              name: 'confirm_by',
              onChange: (e, form) => {
                if (!e?.target?.value) {
                  form?.setFieldValue('confirm_by', null);
                }
              },
            },
          ],
        }}
        deleteFieldProps={{
          name: '删除',
          url: hasAuth(['门店订单', '删除'])
            ? '/erp/hxl.erp.storeorder.batchdelete'
            : '',
          params: (data: any, v: any) => {
            return { fids: v?.map((j: { fid: any }) => j?.fid) };
          },
        }}
        extra={(context) => {
          const {
            fetchData,
            selectRowKeys,
            selectRow,
            dataSource,
            requestForm,
            setLoading,
            loading,
          } = context;
          fetchDataRef.current = fetchData;
          dataSourceRef.current = dataSource;
          return (
            <XlbButton.Group>
              {(hasAuth(['门店订单/直供', '编辑']) ||
                hasAuth(['门店订单/仓配', '编辑'])) && (
                <XlbButton
                  type="primary"
                  label="新增"
                  onClick={async () => {
                    await XlbTipsModal({
                      tips: (
                        <XlbBasicForm
                          form={form}
                          style={{ width: '100%', margin: '12px 0 -8px 0' }}
                          layout="horizontal"
                          initialValues={{
                            delivery_type: 1,
                          }}
                        >
                          <XlbBasicForm.Item
                            name="delivery_type"
                            label="订单类型"
                            rules={[
                              { required: true, message: '请选择订单类型' },
                            ]}
                          >
                            <XlbRadio.Group>
                              {hasAuth(['门店订单/直供', '编辑']) && (
                                <XlbRadio value={1}>直供订单</XlbRadio>
                              )}
                              {hasAuth(['门店订单/仓配', '编辑']) && (
                                <XlbRadio value={2}>仓配订单</XlbRadio>
                              )}
                            </XlbRadio.Group>
                          </XlbBasicForm.Item>
                        </XlbBasicForm>
                      ),
                      isCancel: true,
                      onOkBeforeFunction: async () => {
                        deliveryTypeRef.current =
                          form.getFieldValue('delivery_type');
                        // 直供详情
                        if (form.getFieldValue('delivery_type') === 1) {
                          form.setFieldsValue({
                            delivery_type: 1,
                          });
                          setRecord({});
                          pageModalItemRef.current?.setOpen(true);
                        }
                        //仓配详情

                        if (form.getFieldValue('delivery_type') === 2) {
                          form.setFieldsValue({
                            delivery_type: 2,
                          });
                          setRecord({ fid: 1 });
                          pageModalItemRef.current?.setOpen(true);
                        }
                        form.resetFields();
                        return true;
                      },
                    });
                  }}
                  icon={<XlbIcon size={16} name="jia" />}
                />
              )}
              {hasAuth(['门店订单', '导出']) && (
                <XlbDropdownButton
                  label="导出"
                  dropList={[
                    {
                      label: '导出',
                      disabled: !dataSource?.length || loading,
                    },
                    {
                      label: '导出明细',
                      disabled: !selectRow?.length || loading,
                    },
                  ]}
                  dropdownItemClick={(value: number, item, e) => {
                    if (item?.label == '导出') {
                      exportItem(1, e, requestForm, setLoading);
                    }
                    if (item?.label == '导出明细') {
                      exportDetailItem(e, selectRow, setLoading);
                    }
                  }}
                ></XlbDropdownButton>
              )}
              {hasAuth(['门店订单', '编辑']) && (
                <XlbButton
                  label="复制"
                  type="primary"
                  disabled={selectRowKeys?.length !== 1 || loading}
                  onClick={() => copyItem1(selectRow, setLoading)}
                  icon={<XlbIcon name="fuzhi" />}
                />
              )}
              {/* { label: '仓配单批量制单' } */}
              {(hasAuth(['门店订单/直供', '编辑']) ||
                hasAuth(['门店订单/仓配', '编辑'])) && (
                <XlbDropdownButton
                  dropList={[
                    ...(hasAuth(['门店订单/直供', '编辑'])
                      ? [{ label: '直供单批量制单' }]
                      : []),
                    ...(hasAuth(['门店订单/仓配', '编辑'])
                      ? [{ label: '仓配单批量制单' }]
                      : []),
                  ]}
                  dropdownItemClick={(index, item) => {
                    if (item?.label === '直供单批量制单') {
                      setBatchOrderVisible(true);
                    } else if (item?.label === '仓配单批量制单') {
                      setDeliveryOrderVisible(true);
                    }
                  }}
                  label={'批量制单'}
                ></XlbDropdownButton>
              )}
              {(hasAuth(['门店订单/直供', '导入']) ||
                hasAuth(['门店订单/仓配', '导入'])) && (
                <XlbDropdownButton
                  dropList={[
                    ...(hasAuth(['门店订单/直供', '导入'])
                      ? [{ label: '导入直供单' }]
                      : []),
                    ...(hasAuth(['门店订单/仓配', '导入'])
                      ? [{ label: '导入仓配单' }]
                      : []),
                  ]}
                  dropdownItemClick={async (index, item) => {
                    if (item?.label === '导入直供单') {
                      const res = await XlbImportModal({
                        importUrl: `${process.env.ERP_URL}/erp/hxl.erp.storeorder.direct.import`,
                        templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.storeorder.template.direct.download`,
                        params: {
                          type: 'DIRECT_SUPPLY_RESERVE',
                        },
                        templateName: '直供单模板',
                        callback: (res: any) => {
                          if (res.code === 0) {
                            fetchData();
                          }
                        },
                      });
                    } else if (item?.label === '导入仓配单') {
                      const res = await XlbImportModal({
                        importUrl: `${process.env.ERP_URL}/erp/hxl.erp.storeorder.storehousedelivery.import`,
                        templateUrl: `${process.env.ERP_URL}/erp/hxl.erp.storeorder.template.storehousedelivery.download`,
                        params: {
                          type: 'STOREHOUSE_DELIVERY',
                        },
                        templateName: '仓配单模板',
                        callback: (res: any) => {
                          if (res.code === 0) {
                            fetchData();
                          }
                        },
                      });
                    }
                  }}
                  label={'批量导入'}
                ></XlbDropdownButton>
              )}
              {hasAuth(['门店订单', '审核']) && (
                <XlbButton
                  label="批量审核"
                  type="primary"
                  disabled={!selectRowKeys?.length || loading}
                  onClick={() => requestBatchaudit(selectRow, setLoading)}
                  icon={<XlbIcon name="shenhe" />}
                />
              )}
              {hasAuth(['门店订单', '作废']) && (
                <XlbButton
                  label="作废"
                  type="primary"
                  disabled={
                    loading ||
                    (selectRowKeys?.length == 1 &&
                      !selectRow?.[0]?.ref_order_id &&
                      selectRow?.[0]?.state === 'AUDIT' &&
                      selectRow?.[0]?.confirm_state === 'INIT' &&
                      ((selectRow?.[0].type == 'STOREHOUSE_DELIVERY' &&
                        hasAuth(['门店订单/仓配', '作废'])) ||
                        (selectRow?.[0].type == 'DIRECT_SUPPLY_RESERVE' &&
                          hasAuth(['门店订单/直供', '作废']))))
                      ? false
                      : true
                  }
                  onClick={() =>
                    requestInvalid(selectRow?.[0], fetchData, setLoading)
                  }
                  icon={<XlbIcon name="shenqingtuihuo" />}
                />
              )}
              {hasAuth(['门店订单', '编辑']) && (
                <XlbButton
                  label="多单合并"
                  type="primary"
                  disabled={!selectRowKeys?.length || loading}
                  onClick={() => mergeItem(selectRow, setLoading)}
                  icon={<MergeCellsOutlined />}
                />
              )}
              {hasAuth(['门店订单/仓配', '编辑']) && (
                <XlbButton
                  label="生成缺货补货单"
                  type="primary"
                  disabled={!selectRowKeys?.length || loading}
                  onClick={() => generateOutOrder(selectRow, setLoading)}
                  icon={<EditOutlined />}
                />
              )}
            </XlbButton.Group>
          );
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.storeorder.page',
          tableColumn: Columns.map((item: any) => {
            if (item.code === 'fid') {
              return {
                ...item,
                render: (text: any, record: any, index: any) => {
                  return (
                    <span
                      className="link cursors"
                      onClick={(e) => {
                        if (record?.type == 'STOREHOUSE_DELIVERY') {
                          e.stopPropagation();
                          goDetail(record, index['index']);
                        } else {
                          // 跳直供详情
                          deliveryTypeRef.current = 1;
                          form?.setFieldsValue({
                            delivery_type: 1,
                          });
                          setRecord(record);
                          pageModalItemRef.current?.setOpen(true);
                        }
                      }}
                    >
                      {text}
                    </span>
                  );
                },
              };
            } else if (item.code == 'ref_order_fid') {
              return {
                ...item,
                render: (text: any, record: any) => {
                  return (
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-start',
                      }}
                    >
                      <span
                        className={'link cursors'}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (record?.type == 'STOREHOUSE_DELIVERY') {
                            openRefOrder(record);
                          } else {
                            const fid = text?.includes(',')
                              ? text.split(',')[0]
                              : text;
                            // 截取前两个字如果为YH,跳门店补货单，如果为PO,跳采购订单
                            if (fid.slice(0, 2) == 'YH') {
                              navigate(
                                '/xlb_erp/storeSupplyOrder/item',
                                {
                                  fid: fid,
                                },
                                'xlb_erp',
                                true,
                              );
                            } else if (fid.slice(0, 2) == 'PO') {
                              navigate(
                                '/xlb_erp/purchaseOrder/item',
                                {
                                  fid: fid,
                                },
                                'xlb_erp',
                                true,
                              );
                            }
                          }
                          return;
                        }}
                      >
                        {text?.includes(',') ? text.split(',')[0] : text}
                      </span>
                      {text?.includes(',') && (
                        <Tooltip
                          color="#FFF"
                          title={
                            <div
                              style={{
                                color: '#1D2129',
                                minWidth: 80,
                                textAlign: 'left',
                              }}
                            >
                              {text?.split(',')?.length > 1 &&
                                text?.split(',')?.map((v: any) => {
                                  return (
                                    <div
                                      key={v}
                                      className="link cursors"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        // 截取前两个字如果为YH,跳门店补货单，如果为PO,跳采购订单
                                        if (v.slice(0, 2) == 'YH') {
                                          navigate(
                                            '/xlb_erp/storeSupplyOrder/item',
                                            {
                                              fid: v,
                                            },
                                            'xlb_erp',
                                            true,
                                          );
                                        } else if (v.slice(0, 2) == 'PO') {
                                          navigate(
                                            '/xlb_erp/purchaseOrder/item',
                                            {
                                              fid: v,
                                            },
                                            'xlb_erp',
                                            true,
                                          );
                                        }
                                      }}
                                    >
                                      {v}
                                    </div>
                                  );
                                })}
                            </div>
                          }
                          placement={'bottom'}
                        >
                          <span
                            className={'default'}
                            style={{ display: 'inline-block', marginLeft: 3 }}
                          >
                            (
                            {text?.split(',')?.length > 1
                              ? text.split(',').length
                              : 0}
                            )
                          </span>
                        </Tooltip>
                      )}
                    </div>
                  );
                },
              };
            } else {
              return item;
            }
          }),
          primaryKey: 'fid',
          selectMode: 'multiple',
          immediatePost: true,
        }}
      />
    </>
  );
};
export default Index;