import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbTipsModal,
} from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import {
  basicTable,
  inFid,
  inItem,
  inStore,
  inSupplier,
  searchFormList,
} from './data';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const wholeSaleAnalyze = () => {
  const userInfo = LStorage.get('userInfo');
  const pageRef = useRef<XlbPageContainerRef>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // searchFormList
  const [form] = XlbBasicForm.useForm();
  const [formList, setFormList] = useState(searchFormList);
  // table
  const [itemArr, setItemArr] = useState(basicTable);

  const onValuesChange = (e: Object) => {};

  // 获取数据
  const prevPost = () => {
    const group_by = form.getFieldValue('group_by');
    const [start_time, end_time] = form.getFieldValue('query_date');
    const time = [
      dayjs(start_time).format('YYYY-MM-DD') + ' 00:00:00',
      dayjs(end_time).format('YYYY-MM-DD') + ' 23:59:59',
    ];
    onChangeKey(group_by);
    const data: any = {
      ...form.getFieldsValue(true),
      query_date: time,
      group_by: form.getFieldValue('group_by') || [],
    };
    delete data.Data_Compact_RangeType_query_date;
    return data;
  };
  const getData = async () => {
    if (
      !form.getFieldValue('group_by') ||
      form.getFieldValue('group_by')?.length === 0
    ) {
      XlbTipsModal({
        tips: '请选择汇总条件',
      });
      return;
    }
    setIsLoading(true);
    pageRef?.current?.fetchData();
    setIsLoading(false);
  };

  const tableRender = (item: any = itemArr) => {
    switch (item.code) {
      case 'money':
      case 'actual_delivered_money':
      case 'actual_received_money':
        return (item.render = (value: any) => (
          <div>
            {hasAuth(['直供分析/价格', '查询']) && value !== '****'
              ? value
                ? Number(value).toFixed(4)
                : '0.0000'
              : '****'}
          </div>
        ));
      case 'quantity':
      case 'actual_delivered_quantity':
      case 'actual_received_quantity':
        return (item.render = (value: any) => (
          <div>{value ? Number(value).toFixed(3) : '0.000'}</div>
        ));
      case 'diff_quantity':
        return (item.render = (value: any) => (
          <div>
            {Number(value) == 0 ? (
              <span className="info" style={{ color: '#00b42b' }}>
                {value}
              </span>
            ) : (
              <span className="info" style={{ color: '#FF0000' }}>
                {value}
              </span>
            )}
          </div>
        ));
      default:
        return (item.render = (value: any) => <div>{value}</div>);
    }
    return item;
  };

  const onChangeKey = (ids: any = []) => {
    // 根据汇总条件计算不同的表头
    let TableName = JSON.parse(JSON.stringify(basicTable));
    const showName = {
      FID: inFid, //单据号
      ITEM: inItem, //商品
      STORE: inStore, //收货门店
      SUPPLIER: inSupplier, // 供应商
    };
    type ItemId = keyof typeof showName;
    const spliceTable: any[] = [];
    Object.keys(showName).forEach((key) => {
      if (ids.includes(key as ItemId)) {
        spliceTable.push(...showName[key as ItemId]);
      }
    });
    TableName.splice(1, 0, ...spliceTable);
    setItemArr(TableName);
  };

  itemArr.map((v) => tableRender(v));
  return (
    <XlbPageContainer
      ref={pageRef}
      url={'/erp/hxl.erp.storeorder.direct.analyze.find'}
      tableColumn={itemArr}
      immediatePost={false}
      prevPost={() => prevPost()}
      changeColumnAndResetDataSource={false}
    >
      <SearchForm>
        <XlbForm
          form={form}
          formList={formList}
          initialValues={{
            date_type: 'CREATE',
            query_date: [dayjs(), dayjs()],
          }}
          isHideDate
          onValuesChange={onValuesChange}
        />
      </SearchForm>
      <ToolBtn>
        {(context) => {
          const { dataSource } = context;
          return (
            <XlbButton.Group>
              {hasAuth(['直供分析', '查询']) && (
                <XlbButton
                  label="查询"
                  type={'primary'}
                  disabled={isLoading}
                  onClick={() => getData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        primaryKey="_index"
        selectMode={'multiple'}
        key="store_id"
        isLoading={isLoading}
      />
    </XlbPageContainer>
  );
};
export default wholeSaleAnalyze;
