import { FC, Fragment, useEffect } from 'react'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import ErpProvider from '@/pages/xlb_erp/provider'
import { columnWidthEnum } from '@/data/common/constant'
import { XlbProPageContainer, type XlbTableColumnProps } from '@xlb/components'
const Index: FC = () => {
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: columnWidthEnum.INDEX,
      align: 'center'
    },
    {
      name: '载具名称',
      code: 'name',
      width: 140,
      // features: { sortable: true, details: false },
      features: { sortable: true }
    },
    {
      name: '单价',
      code: 'price',
      align: 'right',
      width: 110,
      features: { sortable: true, format: 'MONEY' }
    },
    {
      name: '启用',
      code: 'enabled',
      width: 70,
      features: { sortable: true },
      render: (value: boolean) => {
        const showColor = value ? 'success' : 'danger'
        return <div className={`${showColor}`}>{value ? '是' : '否'}</div>
      }
    },
    {
      name: '押金起始量',
      code: 'initial_quantity',
      align: 'right',
      width: 110,
      features: { sortable: true, format: 'QUANTITY' }
    },
    {
      name: '最近更新人',
      code: 'update_by',
      width: 110
    },
    {
      name: '最近更新时间',
      code: 'update_time',
      width: 120,
      features: { sortable: true, format: 'TIME' }
    }
  ]

  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [
          { id: 'keyword', name: 'keyword', label: '关键字' },
          {
            id: 'commonSelect',
            name: 'enabled',
            label: '是否启用',
            options: [
              { label: '启用', value: true },
              { label: '禁用', value: false }
            ]
          }
        ]
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.basket.page',
        tableColumn: tableColumn,
        selectMode: 'single',
        immediatePost: true,
        keepDataSource: false,
        showColumnsSetting: true
      }}
      // deleteFieldProps={{
      //   name: '删除',
      //   showField: 'poi_id',
      //   url: hasAuthority(['物资载具', '删除']) ? '/erp/hxl.erp.basket.delete' : ''
      // }}
      // addFieldProps={{
      //   name: '新增',
      //   url: hasAuthority(['物资载具', '编辑']) ? '/erp/hxl.erp.basket.save' : ''
      // }}
      details={{
        mode: 'modal',
        isCancel: true,
        width: 380,
        initialValues: {
          id: -1,
          enabled: false,
          default: false
        },
        title: (obj) => {
          return <div>{obj?.id !== -1 ? '编辑' : '新增'}</div>
        },
        hiddenSaveBtn: true,
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: 'commonInput',
                  itemSpan: 24,
                  label: '载具名称',
                  name: 'name',
                  disabled: true,
                  rules: [{ required: true, message: '载具名称不能为空' }],
                  fieldProps: { maxLength: 20 ,width: 226}
                },
                {
                  id: 'commonInputNumber',
                  itemSpan: 24,
                  label: '单价',
                  name: 'price',
                  rules: [{ required: true, message: '单价不能为空' }],
                  fieldProps: { maxLength: 10, precision: 2 }
                },
                {
                  id: 'commonInputNumber',
                  itemSpan: 24,
                  label: '押金起始量',
                  name: 'initial_quantity',
                  fieldProps: { maxLength: 10 ,min: 0}
                },
                {
                  id: ErpFieldKeyMap.erpBasketEnabled,
                  label: '是否启用',
                  itemSpan: 24,
                  disabled: true,
                  fieldProps: {
                    options: [{ label: '启用', value: 'enabled' }]
                  }
                }
              ]
            }
          }
        ],
        updateFieldProps: {
          url: '/erp/hxl.erp.basket.update'
        }
      }}
    />
  )
}

export default Index
