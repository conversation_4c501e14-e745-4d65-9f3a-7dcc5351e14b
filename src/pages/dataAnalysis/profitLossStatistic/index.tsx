import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import { SearchOutlined, UploadOutlined } from '@ant-design/icons';
import {
  XlbBasicForm,
  XlbButton,
  XlbColumns,
  XlbForm,
  XlbMessage,
  XlbPageContainer,
  XlbTable,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { dateManipulation, LStorage, XlbFetch } from '@xlb/utils';
import { Tabs } from 'antd';
import { cloneDeep } from 'lodash-es';
import moment from 'moment';
import { useEffect, useState } from 'react';

import {
  classLevelList,
  formList,
  originTableList,
  syResultArr,
  tableList,
  tableListDetail,
} from './data';
import { getProfitLossData, getStoreHouseList } from './server';
const Goodsway = () => {
  const { TabPane } = Tabs;
  const [rowDataSum, setRowDataSum] = useState<any[]>([]); //汇总
  const [rowDataDetail, setRowDataDetail] = useState<any[]>([]); //明细
  const [paginDetail, setPaginDetail] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  }); //明细
  const [paginSum, setPaginSum] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  }); //汇总
  const [isReson, setIsReson] = useState<boolean>(false);
  const [isFold, setIsFold] = useState<boolean>(false);
  const [form] = XlbBasicForm.useForm();
  const [tabsKey, setTabsKey] = useState('plSum');
  const [footerData, setFooterData] = useState<any[]>([]);
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(tableList)),
  );
  const [itemArrDetail, setItemArrDetail] = useState<any[]>(
    JSON.parse(JSON.stringify(tableListDetail)),
  );
  const [sortType, setSortType] = useState<{ order: string; code: string }>({
    order: '',
    code: '',
  });

  // const [rowData, setRowData] = useState<any[]>([]);

  const [isLoading, setisLoading] = useState<boolean>(false);

  const [formLists, setSearchFormLists] = useState(cloneDeep(formList));
  //表格数据
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'quantity':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {value ? Number(value).toFixed(3) : '0.000'}
            </div>
          );
        };
        break;
      case 'money':
      case 'retail_money':
      case 'no_tax_money':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {hasAuth(['损益统计报表/价格', '查询'])
                ? value
                  ? Number(value).toFixed(2)
                  : '0.00'
                : '***'}
            </div>
          );
        };
        break;
      case 'audit_time':
        item.render = (value: any, record: any) => (
          <div className="info overwidth"> {dateManipulation(value)}</div>
        );
        break;
    }
  };

  useEffect(() => {
    sortType.code !== '' && getData();
  }, [sortType]);

  const checkData = (page_number: number = 1, page_size?: number) => {
    const data: any = {
      audit_date: [
        form.getFieldValue('compactDatePicker')?.[0] + ' 00:00:00',
        form.getFieldValue('compactDatePicker')?.[1] + ' 23:59:59',
      ],
      summary_types:
        tabsKey === 'plSum'
          ? form.getFieldValue('summary_types')
            ? form.getFieldValue('summary_types')
            : []
          : [],
      category_level: form.getFieldValue('category_level'),
      store_ids: form.getFieldValue('store_ids')
        ? form.getFieldValue('store_ids')
        : [],
      storehouse_ids: form.getFieldValue('storehouse_ids')?.length
        ? form.getFieldValue('storehouse_ids')
        : null,
      item_ids: form.getFieldValue('item_ids')
        ? form.getFieldValue('item_ids')
        : null,
      item_category_ids: form.getFieldValue('item_category_ids')
        ? form.getFieldValue('item_category_ids')
        : null,
      item_dept_ids: form.getFieldValue('item_dept_ids')
        ? form.getFieldValue('item_dept_ids')
        : null,
      report_type: form.getFieldValue('report_type'),
      result_type: form.getFieldValue('result_type'),
      reason_ids: form.getFieldValue('reason_ids')
        ? form.getFieldValue('reason_ids')
        : null,
      reason_names: form.getFieldValue('reason_names')
        ? form.getFieldValue('reason_names')?.split(',')
        : null,
      unit_type: form.getFieldValue('unit_type') || 'BASIC',
      page_size: page_size || pagin.pageSize,
      page_number: page_number - 1,
    };
    data.orders = sortType.code
      ? [
          {
            direction: sortType.order.toUpperCase(),
            property: sortType.code,
          },
        ]
      : undefined;
    return data;
  };
  const processingTableColumns = () => {
    const { unit_type } = form.getFieldsValue(true);
    const unitMapping: Record<string, string> = {
      BASIC: '基本单位',
      STOCK: '库存单位',
      WHOLESALE: '批发单位',
      PURCHASE: '采购单位',
      DELIVERY: '配送单位',
    };
    const unitName = unitMapping[unit_type];
    itemArrDetail.forEach((item) => {
      if (Object.values(unitMapping).includes(item.name)) {
        item.name = unitName;
      }
    });
    setItemArrDetail([...itemArrDetail]);
  };
  // 获取数据
  const getData = async (page_number: number = 1, page_size?: number) => {
    processingTableColumns();
    // LStorage.set('profitLossStatistic', {
    //   ...form.getFieldsValue(),
    // });
    setisLoading(true);
    const data: any = checkData(page_number, page_size);

    const res = await getProfitLossData({ ...data });
    setisLoading(false);
    if (res?.code === 0) {
      const data_u = res.data.content;
      setPagin({
        ...pagin,
        pageSize: page_size || pagin?.pageSize,
        pageNum: page_number,
        total: res.data?.total_elements,
      });
      if (tabsKey === 'plSum') {
        setRowDataSum(data_u || []);
        // 设置合计行
        footerData[0] = {};
        footerData[0]._index = '合计';
        footerData[0].money = res.data.money?.toFixed(2) || '0.00';
        footerData[0].no_tax_money =
          res.data.no_tax_money?.toFixed(2) || '0.00';
        footerData[0].quantity = res.data.quantity?.toFixed(3) || '0.000';
        footerData[0].retail_money = res.data.retail_money || 0;
        setFooterData(footerData);
        setPaginSum({
          ...pagin,
          pageSize: page_size || pagin?.pageSize,
          pageNum: page_number,
          total: res.data?.total_elements,
        });
      } else {
        setRowDataDetail(data_u || []);
        // 设置合计行
        footerData[0] = {};
        footerData[0]._index = '合计';
        footerData[0].money = res.data.money?.toFixed(2) || '0.00';
        footerData[0].no_tax_money =
          res.data.no_tax_money?.toFixed(2) || '0.00';
        footerData[0].quantity = res.data.quantity?.toFixed(3) || '0.000';
        footerData[0].retail_money = res.data.retail_money || 0;
        setFooterData(footerData);
        setPaginDetail({
          ...pagin,
          pageSize: page_size || pagin?.pageSize,
          pageNum: page_number,
          total: res.data?.total_elements,
        });
      }
    }
  };

  const oldArr = () => {
    let tableArr: any = [];
    switch (tabsKey) {
      case 'plSum':
        tableArr = cloneDeep(originTableList);
        break;
      case 'plDetail':
        tableArr = cloneDeep(tableListDetail);
        break;
      default:
        tableArr = cloneDeep(originTableList);
    }
    return tableArr;
  };

  // 导出
  const exportItem = async (e: any) => {
    const data = { ...checkData(pagin.pageNum) };
    // let xlsxName: any;
    setisLoading(true);
    const res = await XlbFetch.post(
      '/erp/hxl.erp.stockprofitlossreport.stocksummary.export',
      {
        ...data,
      },
    );
    setisLoading(false);
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };
  // const initData = async () => {
  //   const formData = LStorage.get('profitLossStatistic');
  //   if (formData) {
  //     form.setFieldsValue({
  //       ...formData,
  //       start_time: moment(formData.start_time),
  //       end_time: moment(formData.end_time),
  //       compactDatePicker: [
  //         LStorage.get('profitLossStatistic').compactDatePicker?.[0],
  //         LStorage.get('profitLossStatistic').compactDatePicker?.[1],
  //       ],
  //     });
  //   } else {
  //     form.setFieldsValue({
  //       // time_desc: 0,
  //       // time_type: 0,
  //       // start_time: moment(),
  //       // end_time: moment(),
  //       compactDatePicker: [
  //         moment().format('YYYY-MM-DD'),
  //         moment().format('YYYY-MM-DD'),
  //       ],
  //     });
  //   }
  // };

  // 切换表格数据
  const onChangeKey = (key: any) => {
    const { unit_type } = form.getFieldsValue(true);
    form.setFieldValue('unit_type', unit_type || 'BASIC');
    setTabsKey(key);
    // let tableArr: any = [];
    switch (key) {
      case 'plSum':
        setPagin(paginSum);
        break;
      case 'plDetail':
        setPagin(paginDetail);
        break;
      default:
        setPagin(paginSum);
        break;
    }
  };

  //查询门店下仓库
  const getStockData = async (store_ids: any[]) => {
    if (store_ids.length === 1) {
      const res = await getStoreHouseList({
        store_id: store_ids?.[0],
      });
      if (res.code === 0) {
        const obj = res.data.map((v: any) => ({
          label: v.name,
          value: v.id,
        }));
        form.setFieldValue('storehouse_ids_options', obj);
      } else {
        form.setFieldValue('storehouse_ids_options', []);
      }
    } else {
      form.setFieldValue('storehouse_ids_options', []);
    }
  };

  const onValuesChange = async (changeValues: any, _values: any) => {
    // console.log('changeValues: ', changeValues);
    // 选择门店后查询仓库信息
    if (changeValues.store_ids) {
      form.setFieldValue('storehouse_ids', undefined);
      getStockData(changeValues.store_ids);
    }
    if ('report_type' in changeValues && changeValues?.report_type) {
      const reportType = changeValues.report_type as '0' | '1' | '2';
      const adjustments = {
        '1': { adjustReasonHidden: false, profitLossHidden: true },
        '0': { adjustReasonHidden: true, profitLossHidden: false },
        '2': { adjustReasonHidden: true, profitLossHidden: true },
      };

      const { adjustReasonHidden, profitLossHidden } = adjustments[reportType];

      const adjustReasonItem = formLists.find(
        (item) => item.label === '调整原因',
      );
      const profitLossItem = formLists.find(
        (item) => item.label === '损益结果',
      );

      if (adjustReasonItem) adjustReasonItem.hidden = adjustReasonHidden;
      if (profitLossItem) profitLossItem.hidden = profitLossHidden;

      setSearchFormLists([...formLists]);
    }
  };
  // 根据筛选显示表格数据
  const howShowList = () => {
    const indices = form.getFieldValue('summary_types') || [];
    // 根据汇总条件 ---
    const showName = [
      { name: '门店', show: false, type: ['门店'] },
      { name: '商品代码', show: false, type: ['商品档案'] },
      { name: '商品条码', show: false, type: ['商品档案'] },
      { name: '调整原因', show: false, type: ['调整原因'] },
      { name: '商品名称', show: false, type: ['商品档案'] },
      { name: '采购规格', show: false, type: ['商品档案'] },
      { name: '商品类别', show: false, type: ['商品档案', '商品类别'] },
      { name: '商品部门', show: false, type: ['商品档案', '商品部门'] },
    ];
    const names = ['门店', '商品档案', '商品类别', '商品部门', '调整原因'];
    const l = indices
      .map((index: any) => names[index])
      .filter((name: any) => name);
    names.map((v) => {
      if (l.indexOf(v) !== -1) {
        showName.map((s) => {
          if (s.type?.indexOf(v) !== -1) {
            if (v === '调整原因') {
              s.show = isReson;
            } else {
              s.show = true;
            }
          }
        });
      }
    });
    if (tabsKey === 'plSum') {
      return tableList.filter((v) => {
        const findItem = showName.find((s) => s.name === v.name);
        if (findItem) {
          return findItem.show;
        }
        return true;
      });
    } else {
      return itemArrDetail;
    }
  };
  const setChangeMethod = (formList: any) => {
    formList.find((v) => v.label === '损益类型')!.onChange = async (
      value: any,
    ) => {
      formList.find((v) => v.label === '调整原因')!.hidden = value !== '1';
      setSearchFormLists([...formList]);
      if (value == 0) {
        formList.find((v) => v.label === '损益结果')!.options = syResultArr;
        setSearchFormLists([...formList]);
        form.setFieldValue('summary_types', ['0']);
      } else {
        form.setFieldsValue({
          result_type: null,
        });
        formList.find((v) => v.label === '损益结果')!.options = [];
        setSearchFormLists([...formList]);
        if (value == 2) {
          form.setFieldValue('summary_types', ['0']);
        }
      }
      setIsReson(value == 1);
      if (value !== 1) {
        form.setFieldsValue({
          reason_names: '',
          reason_ids: null,
        });
      }
    };
    const summaryConditionItem = formList.find((v) => v.label === '汇总条件');
    if (!summaryConditionItem) return
    summaryConditionItem.onChange = async (
      value: any,
    ) => {
      if (!value.length) {
        XlbTipsModal({
          tips: '必须选择汇总条件！',
          isCancel: true,
          title: '提示',
        });
        form.setFieldValue('summary_types', ['0']);
        return;
      }
      if (value.indexOf('2') !== -1) {
        formList.find((v) => v.label === '类别等级')!.options = classLevelList;
        form.setFieldsValue({
          category_level: '0',
        });
      } else {
        form.setFieldsValue({
          category_level: undefined,
        });
        formList.find((v) => v.label === '类别等级')!.options = [];
      }
      setSearchFormLists([...formList]);
      howShowList();
      getData(1);
    };
  }
  useEffect(() => {
    // initData();
    setChangeMethod(formLists)

    if (localStorage.userInfo) {
      form.setFieldsValue({
        store_names: JSON.parse(localStorage.userInfo).value.store_name,
        store_ids: [JSON.parse(localStorage.userInfo).value.store_id],
      });
      if (form.getFieldValue('store_names')?.split(',').length < 2) {
        getStockData([JSON.parse(localStorage.userInfo).value.store_id]);
      }
    }
    form.setFieldsValue({
      unit_type: 'BASIC',
    });
  }, []);

  itemArr.map((v) => tableRender(v));
  return (
    <XlbPageContainer>
      <div
        className={'button_box row-flex'}
        style={{ marginBottom: '10px', padding: '0 16px' }}
      >
        <div style={{ width: '90%' }} className="row-flex">
          <XlbButton.Group>
            {hasAuth(['损益统计报表', '查询']) && (
              <XlbButton
                label={'查询'}
                disabled={isLoading}
                type="primary"
                onClick={() => getData()}
                icon={<SearchOutlined />}
              />
            )}
            {hasAuth(['损益统计报表', '导出']) && (
              <XlbButton
                type="primary"
                label={'导出'}
                disabled={
                  tabsKey === 'plSum'
                    ? !rowDataSum.length
                    : !rowDataDetail.length
                }
                onClick={(e: any) => exportItem(e)}
                icon={<UploadOutlined />}
              />
            )}
          </XlbButton.Group>
        </div>
        <div
          style={{
            width: '10%',
            height: '28px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-end',
            columnGap: '8px',
          }}
        >
          {/* <Tooltip title={isFold ? '收起' : '展开'}>
            <XlbIcon
              data-type={'顶层展开收起'}
              size={20}
              onClick={() => setIsFold(!isFold)}
              name="shouqi"
              className={classnames('xlb-columns-main-btn', {
                'xlb-columns-fold': !isFold,
                'xlb-columns-expand-btn-dev': true,
              })}
            />
          </Tooltip> */}
          <XlbColumns
            isFold={isFold}
            isFoldChange={setIsFold}
            url={
              tabsKey === 'plSum'
                ? '/erp/hxl.erp.stockprofitlossreport.stocksummary.summary.page'
                : '/erp/hxl.erp.stockprofitlossreport.stocksummary.detail.page'
            }
            originColumns={oldArr()}
            value={howShowList()}
            onChange={tabsKey === 'plSum' ? setItemArr : setItemArrDetail}
            name={
              tabsKey === 'plSum'
                ? '/erp/hxl.erp.stockprofitlossreport.stocksummary.page-TOTAL'
                : '/erp/hxl.erp.stockprofitlossreport.stocksummary.page-DETAIL'
            }
            formList={formLists}
            originFormList={tabsKey === 'plDetail'
              ? formList.filter(
                (v) => v.label !== '汇总条件' && v.label !== '类别等级',
              )
              : formList.filter((v) => v.label !== '查询单位')}
            onFormChange={(e: any) => {
              setChangeMethod(e)
              setSearchFormLists(e)
            }}
          />
        </div>
      </div>

      <div
        style={{ display: isFold ? 'none' : 'block' }}
        className={'form_header_box'}
      >
        <XlbForm
          onValuesChange={onValuesChange}
          formList={
            tabsKey === 'plDetail'
              ? formLists.filter(
                  (v) => v.label !== '汇总条件' && v.label !== '类别等级',
                )
              : formLists.filter((v) => v.label !== '查询单位')
          }
          form={form}
          isHideDate={true}
          initialValues={{
            compactDatePicker: [moment().format('YYYY-MM-DD'),moment().format('YYYY-MM-DD')]
          }}
        />
      </div>

      <div>
        <Tabs
          defaultActiveKey={tabsKey}
          style={{ paddingLeft: '16px' }}
          className="contractTab"
          onChange={(key) => onChangeKey(key)}
        >
          <TabPane tab="损益汇总" key={'plSum'}></TabPane>
          <TabPane tab="损益明细" key={'plDetail'}></TabPane>
        </Tabs>
      </div>
      <XlbTable
        tableKey={tabsKey}
        key={`${isFold?.toString()}_${tabsKey}`}
        columns={howShowList()}
        // keepDataSource={false}
        primaryKey="ssid"
        isLoading={isLoading}
        style={{ flex: 1, margin: '0 16px' }}
        // pagin={pagin}
        pageSize={pagin.pageSize}
        total={pagin?.total}
        dataSource={tabsKey === 'plSum' ? rowDataSum : rowDataDetail}
        isFold={isFold}
        onPaginChange={(page: number, pageSize: number) => {
          if (tabsKey === 'basket') return;
          const paginT = {
            total: pagin.total,
            pageNum: page,
            pageSize,
          };
          // setPagin(paginT);
          switch (tabsKey) {
            case 'plSum':
              setPaginSum(paginT);
              break;
            case 'plDetail':
              setPaginDetail(paginT);
              break;
          }
          getData(page, pageSize);
        }}
        onChangeSorts={(e) => {
          setSortType(e);
        }}
        footerDataSource={footerData}
      />
    </XlbPageContainer>
  );
};
export default Goodsway;