import { LStorage } from '@/utils';
import { XlbButton, XlbModal, XlbTable } from '@xlb/components';
import { useEffect, useState } from 'react';
import styles from '../index.less';
import { gethistory } from '../server';

export default function History(props: any) {
  const { visible, onCancel, id } = props;
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const companyId = LStorage.get('userInfo')?.company_id;
  const getHistory = async (page_number: any) => {
    setisLoading(true);
    const data = {
      company_id: companyId,
      operator: JSON.parse(localStorage.userInfo).value.account,
      store_id: id[0]?.store_id,
      item_id: id[0]?.item_id,
    };
    const res = await gethistory({ ...data });
    setisLoading(false);
    if (res.code === 0) {
      setRowData(res.data);
      setPagin({
        ...pagin,
        pageNum: page_number,
        total: res.data?.total_elements,
      });
    }
  };
  useEffect(() => {
    // itemArr.map((v: any) => tableRender(v))
    visible && getHistory(1);
  }, [visible]);

  return (
    <div>
      <XlbModal
        title={'修改记录'}
        visible={visible}
        width={750}
        onCancel={onCancel}
        isConfirm={false}
        footerExtroContent={[
          <XlbButton onClick={() => onCancel()} key={1}>
            确定
          </XlbButton>,
        ]}
      >
        <div className={rowData?.length ? styles.table_box : ''}>
          <XlbTable
            isLoading={isLoading}
            style={{
              height: 288,
            }}
            dataSource={rowData}
            columns={[
              {
                name: '序号',
                code: '_index',
                width: 60,
                align: 'center',
                lock: true,
              },
              {
                name: '操作人',
                code: 'create_by',
                width: 120,
                align: 'left',
                // features: { sortable: true },
              },
              {
                name: '操作时间',
                code: 'create_time',
                width: 160,
                align: 'left',
                features: { format: 'TIME' },
              },
              {
                name: '操作内容',
                code: 'content',
                width: 324,
                align: 'left',
                // features: { sortable: true },
              },
            ]}
            keepDataSource={true}
            pageSize={100}
            total={rowData?.length}
          ></XlbTable>
        </div>
      </XlbModal>
    </div>
  );
}