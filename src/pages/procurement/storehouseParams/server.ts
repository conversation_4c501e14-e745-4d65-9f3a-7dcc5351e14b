import { default as XlbFetch } from '@/utils/XlbFetch';

// 新增
export const addStoreSupplierConfig = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.storesupplierconfig.save', data);
};

// 修改
export const updateStoreSupplierConfig = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.storesupplierconfig.update', data);
};

// 删除
export const deleteStoreSupplierConfig = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.storesupplierconfig.batchDelete', data);
};
