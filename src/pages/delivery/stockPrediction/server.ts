import { XlbFetch as ErpRequest } from '@xlb/utils'

export const getTableSourceUrl = () => {
  return '/erp/hxl.erp.deliveryreport.prepareprediction.find'
}

/**
 * 导出
 */
export const exportOrder = async (data: any) => {
  return await ErpRequest.post<any>('/erp/hxl.erp.deliveryreport.prepareprediction.export', {
    data
  })
}
/**
 * 备货类型设置
 */
export const preparetypeFind = async (data: any) => {
  return await ErpRequest.post<any>('/erp/hxl.erp.preparetype.find', { data })
}
