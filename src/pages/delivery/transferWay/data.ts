import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const formList: SearchFormType[] = [
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    label: '调出门店',
    name: 'out_store_ids',
    type: 'inputDialog',
    dependencies: [],
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        center_flag: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '调入门店',
    name: 'store_ids',
    type: 'inputDialog',
    dependencies: [],
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        center_flag: false,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '载具名称',
    name: 'basket_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'basket',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
    },
  },
  {
    label: '调出组织',
    name: 'out_org_ids',
    type: 'select',
    selectRequestParams: (params: any, form: any) => {
        return {
          url: '/erp-mdm/hxl.erp.org.find',
          responseTrans(data) {
            return data?.map((item: any) => ({
                label: item.name,
                value: item.id,
              }));
          },
        };
    },
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: 100,
    features: { sortable: true },
  },

  {
    name: '门店编号',
    code: 'store_number',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'store_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '调出',
    code: 'deliver_goods',
    width: 500,
    align: 'center',
    children: [
      {
        title: '仓库确认数量',
        code: 'handle_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '司机取件数量',
        code: 'approve_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '留店数量',
        code: 'remain_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '留店率',
        code: 'rate',
        width: 120,
        align: 'right',
        features: { format: 'COMPARE', sortable: true },
      },
    ],
  },
  {
    name: '退货',
    code: 'return_goods',
    width: 500,
    align: 'center',
    children: [
      {
        title: '门店确认数量',
        code: 'out_handle_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '司机取件数量',
        code: 'out_approve_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '实收数量',
        code: 'out_remain_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '实收率',
        code: 'out_rate',
        width: 120,
        align: 'right',
        features: { format: 'COMPARE', sortable: true },
      },
    ],
  },
];

export const detailTableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: 100,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '门店编号',
    code: 'store_number',
    width: 90,
    features: { sortable: true },
  },
  {
    name: '收货门店',
    code: 'store_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '载具名称',
    code: 'basket_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '调出',
    code: 'deliver_goods',
    width: 500,
    align: 'center',
    children: [
      {
        title: '仓库确认数量',
        code: 'handle_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '司机取件数量',
        code: 'approve_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '留店数量',
        code: 'remain_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '留店率',
        code: 'rate',
        width: 120,
        align: 'right',
        features: { format: 'COMPARE', sortable: true },
      },
    ],
  },
  {
    name: '退货',
    code: 'return_goods',
    width: 500,
    align: 'center',
    children: [
      {
        title: '门店确认数量',
        code: 'out_handle_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '司机取件数量',
        code: 'out_approve_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '实收数量',
        code: 'out_remain_quantity',
        width: 120,
        align: 'right',
        features: { format: 'QUANTITY', sortable: true },
      },
      {
        title: '实收率',
        code: 'out_rate',
        width: 120,
        align: 'right',
        features: { format: 'COMPARE', sortable: true },
      },
    ],
  },
];
