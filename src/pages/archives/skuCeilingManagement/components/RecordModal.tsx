import { XlbModal, XlbPageContainer, XlbPopover } from '@xlb/components';
const Index = (props: {
  close: ((e: React.MouseEvent<HTMLButtonElement>) => void) | undefined;
  visible: boolean | undefined;
  recordParams: any;
}) => {
  const { Table } = XlbPageContainer;
  return (
    <>
      <XlbModal
        title="修改记录"
        width={800}
        onCancel={props.close}
        onOk={props.close}
        open={props.visible}
      >
        <XlbPageContainer
          tableColumn={[
            { code: '_index', name: '序号', width: 60 },
            {
              code: 'create_time',
              name: '修改人',
              width: 160,
              features: {
                format: 'TIME',
              },
            },
            { code: 'create_by', name: '修改人', width: 100 },
            {
              code: 'content',
              name: '操作内容',
              width: 340,
              render: (text) => {
                return (
                  <>
                    <XlbPopover
                      style={{ width: 200 }}
                      key={text}
                      content={<div style={{ maxWidth: '80vw' }}>{text}</div>}
                      trigger="hover"
                    >
                      <div>{text}</div>
                    </XlbPopover>
                  </>
                );
              },
            },
          ]}
          immediatePost={true}
          prevPost={(p) => {
            const { type, ...rest } = props.recordParams;
            return { ...p, ...rest };
          }}
          url={
            props.recordParams.type === '1'
              ? '/erp-mdm/hxl.erp.org.skulimit.log.find'
              : '/erp-mdm/hxl.erp.org.skulimit.excludeitem.log.find'
          }
        >
          <Table></Table>
        </XlbPageContainer>
      </XlbModal>
    </>
  );
};
export default Index;