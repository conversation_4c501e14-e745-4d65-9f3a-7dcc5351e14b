import type { XlbTableColumnProps } from '@xlb/components';
import { SearchFormType } from '@xlb/components';
import { message } from 'antd';
import { selectType } from './types';

export const queryUnit = [
  {
    label: '基本单位',
    value: 'BASIC',
  },
  {
    label: '配送单位',
    value: 'DELIVERY',
  },
  {
    label: '库存单位',
    value: 'STOCK',
  },
  {
    label: '批发单位',
    value: 'WHOLESALE',
  },
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
];

interface SummaryType extends selectType {
  code: string;
}

export const summaryTypes: SummaryType[] = [
  {
    label: '发货门店',
    value: 'DELIVERY_STORE',
    code: 'delivery_store_name',
  },
  {
    label: '统配门店',
    value: 'STORE',
    code: 'store_name',
  },
];

export const filterArr = [
  {
    label: '调出单',
    value: 'delivery_out_order',
  },
  {
    label: '批发销售单',
    value: 'wholesale_order',
  },
  {
    label: '前台销售',
    value: 'pos_order',
  },
];

export const formList: SearchFormType[] = [
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        status: true,
      },
      onOkBeforeFunction: (ids, selectData) => {
        if (ids?.length > 2) {
          message.error('门店最多仅支持选择2个!');
          return false;
        }
        return true;
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '仓库',
    name: 'storehouse_ids',
    type: 'select',
    multiple: true,
    clear: true,
    dependencies: ['store_ids'],
    // @ts-ignore
    selectRequestParams: (params: any, form: any) => {
      form?.setFieldValue('storehouse_ids', null);
      if (!!params?.store_ids?.length && params?.store_ids?.length === 1) {
        return {
          url: '/erp-mdm/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data) {
            return data
              .filter((v: any) => v.distribution)
              .map((item: any) => ({
                label: item.name,
                value: item.id,
                default_flag: item.default_flag,
              }));
          },
        };
      }
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
    clear: true,
    check: true,
  },
  {
    label: '商品部门',
    name: 'item_dept_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'productDept',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '查询单位',
    name: 'unit_type',
    allowClear: false,
    type: 'select',
    options: queryUnit,
  },
  {
    type: 'group',
    label: '库存数量',
    span: 1,
    formList: [
      {
        type: 'select',
        allowClear: false,
        name: 'stock_quantity_symbol',
        options: [
          { label: '>', value: '>' },
          { label: '<', value: '<' },
          { label: '=', value: '=' },
        ],
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'stock_quantity_ratio',
        min: 0,
        allowClear: false,
        placeholder: '',
      },
    ],
  },
  {
    type: 'group',
    label: '库存金额',
    span: 1,
    formList: [
      {
        type: 'select',
        allowClear: false,
        name: 'stock_money_symbol',
        options: [
          { label: '>', value: '>' },
          { label: '<', value: '<' },
          { label: '=', value: '=' },
        ],
        placeholder: '',
      },
      {
        type: 'inputNumber',
        name: 'stock_money_ratio',
        min: 0,
        allowClear: false,
        placeholder: '',
      },
    ],
  },
  {
    label: '商品库存',
    name: 'stock_type',
    type: 'select',
    options: [
      { value: 'MAX', label: '高库存' },
      { value: 'MIN', label: '低库存' },
      {
        value: 'MAX_MIN',
        label: '高库存+低库存',
      },
    ],
  },
  {
    type: 'inputNumber',
    label: '可用天数',
    name: 'valid_days_ratio',
    prefix: '>',
    suffix: '天',
    colon: false,
    min: 0,
    allowClear: false,
    placeholder: '',
  },
  {
    label: '统计',
    type: 'inputNumber',
    name: 'sale_day',
    colon: false,
    min: 0,
    allowClear: false,
    suffix: '天内日均销量',
    placeholder: '',
  },
  {
    label: '',
    name: 'checkValue',
    type: 'checkbox',
    customWidth: 400,
    clear: true,
    colon: false,
    options: filterArr,
  },
];

const columnWidthEnum = {
  tel: 120,
  fid: 160,
  TIME: 120,
  DATE: 100,
  ITEM_CODE: 124,
  ITEM_BAR_CODE: 124,
  SHORTHAND_CODE: 110,
  STORE_NAME: 140,
  MEMO: 140,
  STOP_SALE: 90,
  ORDER_STATE: 90,
  INDEX: 50,
  ITEM_SPEC: 110,
  BY: 110,
  ORDER_FID: 140,
};

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true }
  },
  {
    name: '单位',
    code: 'unit',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '保质期',
    code: 'period',
    width: 90,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true }
  },
  {
    name: '供应商',
    code: 'supplier_names',
    width: 280,
    features: { sortable: true }
  },
  {
    name: '基础库存',
    code: 'base_stock_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '库存上限',
    code: 'upper_limit',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '库存数量',
    code: 'stock_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '日均销量',
    code: 'avg_sale_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '预计可用天数',
    code: 'valid_days',
    width: 114,
    align: "right",
    features: { sortable: true }
  },
  {
    name: '库存单价',
    code: 'stock_price',
    width: 100,
    align: "right",
    features: { sortable: true }
  },
  {
    name: '库存金额',
    code: 'stock_money',
    width: 100,
    align: "right",
    features: { sortable: true }
  }, {
    name: '停购',
    code: 'stop_purchase',
    width: 80,
    features: { sortable: true }
  }, {
    name: '最近出库日期',
    code: 'latest_out_date',
    width: 120,
    features: { sortable: true }
  },
  {
    name: '最近入库日期',
    code: 'latest_in_date',
    width: 120,
    features: { sortable: true }
  },
]