export const OrganizeManageKeyMap = {
  /**批发客户 inputDialog 单选 */
  erpWholesalerId: 'erpWholesalerId',
  erpKingdeeCode: 'erpKingdeeCode',
};

export const organizeManageConfig: any[] = [
  {
    tag: 'ERP',
    label: '批发客户',
    id: OrganizeManageKeyMap?.erpWholesalerId,
    name: 'wholesaler_id',
    fieldProps: {
      dialogParams: {
        type: 'wholesaler',
        dataType: 'lists',
        isMultiple: false,
      },
    },
    formItemProps: {
      getValueFromEvent: (value: any) => {
        if (!Array.isArray(value)) return;
        return value[0];
      },
    },
    componentType: 'inputDialog',
  },
  {
    tag: 'ERP',
    componentType: 'input',
    label: '金蝶编码',
    name: 'kingdee_code',
    id: OrganizeManageKeyMap?.erpKingdeeCode,
    fieldProps: {
      maxLength: 30,
    },
  },
];
