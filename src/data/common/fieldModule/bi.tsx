export const BIKeyMap = {
  /**@name 门店 inputDialog 多选 */
  biStoreIds: 'biStoreIds'
}

export const BIConfig: any[] = [
  {
    tag: 'BI',
    label: '门店',
    id: BIKeyMap?.biStoreIds,
    name: 'store_ids',
    dependencies: ['org_ids', 'summary_types'],
    fieldProps: (form) => {
      console.log('门店----', form.getFieldsValue(['org_ids', 'summary_types']))
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...form.getFieldsValue(['org_ids', 'summary_types'])
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  }
]
