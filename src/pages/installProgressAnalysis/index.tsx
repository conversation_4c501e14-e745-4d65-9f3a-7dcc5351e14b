import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth, LStorage } from '@/utils';
import {
  XlbButton,
  XlbProPageContainer,
  XlbProPageModal,
} from '@xlb/components';
import dayjs from 'dayjs';
import { Fragment, useRef } from 'react';
import { summaryType, tableColumn } from './data';

const InstallProgressAnalysis = () => {
  const realitionRef = useRef<any>(null);
  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        initialValues: {
          summary_types: 'STORE',
          date: [dayjs()?.format('YYYY-MM-DD'), dayjs()?.format('YYYY-MM-DD')],
          company_id: LStorage.get('userInfo').company_id,
          company_name: LStorage.get('userInfo').company_name,
        },
        formList: [
          {
            id: 'dateCommon',
            label: '日期范围',
            name: 'date',
            format: 'YYYY-MM-DD',
          },
          {
            id: ErpFieldKeyMap.companyName,
            label: '公司',
            disabled: true,
          },
          {
            id: ErpFieldKeyMap?.erpStoreIds,
            label: '门店',
          },
          {
            id: ErpFieldKeyMap.erpSummerType,
            label: '汇总条件',
            name: 'summary_types',
            rules: [{ required: true, message: '请先选择汇总条件' }],
            required: false,
            fieldProps: {
              options: summaryType,
            },
          },
          {
            id: ErpFieldKeyMap.cityArea,
            label: '省市区',
            name: 'city_codes',
          },
          { id: ErpFieldKeyMap.businessArea, label: '业务区域' },
        ],
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.alipaymetricsinstall.page',
        tableColumn: (formValues: any) => {
          const summary_types = formValues?.summary_types;
          return tableColumn(summary_types);
        },
        selectMode: 'single',
        // keepDataSource: false,
        showColumnsSetting: false,
        changeColumnAndResetDataSource: false,
      }}
      // 导出
      exportFieldProps={{
        url: hasAuth(['安装进度分析', '导出'])
          ? '/erp/hxl.erp.alipaymetricsinstall.export'
          : '',
        fileName: '安装进度分析.xlsx',
      }}
      extra={() => {
        return (
          <Fragment>
            <div
              onClick={() => {
                realitionRef.current?.open();
              }}
            >
              触发XlbProPageModal
            </div>
            <XlbProPageModal
              ref={realitionRef}
              Content={({ onClose }) => (
                <div style={{ padding: '20px' }}>
                  详情页面<XlbButton onClick={onClose}>返回</XlbButton>
                </div>
              )}
              // onClose={onClose}
            >
              <span className="link">
                <XlbButton>进入详情页面</XlbButton>
              </span>
            </XlbProPageModal>
          </Fragment>
        );
      }}
    />
  );
};

export default InstallProgressAnalysis;
