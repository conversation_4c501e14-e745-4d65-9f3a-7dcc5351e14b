export const MoneyApplyKeyMap = {
  verifyDate: 'verifyDate',
  // 对账门店
  erpMoneyApplyStoreName: 'erpMoneyApplyStoreName',
  // 日期范围
  erpDateRange: 'erpDateRange'
}

export const moneyApplyConfig: any[] = [
  {
    tag: 'ERP',
    label: '核销日期',
    id: MoneyApplyKeyMap.verifyDate,
    name: 'verify_date',
    componentType: 'datePicker',
    fieldProps: {
      width: 180,
      picker: 'date',
      format: 'YYYY-MM-DD'
    }
  },
  // {
  //   tag: 'ERP',
  //   label: '对账门店',
  //   id: MoneyApplyKeyMap?.erpMoneyApplyStoreName,
  //   dependencies: ['supplier_id', 'settlement_model'],
  //   name: 'moneyApply_store_id',
  //   fieldProps: (form) => {
  //     return {
  //       dialogParams: {
  //         type: 'store',
  //         dataType: 'lists',
  //         isMultiple: false,
  //         data: {
  //           status: true,
  //           supplier_id: form.getFieldValue('supplier_id'),
  //           query_center: form.getFieldValue('settlement_model') == 2 ? true : null
  //         }
  //       },
  //       fieldNames: {
  //         idKey: 'id',
  //         nameKey: 'store_name'
  //       }
  //     }
  //   },
  //   formItemProps: {
  //     getValueFromEvent: (value: any) => {
  //       if (!Array.isArray(value)) return
  //       return value[0]
  //     }
  //   },
  //   componentType: 'inputDialog'
  // },
  {
    tag: 'ERP',
    label: '业务日期',
    id: MoneyApplyKeyMap.erpDateRange,
    name: 'date_range',
    componentType: 'rangePicker'
  }
]
