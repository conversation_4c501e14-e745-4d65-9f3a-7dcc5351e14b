import { hasAuth } from '@/utils/kit';
import { useIRouter, wujieBus } from '@/wujie/utils';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbMessage,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { FC, useEffect, useRef, useState } from 'react';
import { DETAIL_FORM_LIST, detailTableColumn, MustSellGoodState } from './data';
import { aduitGoods, exportGoodsDetail, readuitGoods } from './server';
import { useStore } from './store';

const Index: FC = () => {
  const { navigate, goBack } = useIRouter();
  // form
  const [form] = XlbBasicForm.useForm();
  const { record, setIsFresh } = useStore();

  // table format
  const prevPost = () => {
    if (!record) {
      return false;
    }
    return {
      ...form.getFieldsValue(true),
      fid: record?.fid,
    };
  };

  // operate
  const [exportLoading, setExportLoading] = useState(false);
  const exportItem = async (e: any, requestForm: any) => {
    setExportLoading(true);
    const res = await exportGoodsDetail(requestForm);
    setExportLoading(false);
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
  };
  const importItem = (fetchData: any) => {
    XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp/hxl.erp.mustsellitemassortment.detail.import`,
      templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.mustsellitemassortment.detail.template.download`,
      templateName: '必卖品详情导入模板',
      params: { fid: record?.fid },
      callback: (res) => {
        if (res.code === 0) {
          fetchData?.();
        }
      },
    });
  };
  const operateItem = (type: string) => {
    XlbTipsModal({
      title: '提示',
      tips: `是否${type}`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res =
          type === '审核'
            ? await aduitGoods({ fid: record?.fid })
            : await readuitGoods({ fid: record?.fid });
        if (res?.code === 0) {
          setIsFresh(true);
          goBack();
          return true;
        }
        return false;
      },
    });
  };

  const fetchDataRef = useRef<any>();
  useEffect(() => {
    fetchDataRef.current?.();
    if (!record) {
      navigate('/xlb_erp/mustSellGoodsManagement/index');
    }
  }, [record]);

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.mustsellitemassortment.inner.detail.page'}
      tableColumn={detailTableColumn}
      immediatePost
      prevPost={prevPost}
    >
      <XlbPageContainer.ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource,
          requestForm,
        }: ContextState<any>) => {
          fetchDataRef.current = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['必卖品货盘管理', '查询']) && (
                <XlbButton
                  label="查询"
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<XlbIcon name="sousuo" />}
                />
              )}
              {hasAuth(['必卖品货盘管理', '审核']) && (
                <XlbButton
                  label="审核"
                  type="primary"
                  disabled={record?.state !== MustSellGoodState.Audit}
                  icon={<XlbIcon name="shenhe" />}
                  onClick={() => operateItem('审核')}
                />
              )}
              {hasAuth(['必卖品货盘管理', '反审核']) && (
                <XlbButton
                  label="反审核"
                  type="primary"
                  disabled={record?.state !== MustSellGoodState.Pass}
                  icon={<XlbIcon name="fanshenhe" />}
                  onClick={() => operateItem('反审核')}
                />
              )}
              {hasAuth(['必卖品货盘管理', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  disabled={!dataSource?.length}
                  loading={exportLoading}
                  icon={<XlbIcon name="daochu" />}
                  onClick={(e) => exportItem(e, requestForm)}
                />
              )}
              {hasAuth(['必卖品货盘管理', '导入']) && (
                <XlbButton
                  label="导入"
                  type="primary"
                  loading={exportLoading}
                  icon={<XlbIcon name="daoru" />}
                  onClick={() => importItem(fetchData)}
                />
              )}
              <XlbButton
                label="返回"
                type="primary"
                loading={exportLoading}
                icon={<XlbIcon name="fanhui" />}
                onClick={() => goBack()}
              />
            </XlbButton.Group>
          );
        }}
      </XlbPageContainer.ToolBtn>

      <XlbPageContainer.SearchForm>
        <XlbForm form={form} formList={DETAIL_FORM_LIST} isHideDate />
      </XlbPageContainer.SearchForm>

      <XlbPageContainer.Table key="fid" primaryKey="fid" selectMode="single" />
    </XlbPageContainer>
  );
};
export default Index;
