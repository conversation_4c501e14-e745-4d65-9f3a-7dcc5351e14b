import { useBaseParams } from '@/hooks/useBaseParams';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import { XlbBasicForm, XlbButton, XlbForm, XlbIcon } from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { message } from 'antd';
import { useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import {
  detailTableColumn,
  detailUrl,
  searchFormList,
  summaryTableColumn,
  summaryUrl,
} from './data';
import Api from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const StockDyingPeriod = () => {
  const pageRef = useRef<XlbPageContainerRef>(null);
  const { enable_cargo_owner } = useBaseParams((state) => state);
  // const enable_cargo_owner = true;

  const [form] = XlbBasicForm.useForm();
  const [tableColumn, setTableColunm] = useState(
    summaryTableColumn.filter((el) => el.name !== '货主' || enable_cargo_owner),
  );
  const [requestUrl, setRequestUrl] = useState(summaryUrl);
  const mode = XlbBasicForm.useWatch('mode', form);
  const categoryLevel = XlbBasicForm.useWatch('category_level', form);

  const prevPost = () => {
    const formData = form.getFieldsValue();
    if (formData.category_level === 'empty') {
      formData.category_level = undefined;
    }
    return {
      ...formData,
      ...(formData.storehouse_ids && {
        storehouse_ids: [formData.storehouse_ids],
      }),
      query_expired_item: !!formData.query_expired_item?.length,
    };
  };
  // 选择门店后查询仓库信息
  const onValuesChange = async (changeValues: any, _values: any) => {
    console.log('changeValues: ', changeValues);
    if (changeValues.store_ids) {
      form.setFieldValue('storehouse_ids', undefined);
      if (changeValues.store_ids.length === 1) {
        const res = await Api.getStoreHouseList({
          store_ids: changeValues.store_ids,
        });
        if (res.code === 0) {
          const obj = res.data.content.map((v: any) => ({
            label: v.name,
            value: v.id,
          }));
          if (changeValues.store_ids.length > 1) {
            form.setFieldValue('storehouse_ids_options', []);
          } else {
            form.setFieldValue('storehouse_ids_options', obj);
          }
        }
      } else {
        form.setFieldValue('storehouse_ids_options', []);
      }
    }
  };
  const resetTableColunm = () => {
    let originColunm = summaryTableColumn;
    if (mode === 'detail') {
      originColunm = detailTableColumn;
    }
    if (categoryLevel === 1) {
      originColunm = originColunm.filter(
        (el) => el.name !== '二级分类' && el.name !== '三级分类',
      );
    } else if (categoryLevel === 2) {
      originColunm = originColunm.filter((el) => el.name !== '三级分类');
    }
    originColunm = originColunm.filter(
      (el) => el.name !== '货主' || enable_cargo_owner,
    );
    setTableColunm(originColunm);
  };
  const setUrl = () => {
    if (mode === 'detail') {
      setRequestUrl(detailUrl);
    } else {
      setRequestUrl(summaryUrl);
    }
  };
  const queryData = () => {
    const expire_value = form.getFieldValue('expire_value');
    if (!expire_value) {
      message.error('请填写临期条件数值');
      return;
    }
    flushSync(() => {
      setUrl();
      resetTableColunm();
    });
    pageRef.current?.fetchData();
  };
  const exportItem = async (setIsLoading: any, requestForm: any, e) => {
    setIsLoading(true);
    let res = null;
    if (mode === 'detail') {
      res = await Api.exportDetail({
        ...prevPost(),
        // responseType: 'blob'
      });
    } else {
      res = await Api.exportSummary({
        ...prevPost(),
        // responseType: 'blob'
      });
    }
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', {
        code: 'downloadEnd',
        target: e,
      });
      message.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };
  return (
    <XlbPageContainer
      ref={pageRef}
      url={requestUrl}
      tableColumn={tableColumn}
      immediatePost
      prevPost={prevPost}
    >
      <SearchForm>
        <XlbForm
          form={form}
          formList={searchFormList.filter(
            (v) => v.label !== '货主' || enable_cargo_owner,
          )}
          initialValues={{
            // store_ids_name: [
            //   {
            //     store_name: LStorage.get('userInfo').store_name,
            //     id: LStorage.get('userInfo').store_id
            //   }
            // ],
            store_ids: [LStorage.get('userInfo')?.store_id],
            expire_value: 33,
            expire_type: '1',
            mode: 'summary',
            unit_type: 'PURCHASE',
          }}
          isHideDate
          onValuesChange={onValuesChange}
        />
      </SearchForm>
      <ToolBtn>
        {({ dataSource, loading, setLoading, requestForm }) => {
          return (
            <XlbButton.Group>
              <XlbButton
                type="primary"
                onClick={() => queryData()}
                icon={<XlbIcon name="sousuo" />}
              >
                查询
              </XlbButton>
              <XlbButton
                type="primary"
                label="导出"
                disabled={!dataSource?.length || loading}
                onClick={(e) => exportItem(setLoading, requestForm, e)}
                icon={<XlbIcon name="daochu" />}
              />
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table />
    </XlbPageContainer>
  );
};

export default StockDyingPeriod;
