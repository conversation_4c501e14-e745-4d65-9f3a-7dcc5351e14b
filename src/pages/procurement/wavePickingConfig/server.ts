import { default as XlbFetch } from '@/utils/XlbFetch';

// 新增
interface SaveParams {
  store_id: number;
  config: { times: number[] };
}
export const saveWavePickingConfig = async (data: SaveParams) => {
  return await XlbFetch('/erp/hxl.erp.wavepickingconfig.save', data);
};

// 修改
interface UpdateParams extends SaveParams {
  id: number;
  enable: number;
}
export const updateWavePickingConfig = async (data: UpdateParams) => {
  return await XlbFetch('/erp/hxl.erp.wavepickingconfig.update', data);
};

// 供应商--新增
export const saveWavePickingDetailConfig = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.wavepickingconfigdetail.save', data);
};

// 供应商--修改
export const updateWavePickingDetailConfig = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.wavepickingconfigdetail.update', data);
};
