import { XlbDetailProps } from '@xlb/components';

export const contractStatusList: any[] = [
  {
    label: '待签署',
    value: 'PENDING_SIGN',
  },
  {
    label: '部分签署',
    value: 'PARTIALLY_SIGNED',
  },
  // {
  //   label: '待生效',
  //   value: 'PENDING_EFFECT',
  // },
  {
    label: '已生效',
    value: 'ALL',
  },
  {
    label: '已拒签',
    value: 'REJECT',
  },
  {
    label: '已撤回',
    value: 'CANCEL',
  },
  {
    label: '已解除',
    value: 'RELIEVED',
  },
  {
    label: '已失效',
    value: 'INVALID',
  },
  {
    label: '异常',
    value: 'EXCEPTION',
  },
];

export const contractTypes = {
  FUND_SPLIT_WITHHOLD: '资金分账代扣',
  FUND_TRANSFER: '资金转款',
  SALES_REBATE: '营业返款',
};

export const contractTypes_list = [
  {
    label: '资金分账代扣',
    value: 'FUND_SPLIT_WITHHOLD',
  },
  {
    label: '资金转款',
    value: 'FUND_TRANSFER',
  },
];
export const formListPre = [
  {
    label: '合同状态',
    name: 'contract_status_list',
    type: 'select',
    check: true,
    multiple: true,
    allowClear: true,
    options: contractStatusList,
  },
  {
    label: '合同模板名称',
    name: 'contract_template_name',
    type: 'input',
    check: true,
    allowClear: true,
  },
];
export const formList: any[] = [
  {
    label: '关键字',
    name: 'keyword',
    type: 'input',
    allowClear: true,
    check: true,
  },
  {
    label: '合同类型',
    width: 270,
    name: 'contract_type_enums',
    placeholder: '请选择',
    multiple: true,
    type: 'select',
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.contract.enum',
      postParams: { type: 'CONTRACT_TYPE', s: 2 },
      responseTrans(data: { data: any[] }) {
        const options = data.data.map((item: any) => {
          const obj = {
            label: item.name,
            value: item.code,
          };
          return obj;
        });
        return options;
      },
    },
    allowClear: true,
    check: true,
  },
  {
    label: '关联业务单据', // CONTRACT_MODULE
    name: 'module',
    width: 270,
    type: 'select',
    placeholder: '请选择',
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.contract.enum',
      postParams: { type: 'CONTRACT_MODULE', s: 2 },
      responseTrans(data: { data: any[] }) {
        const options = data.data.map((item: any) => {
          const obj = {
            label: item.name,
            value: item.code,
          };
          return obj;
        });
        return options;
      },
    },
    fieldProps: {
      label: 'name',
      value: 'code',
      mode: 'multiple',
      allowClear: true,
      fieldNames: {
        label: 'name',
        value: 'code',
      },
    },
    allowClear: true,
    check: true,
  },
];
export const signFormList: any[] = [
  {
    label: '合同模版名称',
    name: 'template_id',
    type: 'select',
    width: 270,
    placeholder: '请选择',
    rules: [{ required: true, message: '合同模版名称不能为空' }],
    check: true,
    //
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.contract.template.page',
      postParams: {},
      responseTrans(data: { data: any[] }) {
        const options = data?.content.map((item: any) => {
          const obj = {
            label: item.name,
            value: item.id,
          };
          return obj;
        });
        return options;
      },
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true,
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    multiple: false,
    allowClear: true,
    options: [],
  },
  {
    label: '合同类型',
    width: 270,
    name: 'contract_type',
    placeholder: '请选择',

    rules: [{ required: true, message: '合同类型不能为空' }],
    type: 'select',
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.contract.enum',
      postParams: { type: 'CONTRACT_TYPE', s: 2 },
      responseTrans(data: { data: any[] }) {
        const options = data.data.map((item: any) => {
          const obj = {
            label: item.name,
            value: item.code,
          };
          return obj;
        });
        return options;
      },
    },
    allowClear: true,
    check: true,
  },
  {
    label: '关联业务单据', // CONTRACT_MODULE
    name: 'module',
    width: 270,
    type: 'select',
    placeholder: '请选择',
    rules: [{ required: true, message: '关联业务单据不能为空' }],
    selectRequestParams: {
      url: '/erp-mdm/hxl.erp.contract.enum',
      postParams: { type: 'CONTRACT_MODULE', s: 2 },
      responseTrans(data: { data: any[] }) {
        const options = data.data.map((item: any) => {
          const obj = {
            label: item.name,
            value: item.code,
          };
          return obj;
        });
        return options;
      },
    },
    fieldProps: {
      label: 'name',
      value: 'code',
      mode: 'multiple',
      allowClear: true,
      fieldNames: {
        label: 'name',
        value: 'code',
      },
    },
    allowClear: true,
    check: true,
  },
  // *关联业务单据
];

export const signFormList2: any[] = [
  {
    label: '供应商',
    name: 'supplier_ids',
    width: 270,
    type: 'inputDialog',
    placeholder: '请选择',
    rules: [{ required: true, message: '供应商不能为空' }],
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isMultiple: true,
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    width: 270,
    rules: [{ required: true, message: '门店不能为空' }],
    type: 'inputDialog',
    placeholder: '请选择',
    allowClear: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      data: {
        enable_organization: false,
        status: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  // *关联业务单据
];
export const moduleList = {
  STORE_ORDER: '门店订单',
  TOBACCO_REBATE: '烟草返款',
  SALES_REBATE: '营业返款',
  CARD_REBATE: '卡返款',
};

export const tableList: any[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '合同编号',
    code: 'contract_code',
    align: 'left',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '合同类型',
    code: 'contract_type',
    align: 'left',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '合同名称',
    code: 'contract_name',
    align: 'left',
    width: 250,
    features: { sortable: true },
  },
  {
    name: '合同状态',
    code: 'contract_status',
    align: 'left',
    width: 116,
    features: { sortable: true },
  },
  {
    name: '签署方',
    code: 'sign_info',
    align: 'left',
    width: 400,
    features: { sortable: true },
  },
  {
    name: '关联业务单据',
    code: 'module',
    align: 'left',
    width: 162,
    features: { sortable: true },
  },
  {
    name: '合同创建时间',
    code: 'created_at',
    align: 'left',
    width: 162,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '合同生效时间',
    code: 'effect_time',
    width: 162,
    align: 'left',
    features: { sortable: true, format: 'TIME' },
  },
  // {
  //   name: '合同终止日期',
  //   code: 'item_name',
  //   width: 162,
  //   align: 'left',
  //   features: { sortable: true, format: 'TIME' },
  // },
  {
    name: '操作',
    code: 'action',
    width: 150,
    align: 'left',
    features: { sortable: true },
  },
];
export const tableList2: any[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '合同模板名称',
    code: 'contract_template_name',
    align: 'left',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '合同类型',
    code: 'contract_type',
    align: 'left',
    width: 150,
    features: { sortable: true },
  },
  {
    name: '关联业务单据',
    code: 'module',
    align: 'left',
    width: 162,
    features: { sortable: true },
  },
  {
    name: '签署方',
    code: 'sign_info',
    align: 'left',
    width: 400,
    features: { sortable: true },
  },

  {
    name: '操作时间',
    code: 'operate_time',
    align: 'left',
    width: 162,
    features: { sortable: true, format: 'TIME' },
  },
  {
    name: '操作人',
    code: 'operator',
    align: 'left',
    width: 162,
    features: { sortable: true, format: 'TIME' },
  },
];
export const schema1: XlbDetailProps['schema'] = [
  {
    title: '基本信息',
    formList: [
      {
        label: '合同编号',
        name: 'contract_code',
        style: {
          width: '35%',
        },
      },
      {
        label: '合同名称',
        name: 'contract_name',
        style: {
          width: '35%',
        },
      },
      {
        label: '合同类型',
        name: 'contract_type',
        style: {
          width: '35%',
        },
        render: (value) => {
          return <div>{contractTypes[`${value}`]}</div>;
        },
      },
      {
        label: '合同状态',
        name: 'contract_status',
        style: {
          width: '35%',
        },
        render: (value) => {
          const curItem = contractStatusList.find((y) => y.value === value);
          return <div>{curItem?.label}</div>;
        },
      },
    ],
  },
];
export const schema3: XlbDetailProps['schema'] = [
  {
    title: '其他信息',
    formList: [
      {
        label: '合同创建人',
        name: 'created_by',
        style: {
          width: '35%',
        },
      },
      {
        label: '合同创建日期',
        name: 'created_at',
        style: {
          width: '35%',
        },
      },
      {
        label: '合同生效日期',
        name: 'effect_time',
        style: {
          width: '35%',
        },
        render: (value) => {
          return <div>{value}</div>;
        },
      },

      {
        label: '查看合同附件',
        name: 'contract_file_url',
        type: 'upload',
        accept: 'pdf',
        // listType: 'picture-card',
        uploadName: '',
        style: {
          width: '35%',
        },
      },
      // {
      //   label: '合同终止日期',
      //   name: 'status',
      //   style: {
      //     width: '35%',
      //   },
      // },
    ],
  },
];
export const contract_signs_obj = {
  STORE: '门店',
  SUPPLIER: '供应商',
  WANCHEN: '公司',
};
