import {XlbFetch as ErpRequest } from '@xlb/utils'

// 获取商品汇总数据
export  const getgoods = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryreport.receiverateanalysis.item.page', data)
}

// 获取门店汇总
export  const getstoretotal = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryreport.receiverateanalysis.store.page', data)
}

// 获取门店-商品
export  const getstoregoods = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.deliveryreport.receiverateanalysis.detail.page', data)
}

// 获取商品标签
export  const getItemLabelRequest = async (data: any) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.itemlabel.find', { data })
}