import { LStorage } from '@/utils';
import { useModal } from '@ebay/nice-modal-react';
import { XlbModal, XlbTable } from '@xlb/components';
import { useEffect, useState } from 'react';
import { getHistory } from '../../server';

const History = (props: any) => {
  const { id, unitType } = props;
  const modal = useModal();
  const [rowData, setRowData] = useState<any[]>([]);
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });

  const getHistoryRecord = async (page_number: any) => {
    const data = {
      company_id: 1000,
      operator: LStorage.get('userInfo').account,
      client_id: id[0].client_id,
      store_id: id[0].store_id,
      unit_type: unitType,
      item_id: id[0].item_id,
    };
    const res = await getHistory(data);
    if (res.code === 0) {
      setRowData(res.data);
      setPagin({
        ...pagin,
        pageNum: page_number,
        total: res.data?.total_elements,
      });
    }
  };
  useEffect(() => {
    getHistoryRecord(1);
  }, []);

  return (
    <XlbModal
      title={'修改记录'}
      open={modal.visible}
      width={800}
      keyboard={false}
      isCancel={false}
      onCancel={() => {
        modal.hide();
        modal.resolve(false);
      }}
      onOk={() => {
        modal.hide();
        modal.resolve(true);
      }}
    >
      <div style={{ margin: '10px 0' }}>
        <XlbTable
          style={{
            height: 288,
          }}
          dataSource={rowData}
          columns={[
            {
              name: '序号',
              code: '_index',
              width: 60,
              align: 'center',
              lock: true,
            },
            {
              name: '修改时间',
              code: 'create_time',
              width: 130,
              align: 'left',
              features: { format: 'TIME' },
            },
            {
              name: '修改人',
              code: 'create_by',
              width: 120,
              align: 'left',
            },
            {
              name: '单位',
              code: 'unit',
              width: 100,
              align: 'left',
            },
            {
              name: '操作明细',
              code: 'memo',
              width: 324,
              align: 'left',
            },
          ]}
          keepDataSource={true}
          pageSize={100}
          total={rowData?.length}
        ></XlbTable>
      </div>
    </XlbModal>
  );
};
export default History;
