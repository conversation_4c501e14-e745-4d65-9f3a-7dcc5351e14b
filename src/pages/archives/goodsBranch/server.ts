import {XlbFetch as ErpRequest } from '@xlb/utils'
import { LStorage } from '@/utils/storage'

export const CACHE_BRANCH_UPDATE = 'CACHE_BRANCH_UPDATE'
export const CACHE_BRANCH_DATA = 'CACHE_BRANCH_DATA'
// 获取数据
export const getBranchsAll = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.dept.find', { data })
}

// 新增品牌
export const addNewBranch = async (data: any) => {
  LStorage.set(CACHE_BRANCH_UPDATE, 'true')
  return await ErpRequest.post('/erp-mdm/hxl.erp.dept.save', { data })
}

// 删除品牌
export const deleteBranchs = async (data: any) => {
  LStorage.set(CACHE_BRANCH_UPDATE, 'true')
  return await ErpRequest.post('/erp-mdm/hxl.erp.dept.delete', { data })
}

// 修改品牌
export const editBranch = async (data: any) => {
  LStorage.set(CACHE_BRANCH_UPDATE, 'true')
  return await ErpRequest.post('/erp-mdm/hxl.erp.dept.update', { data })
}