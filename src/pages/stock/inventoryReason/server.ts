import {XlbFetch as ErpRequest } from '@xlb/utils'


// 新增
export const addstockadjustmentreason = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockadjustmentreason.save', data)
}

// 删除
export const deletestockadjustmentreason = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockadjustmentreason.delete', data)
}

// 修改
export const editstockadjustmentreason = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockadjustmentreason.update', data)
}
// 批量修改
export const editstockadjustmentreasonBatch = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockadjustmentreason.batchupdate', data)
}

// 获取list
export const getReason = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockadjustmentreason.find', data)
}
