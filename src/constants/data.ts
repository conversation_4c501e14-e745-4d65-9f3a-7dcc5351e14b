export const cargoList = [
  {
    label: '组织',
    value: 'ORGANIZATION',
  },
  {
    label: '供应商',
    value: 'SUPPLIER',
  },
];

// 日期范围
export const DateRange: any[] = [
  {
    label: '今天',
    value: 0,
  },
  {
    label: '昨天',
    value: 1,
  },
  {
    label: '本周',
    value: 2,
  },
  {
    label: '上周',
    value: 3,
  },
  {
    label: '本月',
    value: 4,
  },
  {
    label: '上月',
    value: 5,
  },
  {
    label: '本季',
    value: 6,
  },
  {
    label: '今年',
    value: 7,
  },
];

export const itemProperty = [
  {
    label: '正常',
    value: 'normal',
  },
  {
    label: '停购',
    value: 'stop_purchase',
  },
  {
    label: '停售',
    value: 'stop_sale',
  },
  {
    label: '停止要货',
    value: 'stop_request',
  },
  {
    label: '停止批发',
    value: 'stop_wholesale',
  },
];

export const accountType = [
  {
    label: '票折',
    value: 'INVOICE',
  },
  {
    label: '非票折',
    value: 'NOT_INV',
  },
];

export const userDept: any = {
  STORE: '门店',
  SUPPLY_CENTER: '商品中心',
  STOCK_CENTER: '仓储中心',
  LOGISTICS_CENTER: '物流中心',
  FINANCE_CENTER: '财务中心',
  ADMIN_CENTER: '行政中心',
  EXPAND_DEPT: '拓展中心',
  INFORMATION_DEPT: '信息中心',
  OPERATE_CENTER: '运营中心',
  MARKET_CENTER: '市场中心',
  REGION: '大区',
  PROJECT_CENTER: '工程中心',
  BRAND_CENTER: '品牌中心',
  PURCHASE_CENTER: '采购中心',
  QUALITY_CENTER: '品控中心',
};

// 表格样式简单配置
export const tableStyle = {
  '--header-row-height': '28px',
  '--row-height': '24px',
  '--header-bgcolor': '#fafafa',
  '--header-color': '#666',
  '--row-color': '#000',
  '--cell-padding': '0',
  '--highlight-bgcolor': '#E8F1FF',
  fontSize: '14px',
};
