export const SaleAnalysisKeyMap = {
  erpSummary_Types: 'erpSummary_Types',
  erpMemberAndCircleRate: 'erpMemberAndCircleRate'
}

export const saleAnalysisConfig: any[] = [
  {
    tag: 'ERP',
    label: '汇总条件',
    id: SaleAnalysisKeyMap.erpSummary_Types,
    name: 'summary_types',
    componentType: 'select',
    dependencies: ['enable_organization'],
    request: (obj: any) => {
      if (obj.enable_organization) {
        return [
          {
            label: '二级组织',
            value: 'ORGANIZATION_LV2'
          },
          {
            label: '三级组织',
            value: 'ORGANIZATION_LV3'
          },
          {
            label: '门店',
            value: 'STORE'
          },
          {
            label: '营业日',
            value: 'BIZDAY'
          },
          {
            label: '省',
            value: 'PROVINCE'
          },
          {
            label: '市',
            value: 'CITY'
          },
          {
            label: '区/县',
            value: 'DISTRICT'
          },
          {
            label: '配送类型',
            value: 'STORE_TYPE'
          }
        ]
      }
      return [
        {
          label: '门店',
          value: 'STORE'
        },
        {
          label: '营业日',
          value: 'BIZDAY'
        },
        {
          label: '省',
          value: 'PROVINCE'
        },
        {
          label: '市',
          value: 'CITY'
        },
        {
          label: '区/县',
          value: 'DISTRICT'
        },
        {
          label: '配送类型',
          value: 'STORE_TYPE'
        }
      ]
    },
    fieldProps: {
      options: [],
      mode: 'multiple'
    }
  },
  {
    tag: 'ERP',
    label: '环比',
    id: SaleAnalysisKeyMap.erpMemberAndCircleRate,
    componentType: 'checkbox',
    fieldProps: {
      options: [
        { label: '会员', value: 'member' },
        { label: '环比', value: 'compare' }
      ],
      colon: false
    },
    formItemProps: {
      label: ' ',
      colon: false
    },
    group: false,
    colon: false
  }
]
