import {XlbFetch as ErpRequest } from '@xlb/utils'

export default class Api {
  //新增
  static addItem = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.ordertimerule.save', data)
  }
  //更新
  static updateItem = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.ordertimerule.update', data)
  }
  //删除
  static delItem = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.ordertimerule.batchdelete', data)
  }
  //读取
  static readItem = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.ordertimerule.read', data)
  }
}
