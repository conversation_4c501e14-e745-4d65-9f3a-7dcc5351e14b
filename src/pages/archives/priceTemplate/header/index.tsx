import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbProPageContainerProps,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import { useRef, useState } from 'react';
import Item from '../item/index';
import BatchSetting from './component/batchChange';
const PriceTemplate = () => {
  let refresh: any;

  const [batchVisible, setBatchVisible] = useState<boolean>(false);

  const { enable_organization } = useBaseParams((state) => state);
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbProPageContainerProps>(null);
  const [record, setRecord] = useState<any>({});
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'name':
        item.render = (value: any, record: any) => {
          return (
            <div
              className="link"
              onClick={() => {
                setRecord(record);
                pageModalRef.current?.setOpen(true);
              }}
            >
              {value}
            </div>
          );
        };
        break;
      case 'org_name':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div>
              <span>{value}</span>
            </div>
          );
        };
      default:
        break;
    }
    return item;
  };
  // 删除
  const deleteItem = async (
    fetchData: any,
    rowData?: any[],
    selectRowKeys?: string[],
  ) => {
    console.log('00--:', selectRowKeys);
    const sItem = rowData?.find((v) => v?.id === selectRowKeys?.[0]);
    XlbTipsModal({
      tips: `是否确定删除${sItem?.name}?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const data = { id: selectRowKeys?.[0] };
        const res = await XlbFetch.post(
          '/erp/hxl.erp.retailpricetemplate.delete',
          data,
        );
        if (res.code == 0) {
          message.success('操作成功');
          fetchData();
        }
        return true;
      },
    });
  };

  return (
    <>
      <BatchSetting
        visible={batchVisible}
        handleCancel={() => setBatchVisible(false)}
        getData={refresh}
      />
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={() => {
                  pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer
        ref={pageConatainerRef}
        searchFieldProps={{
          fieldProps: {
            initialValues: {
              company_id: LStorage.get('userInfo')?.company_id,
              operator_store_id: LStorage.get('userInfo').store_id,
            },
          },
          formList: [
            { id: 'commonInput', label: '关键字', name: 'keyword' },
            {
              label: '组织',
              name: 'org_ids',
              id: ErpFieldKeyMap?.erpOrgIdsLevel2,
              hidden: !enable_organization,
            },
            {
              id: ErpFieldKeyMap?.erpStoreIds,
              name: 'store_id_list',
            },
          ],
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.retailpricetemplate.page',
          selectMode: 'single',
          showColumnsSetting: false,
          immediatePost: true,
          tableColumn: [
            { name: '序号', code: '_index', width: 70, align: 'center' },
            {
              name: '组织',
              code: 'org_name',
              hidden: !enable_organization,
              width: 140,
            },
            {
              name: '零售价模板代码',
              code: 'code',
              width: 130,
              features: { sortable: true },
            },
            {
              name: '零售价模板名称',
              code: 'name',
              width: 160,
              features: { sortable: true },
            },
          ]?.map((v) => tableRender(v)),
        }}
        extra={(context) => {
          refresh = context?.fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['零售价模板', '编辑']) && (
                <XlbButton
                  style={{ order: 1 }}
                  type="primary"
                  label="新增"
                  onClick={() => {
                    setRecord({ id: -1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<XlbIcon size={16} name="jia" />}
                />
              )}
              {hasAuth(['零售价模板', '删除']) && (
                <XlbButton
                  style={{ order: 2 }}
                  type="primary"
                  label="删除"
                  disabled={
                    !context?.selectRowKeys || !context?.selectRowKeys?.length
                  }
                  onClick={() =>
                    deleteItem(
                      context?.fetchData,
                      context?.dataSource,
                      context?.selectRowKeys,
                    )
                  }
                  icon={<span className="iconfont icon-shanchu" />}
                />
              )}
              {hasAuth(['零售价模板', '编辑']) && (
                <XlbButton
                  style={{ order: 3 }}
                  type="primary"
                  label="批量设置"
                  onClick={() => setBatchVisible(true)}
                  icon={<span className="iconfont icon-xiugai1" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      />
    </>
  );
};

export default PriceTemplate;
