import NP from 'number-precision';

type NumberType = string | number | undefined;


export const safeDivideNumber = (val: NumberType) => {
  return Number(val) === 0 ? 1 : val
}

// 传入的值是 undefined会报 toString 的错误，所以统一处理为 0
const safeMath = {
  /**
   * 乘法
   */
  multiply(a: NumberType,b: NumberType) {
    const val = NP.times(a || 0, b || 0)
    return Number(val)
  },
  /**
   * 加法
   */
  add(a: NumberType, b: NumberType) {
    const val =  NP.plus(a || 0, b || 0)
    return Number(val)
  },
  /**
   * 除法
   */
  divide (a: NumberType, b: NumberType) {
    const val =  NP.divide(a || 0, safeDivideNumber(b || 0));
    return Number(val)
  },
  /**
   * 减法
   */
  minus (a: NumberType, b: NumberType) {
    const val = NP.minus(a || 0, b || 0)
    return Number(val)
  },
};

export default safeMath;
