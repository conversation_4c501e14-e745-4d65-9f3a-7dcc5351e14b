import { useNavigation } from '@xlb/max';
import { name } from '../../package.json';
import { history } from '@umijs/max';

// 子应用名称
export const appName = name;
export const auth = 'ERP';

const appUrlPrefix = `/${appName}`;

// export const useIRouter = () => {
//   return useNavigation();
// };

export const replaceUrlAppName = (path: string) => {
  return path.replace(appUrlPrefix, '');
};

export const isWUJIE = () => {
  return window.__POWERED_BY_WUJIE__;
};
export const wujieBus = window.$wujie?.bus;

export const useIRouter = () => {
  /**
   * 跳转
   * @param params
   *        url = string 时是直接跳转的地址
   *            = object 时是跳转携带的参数
   */
  const navigate = (path: any, state?: any) => {
    if (isWUJIE()) {
      window.$wujie?.bus.$emit('wujie-router-jump', {
        url: path,
        params: state,
        appType: appName,
      });
    } else {
      history.push(path, state);
    }
  };

  /**
   * 回退
   */
  const goBack = () => {
    // @ts-ignore
    history.go(-1);

    // masterProps?.childAction?.({
    //   appName,
    //   eventType: EVENT_TYPE.GOBACK,
    //   payload: { path: path },
    // });
  };

  return {
    navigate,
    goBack,
  };
};