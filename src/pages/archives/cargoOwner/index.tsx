import { hasAuth } from '@/utils';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbImportModal,
  XlbPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { message } from 'antd';
import AddItem from './components/addItem';
import modifyRecord from './components/modifyRecord';
import { cargoList, formList, statusList, tableList } from './data';
import Api from './server';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Index = () => {
  const [form] = XlbBasicForm.useForm<any>();

  let refresh = () => {};

  const prevPost = () => {
    const formData = form.getFieldsValue(true);
    return {
      ...formData,
    };
  };

  /**
   *
   */
  const Render = (item: any) => {
    switch (item.code) {
      case 'owner_type':
        item.render = (value: string) => {
          const curT = cargoList?.find((v) => v.value === value);
          return <div>{curT?.label}</div>;
        };
        break;
      case 'status':
        item.render = (value: string) => {
          const curT = statusList?.find((v) => v.value === value);
          return <div>{curT?.label}</div>;
        };
        break;
      default:
        return (item.render = (value: string) => (
          <div className="info overwidth">{value}</div>
        ));
    }
  };

  const exportItem = async (e: any, requestForm: any) => {
    const data = await XlbFetch.post(
      process.env.BASE_URL + '/erp-mdm/hxl.erp.cargo.owner.export',
      { ...requestForm },
    );

    if (data.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success(data?.data);
    }
  };

  tableList.map((v) => Render(v));

  const imports = () => {
    XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.cargo.owner.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.cargo.owner.template.download`,
      templateName: '货主管理导入',
      callback: (res) => {
        if (res.code === 0) {
          refresh();
        }
      },
    });
  };

  const add = () => {
    NiceModal.show(AddItem, { fetchData: refresh });
  };

  // 批量删除
  const batchUptate = async (selects: any[], status = 'disabled', list) => {
    const check_status = status === 'disabled' ? 'ENABLED' : 'DISABLED';
    const _status = status === 'disabled' ? 'batchDisabled' : 'batchEnabled';
    const name = status === 'disabled' ? '停用' : '启用';
    const checkInfo = list.filter((_) => _.status && _.status !== check_status);
    if (checkInfo.length) {
      XlbTipsModal({
        tips: (
          <>
            <div
              className={'danger'}
            >{`以下数据状态为${name},无需进行${name}操作`}</div>
            {checkInfo.map((_) => {
              return <div key={_.id}>{_.source_name}</div>;
            })}
          </>
        ),
      });
      return;
    }
    console.log(checkInfo);
    const resT = await Api[_status]({
      ids: selects,
      status: status.toUpperCase(),
    });
    if (resT?.code === 0) {
      console.log('hh', resT);
      message.success('操作成功');
      refresh();
    }
  };
  const handleRecord = () => {
    NiceModal.show(modifyRecord);
  };

  return (
    <XlbPageContainer
      url={'/erp-mdm/hxl.erp.cargo.owner.page'}
      tableColumn={tableList}
      prevPost={prevPost}
      immediatePost={true}
    >
      <ToolBtn showColumnsSetting>
        {({
          fetchData,
          loading,
          dataSource = [],
          selectRowKeys = [],
          selectRow = [],
          requestForm,
        }) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              <XlbButton
                key="query"
                label="查询"
                type="primary"
                disabled={loading}
                onClick={() => {
                  fetchData();
                }}
                icon={<span className="iconfont icon-sousuo" />}
              />
              {hasAuth(['货主管理', '编辑']) && (
                <XlbButton
                  label="新增"
                  type="primary"
                  onClick={add}
                  icon={<XlbIcon name="jia" />}
                />
              )}
              {hasAuth(['货主管理', '删除']) && (
                <XlbButton
                  label="停用"
                  type="primary"
                  disabled={!selectRowKeys.length}
                  onClick={() =>
                    batchUptate(selectRowKeys, 'disabled', selectRow)
                  }
                />
              )}
              {hasAuth(['货主管理', '编辑']) && (
                <XlbButton
                  label="启用"
                  type="primary"
                  disabled={!selectRowKeys.length}
                  icon={<XlbIcon name="edit" />}
                  onClick={() =>
                    batchUptate(selectRowKeys, 'enabled', selectRow)
                  }
                />
              )}
              {hasAuth(['货主管理', '导入']) && (
                <XlbButton
                  label="导入"
                  type="primary"
                  onClick={imports}
                  icon={<XlbIcon name="daoru" />}
                />
              )}
              {hasAuth(['货主管理', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  disabled={!dataSource.length}
                  onClick={(e) => exportItem(e, requestForm)}
                  icon={<XlbIcon name="daochu" />}
                />
              )}
              {hasAuth(['货主管理', '查询']) && (
                <XlbButton
                  label="修改记录"
                  type="primary"
                  icon={<XlbIcon name="shenqing" />}
                  onClick={handleRecord}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <SearchForm>
        <XlbForm formList={formList} form={form} isHideDate={true} />
      </SearchForm>
      <Table primaryKey="id" selectMode="multiple" />
    </XlbPageContainer>
  );
};
export default Index;
