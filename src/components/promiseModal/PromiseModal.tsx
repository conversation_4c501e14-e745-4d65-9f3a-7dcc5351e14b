import { hasAuth } from '@/utils/kit';
import NiceModal, { antdModal, useModal } from '@ebay/nice-modal-react';
import { XlbMessage, XlbModal } from '@xlb/components';
import { useState } from 'react';
import CollectDocument from './component/collectDocument';
import { ORDER_AUTH_MAP } from './component/data';
import DeliveryInOrder from './component/deliveryInOrder';
import DeliveryOrder from './component/deliveryOrder';
import PostOrder from './component/postOrder';
import ReceiptOrder from './component/receiptOrder';
import ReturnOrder from './component/returnOrder';
import StockAdjustment from './component/stockAdjustment';
import StockPriceChange from './component/stockPriceChange';
import StockTransFormOrder from './component/stockTransFormOrder';
import StoreCheckOrder from './component/storeCheckOrder';
import TransferWay from './component/transferWay';
import WholeReturn from './component/wholeReturn';
import WholeSale from './component/wholeSale';
const PromiseModal = NiceModal.create((data: any) => {
  const [width, setWidth] = useState(1013);
  const [backData, setBackData] = useState<any>({});
  const {
    order_type, // 单据类型
    order_fid, // 单据号id
    title,
    other_data,
    other_height,
    base_data,
    url,
    fullStatus,
    fid,
    isShowFooter = true,
  } = data;
  let hasShownNoAuthMessage = false;
  const modal = useModal();
  const element = () => {
    switch (order_type) {
      case '物资进出单': {
        return <TransferWay fid={order_fid} />;
      }
      case '收货单':
      case '采购收货单': {
        return <ReceiptOrder fid={order_fid} />;
      }
      case '退货单':
      case '采购退货单': {
        return <ReturnOrder fid={order_fid} />;
      }
      case '调出':
      case '调出单': {
        return <DeliveryOrder fid={order_fid} />;
      }
      case '调入':
      case '调入单': {
        return <DeliveryInOrder fid={order_fid} />;
      }
      case '批发销售单': {
        return <WholeSale fid={order_fid} />;
      }
      case '批发退货单': {
        return <WholeReturn fid={order_fid} />;
      }
      case '库存盘点':
      case '库存盘点单': {
        return <StoreCheckOrder fid={order_fid} />;
      }
      case '库存调整':
      case '库存调整单': {
        return <StockAdjustment fid={order_fid} />;
      }
      case '同店转仓':
      case '同店转仓单': {
        return <StockTransFormOrder fid={order_fid} />;
      }
      case '库存成本调整':
      case '库存成本调整单': {
        return <StockPriceChange fid={order_fid} />;
      }
      case '前台销售':
      case '前台销售单': {
        return <PostOrder url={url} fid={order_fid} />;
      }
      case '领用进出单': {
        return <CollectDocument url={url} fid={order_fid} />;
      }
      case '积分兑换': {
        return <></>;
      }
      default: {
        return <>未完成</>;
      }
    }
  };
  const modelCallBack = () => {
    other_data?.callBack && other_data?.callBack(backData);
    antdModal(modal).onCancel();
  };
  if (
    !hasAuth([ORDER_AUTH_MAP?.[order_type], '查询']) &&
    !['前台销售单', '前台销售', '积分兑换'].includes(order_type) &&
    modal.visible
  ) {
    XlbMessage.error('您没有权限查看该单据');
    return null;
  }

  return (
    <XlbModal
      width={width}
      centered
      title={title ? title : `${order_type}详情`}
      {...antdModal(modal)}
      isCancel
      onOk={modelCallBack}
      onCancel={antdModal(modal).onCancel}
      maskClosable
      footer={isShowFooter ? undefined : null}
    >
      <div style={{ marginBottom: 10 }}>{element()}</div>
    </XlbModal>
  );
});

export default PromiseModal;
