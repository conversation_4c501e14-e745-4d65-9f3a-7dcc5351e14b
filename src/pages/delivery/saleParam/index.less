.form_box {
  position: relative;
  top: 1px;
  display: inline-block;
  width: 160px;
  // padding: 10px 0 0 0;
  margin-bottom: 0;
  // height: 24px;
  overflow: hidden;
}

.erpButton {
  margin-left: 10px;
}

.table_box {
  height: calc(100vh - 260px);

  // padding: 10px 0;
  :global .art-table-body {
    min-height: calc(100vh - 380px);
  }
}

.button_box {
  padding: 6px 0 0 0;
  border-bottom: 1px solid @color_line2;
}

.spanss {
  position: absolute;
  top: -3px;
  right: 0;
  display: inline-block;
  width: 15px;
  height: 15px;
  padding-left: 4px;
  color: white;
  font-size: 12px;
  line-height: 15px;
  text-align: center;
  background-color: #000;
  border-radius: 50%;
  transform: scale(0.7);
  cursor: default;
}

.code {
  // background-color: #000;
  position: relative;
  display: flex;
  align-items: center;
  padding-top: 10px;
  padding-left: 14px;
  font-size: 14px;
}

.ant-checkbox-wrapper {
  width: auto;
}

.automatic1_input,
.period_input {
  width: 100px;
  height: 26px;
  padding: 5px;
  font-weight: 400;
  font-size: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 5px;
  outline: none;
}

.period_input {
  width: 60px !important;
  margin: 0 5px;
}
.input_margin {
  margin-left: 24px;
}
