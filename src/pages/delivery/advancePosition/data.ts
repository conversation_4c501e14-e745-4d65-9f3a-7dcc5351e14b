import { columnWidthEnum } from '@/data/common/constant'
import { clear } from 'antd-mobile/es/components/modal/clear'
import { SearchFormType, XlbTableColumnProps } from '@xlb/components-stage'
//单据状态
export const Options1 = [
  {
    label: '制单',
    value: 'INIT',
    type: 'info'
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning'
  },
  {
    label: '处理通过',
    value: 'APPROVE',
    type: 'success'
  },
  {
    label: '处理拒绝',
    value: 'REFUSE',
    type: 'danger'
  }
]
//结算状态
export const Options2 = [
  {
    label: '未结算',
    value: 'UNPAY',
    type: 'danger'
  },
  {
    label: '部分结算',
    value: 'PARTPAY',
    type: 'info'
  },
  {
    label: '已结算',
    value: 'ALLPAY',
    type: 'success'
  }
]
// 发票
export const Options3 = [
  {
    label: '已上传',
    value: true,
    type: 'success'
  },
  {
    label: '未上传',
    value: false,
    type: 'danger'
  }
]
//收款状态
export const PayStateList = [
  {
    label: '未付款',
    value: 'INIT',
    type: 'info'
  },
  {
    label: '已付款',
    value: 'FINISH',
    type: 'success'
  }
]
export const formList: SearchFormType[] = [
  {
    label: '单据状态',
    value: 'state',
    type: 'select',
    clear: true,
    check: true,
    options: Options1
  },
  {
    label: '结算状态',
    value: 'settlement_states',
    type: 'select',
    clear: true,
    check: true,
    multiple: true,
    options: Options2
  },
  {
    label: '是否分摊',
    value: 'shared',
    type: 'select',
    clear: true,
    check: true,
    options: [
      { label: '是', value: '1' },
      { label: '否', value: '0' }
    ]
  },
  {
    label: '单据号',
    value: 'fid',
    type: 'input',
    clear: true,
    check: true
  },
  {
    label: '时间类型',
    value: 'time_type',
    type: 'select',
    clear: false,
    check: true,
    options: [
      {
        label: '制单时间',
        value: 'create_date'
      },
      {
        label: '审核时间',
        value: 'audit_date'
      },
      {
        label: '付款日期',
        value: 'operate_date'
      },
      {
        label: '最后结算日期',
        value: 'max_settlement_date'
      }
    ]
  },
  {
    label: '供应商',
    value: 'supplier_names',
    type: 'dialog',
    clear: true,
    check: true
  },
  {
    label: '组织',
    value: 'org_ids',
    type: 'selects',
    clear: true,
    hidden: true,
    check: true,
    options: []
  },
  {
    label: '门店',
    value: 'store_names',
    type: 'dialog',
    clear: false,
    check: true
  },
  {
    label: '费用项目',
    value: 'fee_item_name',
    type: 'select',
    clear: true,
    check: true,
    options: []
  },
  {
    label: '制单人',
    value: 'create_by',
    type: 'input',
    check: true,
    clear: true
  },
  {
    label: '审核人',
    value: 'audit_by',
    type: 'input',
    check: true,
    clear: true
  },
  {
    label: '发票',
    value: 'invoice_status',
    type: 'select',
    clear: true,
    check: true,
    options: Options3
  },
  {
    label: '收支方向',
    value: 'flag',
    type: 'select',
    clear: true,
    check: true,
    options: [
      {
        label: '应收',
        value: true
      },
      {
        label: '应付',
        value: false
      }
    ]
  },
  {
    label: '开票属性',
    value: 'account_type_ints',
    type: 'select',
    clear: true,
    check: true,
    multiple: true,
    options: [
      {
        label: '票折',
        value: 1
      },
      {
        label: '账扣',
        value: 2
      },
      {
        label: '电汇',
        value: 3
      },
      {
        label: '空',
        value: 0
      }
    ]
  }
]

export const tableList: any[] = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '组织',
    code: 'org_name',
    width: 120,
    hidden: true
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 160,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 140,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '营业执照名称',
    code: 'letterhead',
    width: 180,
    features: { sortable: false }
  },
  {
    name: '纳税人类型',
    code: 'taxpayer_type',
    width: 180,
    features: { sortable: false }
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '付款日期',
    code: 'operate_date',
    width: columnWidthEnum.DATE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '费用项目',
    code: 'fee_item_name',
    width: 150,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '收支方向',
    code: 'flag',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '开票属性',
    code: 'account_type',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '费用金额',
    code: 'money',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '已结金额',
    code: 'paid_money',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '费用结余',
    code: 'pay_money',
    width: 100,
    features: { sortable: true },
    align: 'right'
  },
  {
    name: '单据状态',
    code: 'state',
    width: columnWidthEnum.ORDER_STATE,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '结算状态',
    code: 'settlement_state',
    width: 90,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '是否分摊',
    code: 'shared',
    width: 90,
    align: 'left',
    render: (value: boolean) => (value ? '是' : value === false ? '否' : '')
  },
  {
    name: '发票',
    code: 'invoice_status',
    width: 110,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '制单人',
    code: 'create_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: columnWidthEnum.BY,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '制单时间',
    code: 'create_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '最后结算日期',
    code: 'max_settlement_date',
    width: columnWidthEnum.TIME,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '留言备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
    align: 'left'
  },
  {
    name: '关联对账',
    code: 'supplier_account_apply_fids',
    width: 180,
    align: 'left'
  },
  {
    name: '关联结算',
    code: 'supplier_settlement_fids',
    width: 180,
    align: 'left'
  }
]

export const itemFormList: SearchFormType[] = [
  {
    label: '单据号',
    name: 'fid',
    type: 'input',
    clear: false,
    check: false,
    width: 260
  },
  // {
  //   label: '单据状态',
  //   value: 'state',
  //   type: 'input',
  //   clear: false,
  //   check: false,
  //   width:260
  // },
  {
    label: '供应商',
    valnameue: 'supplier_name',
    type: 'dialog',
    clear: false,
    check: true,
    width: 260
  },
  {
    label: '门店',
    name: 'store_name',
    type: 'dialog',
    clear: false,
    check: true,
    width: 260
  },
  {
    label: '组织',
    name: 'org_name',
    type: 'input',
    clear: false,
    hidden: true,
    check: false,
    disabled: true,
    width: 260
  },
  {
    label: '费用项目',
    name: 'fee_item_name',
    type: 'select',
    clear: true,
    check: true,
    disabled: false,
    options: []
  },
  {
    label: '收支方向',
    value: 'flag',
    type: 'input',
    clear: false,
    check: false,
    width: 260
  },
  {
    label: '参与对账',
    name: 'account_flag',
    type: 'select',
    clear: false,
    check: false,
    options: [
      {
        label: '不限',
        value: 'NOLIMIT'
      },
      {
        label: '仅对账',
        value: 'ACCOUNT'
      },
      {
        label: '仅结算',
        value: 'SETTLEMENT'
      }
    ]
  },
  {
    label: '开票属性',
    name: 'account_type',
    type: 'select',
    clear: false,
    check: false,
    hidden: true,
    options: [
      {
        label: '票折',
        value: 'INVOICE'
      },
      {
        label: '账扣',
        value: 'DEDUCTION'
      },
      {
        label: '电汇',
        value: 'WIRETRANSFER'
      }
    ]
  },
  {
    label: '费用金额',
    name: 'money',
    type: 'numberInput',
    clear: true,
    check: true,
    Max: *********,
    Min: -*********,
    width: 260
  },
  {
    label: '付款日期',
    name: 'operate_date',
    type: 'datePicker',
    clear: false,
    check: true
  },
  {
    label: '税率',
    name: 'tax_rate',
    type: 'select',
    clear: true,
    check: true,
    disabled: false,
    options: []
  },
  {
    label: '费用项目分类',
    name: 'fee_item_category_name',
    type: 'input',
    clear: false,
    disabled: true,
    check: false,
    width: 260
  },
  {
    label: '留言备注',
    name: 'memo',
    type: 'textArea',
    // clear: true,
    check: true
  }
]

export const baseInfoFormList: SearchFormType[] = [
  {
    label: '付款对象',
    name: 'collect_target',
    type: 'select',
    disabled: true,
    options: [
      {
        label: '供应商',
        value: 'SUPPLIER'
      }
    ],
    width: 160
  },
  {
    label: '供应商',
    name: 'supplier_name',
    type: 'input',
    disabled: true,
    width: 160
  },
  {
    label: '付款类型',
    name: 'collect_type',
    type: 'select',
    width: 160,
    disabled: true,
    options: [
      {
        label: '对账收款',
        value: 'ACCOUNTAPPLY'
      },
      {
        label: '预付退款',
        value: 'ADVANCEPAY'
      }
    ]
  },
  {
    label: '单据号',
    name: 'fid',
    width: 160,
    type: 'input',
    disabled: true
  },
  {
    label: '单据状态',
    name: 'state',
    width: 160,
    type: 'input',
    disabled: true
  },
  {
    label: '付款状态',
    name: 'collect_state',
    width: 160,
    type: 'input',
    disabled: true
  },
  {
    label: '结算日期',
    width: 160,
    name: 'settlement_date',
    type: 'datePicker'
  },
  {
    label: '摘要',
    name: 'digest',
    width: 160,
    type: 'input',
    clear: true,
    check: true
  },
  {
    label: '备注',
    name: 'memo',
    type: 'textArea',
    autoSize: { minRows: 2, maxRows: 2 },
    width: 680
  }
]
export const baseInfoCollection: SearchFormType[] = [
  {
    label: '收款门店',
    name: 'settlement_store_name',
    width: 160,
    type: 'input',
    disabled: true
  },
  {
    label: '收款开户行',
    name: 'bank',
    width: 160,
    type: 'select',
    options: []
  },
  {
    label: '收款开户名',
    width: 160,
    name: 'bank_account_name',
    type: 'input',
    disabled: true
  },
  {
    label: '银行账号',
    name: 'bank_account',
    width: 160,
    type: 'input',
    disabled: true
  }
]
export const baseInfoBusiness: SearchFormType[] = [
  {
    label: '对账申请单号',
    name: 'supplier_account_apply_fid',
    width: 160,
    type: 'input',
    readonly: true
  },
  {
    label: '应收金额',
    name: 'money',
    width: 160,
    type: 'input',
    disabled: true
  },
  {
    label: '待收金额',
    width: 160,
    name: 'obligation',
    type: 'input',
    disabled: true
  },
  {
    label: '费用金额',
    name: 'fee_money',
    width: 160,
    type: 'input',
    disabled: true
  }
]
export const otherInfoFormList: SearchFormType[] = [
  {
    label: '制单人',
    name: 'create_by',
    type: 'input',
    disabled: true,
    width: 160
  },
  {
    label: '制单时间',
    name: 'create_time',
    type: 'input',
    disabled: true,
    width: 160
  },
  {
    label: '审核人',
    name: 'audit_by',
    type: 'input',
    disabled: true,
    width: 160
  },
  {
    label: '审核时间',
    name: 'audit_time',
    type: 'input',
    disabled: true,
    width: 160
  },
  {
    label: '修改人',
    name: 'update_by',
    type: 'input',
    disabled: true,
    width: 160
  },
  {
    label: '修改时间',
    name: 'update_time',
    type: 'datePicker',
    disabled: true,
    width: 160
  },
  {
    label: '处理人',
    name: 'handle_by',
    type: 'datePicker',
    disabled: true,
    width: 160
  },
  {
    label: '处理时间',
    name: 'handle_time',
    type: 'datePicker',
    disabled: true,
    width: 160
  },
  {
    label: '收款人',
    name: 'collect_by',
    type: 'input',
    disabled: true,
    width: 160
  },
  {
    label: '收款时间',
    name: 'collect_time',
    type: 'datePicker',
    disabled: true,
    width: 160
  }
]

export const baseInfoFormListStore: SearchFormType[] = [
  {
    label: '组织',
    value: 'org_ids',
    type: 'select',
    clear: true,
    multiple: true,
    hidden: true,
    check: true,
    options: []
  },
  {
    label: '前置仓',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: true,
    dependencies: ['org_ids'],
    dialogParams: (obj) => {
      return {
        type: 'store',
        dataType: 'lists',
        isMultiple: true,
        data: {
          org_ids: obj?.org_ids || null
        }
      }
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name'
    } as any
  },
  {
    label: '区域仓',
    value: 'orgStore',
    type: 'select',
    clear: true,
    multiple: true,
    hidden: true,
    check: true,
    options: []
  }
]
export const tableListGoods: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    align: 'center',
    lock: true
  },
  {
    name: '商品代码',
    code: 'code',
    width: 110,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'name',
    width: 280,
    features: { sortable: true }
  },
  {
    name: '商品分类',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '速记码',
    code: 'shorthand_code',
    width: columnWidthEnum.SHORTHAND_CODE,
    features: { sortable: true }
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '基本单位',
    code: 'unit',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '停购',
    code: 'stop_purchase',
    width: 70,
    features: { sortable: true }
  },
  {
    name: '停止要货',
    code: 'stop_request',
    width: 100,
    features: { sortable: true }
  }
]
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  },
  {
    label: '标准商品',
    value: 'STANDARD'
  },
  {
    label: '组合商品',
    value: 'COMBINATION'
  },
  {
    label: '成分商品',
    value: 'COMPONENT'
  },
  {
    label: '制单组合',
    value: 'MAKEBILL'
  }
]