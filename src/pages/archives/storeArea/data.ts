import { searchFormType } from '@/data/common/type'
import { ArtColumn } from 'ali-react-table'

export const formList: searchFormType[] = [
  {
    label: '关键字',
    value: 'keyword',
    type: 'input',
    clear: true,
    check: true
    // placeholder:'名称'
  }
]

export const tableColumn: ArtColumn[] = [
  {
    name: '序号',
    code: '_index',
    width: 50,
    align: 'center'
  },
  {
    name: '门店区域名称',
    code: 'name',
    // width: 180,
    features: { sortable: true }
  },
  {
    name: '门店区域分类',
    code: 'store_area_categories',
    features: { sortable: true }
  }
]

export const ShopArr: ArtColumn[] = [
  {
    name: '序号',
    code: 'index',
    width: 60,
    align: 'center'
  },
  {
    name: '门店代码',
    code: 'store_code',
    width: 120,
    features: { sortable: true }
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '门店分组',
    code: 'store_group_name',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '店铺模式',
    code: 'management_type',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '配送类型',
    code: 'delivery_type',
    width: 100,
    features: { sortable: true }
  },
  {
    name: ''
  }
]
