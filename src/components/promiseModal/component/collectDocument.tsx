import { hasAuth } from '@/utils/kit';
import {
  SearchFormType,
  XlbBasicForm,
  XlbForm,
  XlbTable,
  XlbTableColumnProps,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import dayjs from 'dayjs';
import _ from 'lodash';
import { cloneDeep } from 'lodash-es';
import { useEffect, useState } from 'react';
import { readInfoCol } from '../server';
const formList: SearchFormType[] = [
  {
    type: 'input',
    disabled: true,
    name: 'store_name',
    label: '发货门店',
  },
  {
    type: 'input',
    disabled: true,
    name: 'storehouse_name',
    label: '仓库',
  },
  {
    type: 'input',
    disabled: true,
    name: 'org_name',
    label: '发货组织',
  },
  {
    type: 'select',
    disabled: true,
    name: 'flag',
    label: '进出方向',
    options: [
      {
        label: '进货',
        value: true,
      },
      {
        label: '退货',
        value: false,
      },
    ],
  },
  {
    type: 'input',
    disabled: true,
    name: 'requisition_org_name',
    label: '领用部门',
  },
  {
    type: 'input',
    disabled: true,
    name: 'cargo_owner_name',
    label: '货主',
  },
  {
    type: 'select',
    disabled: true,
    name: 'reason_id',
    label: '领用原因',
    options: [],
  },
  {
    type: 'input',
    disabled: true,
    name: 'fid',
    label: '单据号',
  },
  {
    type: 'input',
    disabled: true,
    name: 'item_dept_names',
    label: '商品部门',
  },
  {
    type: 'select',
    disabled: true,
    name: 'state',
    label: '单据状态',
    options: [
      {
        label: '制单',
        value: 'INIT',
      },
      {
        label: '审核',
        value: 'AUDIT',
      },
      { label: '完成', value: 'FINISH' },
      { label: '作废', value: 'INVALID' },
    ],
  },
  {
    type: 'input',
    disabled: true,
    name: 'memo',
    label: '留言备注',
    width: 648,
  },
];
const columnsList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 100,
    align: 'center',
  },
  {
    name: '操作',
    code: '_operator',
    align: 'center',
    width: 80,
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true, showShort: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '单位',
    code: 'ratio',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '单价',
    code: 'price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '金额',
    code: 'money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '销项税率(%)',
    code: 'output_tax_rate',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税费',
    code: 'tax_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '时点成本价',
    code: 'original_money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '时点成本价(去税)',
    code: 'original_no_tax_money',
    width: 150,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '生产日期',
    code: 'producing_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '到期日期',
    code: 'expire_date',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '批次号',
    code: 'batch_number',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '保质期',
    code: 'period',
    width: 110,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '库存数量',
    code: 'basic_stock_quantity',
    width: 160,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '备注',
    code: 'memo',
    width: 160,
    features: { sortable: true },
    align: 'left',
  },
];

const Index = (props: any) => {
  const { fid } = props;
  const [formModel] = XlbBasicForm.useForm<any>();
  const [tableLoading, setTableLoading] = useState<any>(false);
  // 列表数据
  const [fidDataList, setFidDataList] = useState<any>([]);
  const [formlist, setFormlist] = useState<any[]>(cloneDeep(formList));
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    columnsList.map((i: any) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == '_operator' ? true : i.hidden,
    })) as any[],
  );
  const getReasonList = async () => {
    const res = await XlbFetch.post('/erp/hxl.erp.requisitionreason.find', {});
    if (res?.code == 0) {
      const options = res?.data.map((item: any) => ({
        label: item.name,
        value: item.id,
        disabled: !item.flag,
      }));
      formlist.find((v) => v.label == '领用原因')!.options = options;
      setFormlist([...formlist]);
    }
  };
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'ratio':
        item.render = (value: any, record: any, index: number) => {
          record.units = Array.from(
            new Set([
              JSON.stringify({
                label: record.delivery_unit,
                value: record.delivery_ratio,
              }),
              JSON.stringify({ label: record.basic_unit, value: 1 }),
              JSON.stringify({
                label: record?.unit,
                value: record?.isfirst ? 1 : record?.ratio,
              }),
            ]),
          ).map((item) => {
            return JSON.parse(item);
          });
          return (
            <div>
              {record.units?.find((v) => v.value === record.ratio)?.label}
            </div>
          );
        };
        break;
      case 'quantity':
        item.render = (value: any, record: any, index: any) => {
          return Number(value || 0)?.toFixed(3);
        };
      case 'basic_quantity':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(3)}</div>;
        };
        break;
      case 'tax_money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(2)}</div>;
        };
        break;
      case 'initial_quantity':
      case 'activity_quantity':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{value?.toFixed(3)}</div>;
        };
        break;
      case 'original_money':
      case 'original_no_tax_money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(4)}</div>;
        };
        break;
      case 'price':
      case 'basic_price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {hasAuth(['领用进出单/成本价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'money':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{Number(value || 0)?.toFixed(4)}</div>;
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: number) => {
          return <div className="info">{value}</div>;
        };
        break;
    }
    return item;
  };

  const openRefOrder = _.debounce(async (fid, summary: boolean = false) => {
    setTableLoading(true);
    setFidDataList([]);
    formModel.setFieldsValue({});
    const res = await readInfoCol({ fid: fid, summary: summary });
    if (res?.code == 0) {
      // 表单的值
      formModel.setFieldsValue({
        ...res?.data,
        operate_date: res.data.operate_date
          ? dayjs(res.data.operate_date)
          : null,
        payment_date: res.data.payment_date
          ? dayjs(res.data.payment_date)
          : null,
      });
      // 列表的值
      setFidDataList(res?.data?.details);
      setTableLoading(false);
    }
  }, 50);
  itemArrdetail.map((v) => tableRender(v));
  useEffect(() => {
    openRefOrder(fid);
    getReasonList();
  }, []);

  return (
    <>
      <XlbForm
        style={{ marginTop: 15 }}
        formList={formlist}
        form={formModel}
        isHideDate={true}
      />
      <XlbTable
        isLoading={tableLoading}
        style={{ height: 400, maxHeight: 400, overflowY: 'scroll' }}
        hideOnSinglePage={false}
        showSearch={true}
        columns={itemArrdetail}
        total={fidDataList?.length}
        dataSource={fidDataList}
        key={fidDataList?.length}
      ></XlbTable>
    </>
  );
};

export default Index;
