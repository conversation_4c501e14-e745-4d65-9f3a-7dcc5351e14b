import { ArtColumn } from 'ali-react-table'
import { columnWidthEnum } from '@/data/common/constant'
import {
  gettimechange,
  getTimeChangeSpecial,
  storeItemchange
} from './server'

export const makeType = [
  {
    label: '门店补货',
    value: 'REQUEST'
  },
  {
    label: '批发订货',
    value: 'WHOLESALE'
  }
]

export const itemType: any[] = [
  {
    label: '标准商品',
    value: 'STANDARD'
  },
  {
    label: '组合商品',
    value: 'COMBINATION'
  },
  {
    label: '成分商品',
    value: 'COMPONENT'
  },
  {
    label: '制单组合',
    value: 'MAKEBILL'
  },
  {
    label: '分级商品',
    value: 'GRADING'
  },
  {
    label: '主规格商品',
    value: 'MAINSPEC'
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC'
  }
]

export const formList: any[] = [
  {
    label: '组织',
    value: 'org_ids',
    type: 'selects',
    clear: true,
    check: true,
    hidden: true,
    options: []
  },
  {
    label: '门店名称',
    value: 'store_ids',
    type: 'inputDialog',
    modalType: 'store',
    isMultiple: true,
    filterData: {
      center_flag: true
    },
    clear: true,
    check: true,
    hidden: true
  },
  // {
  //   label: '配送门店',
  //   value: 'store_id',
  //   type: 'inputDialog',
  //   modalType: 'store',
  //   isMultiple: false,
  //   filterData: {
  //     center_flag: true
  //   },
  //   clear: true,
  //   check: true,
  //   hidden: true
  // },
  {
    label: '订购类型',
    value: 'type',
    type: 'select',
    clear: false,
    check: true,
    options: makeType,
    hidden: true,
    Change: (value: any) => {}
  },
  {
    label: '商品档案',
    value: 'item_ids',
    type: 'inputDialog',
    modalType: 'item',
    filterData: {
      filter_item_types: ['COMBINATION', 'MAKEBILL']
    },
    clear: true,
    check: true
  },
  {
    label: '商品类别',
    value: 'item_category_ids',
    type: 'inputGoodsType',
    clear: true,
    check: true
  },
  {
    label: '停购',
    value: 'stop_purchase',
    type: 'select',
    options: [
      { label: '是', value: true },
      { label: '否', value: false }
    ],
    clear: true,
    check: true
  },
  {
    label: '推荐商品',
    value: 'recommend',
    type: 'select',
    clear: true,
    check: true,
    hidden: true,
    options: [
      {
        label: '否',
        value: false
      },
      {
        label: '是',
        value: true
      }
    ]
  },
  {
    label: '端头商品',
    value: 'end_cap',
    type: 'select',
    clear: true,
    check: true,
    hidden: true,
    options: [
      {
        label: '否',
        value: false
      },
      {
        label: '是',
        value: true
      }
    ]
  },
  {
    label: '订购类型',
    value: 'type',
    type: 'select',
    check: true,
    hidden: true,
    options: makeType
  }
]

export const goodsOrderArr: any[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '商品代码',
    code: 'code',
    width: 110,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'name',
    width: 280,
    features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '商品类别',
    code: 'category_name',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true }
  },
  {
    name: '停购',
    code: 'stop_purchase',
    width: 80,
    features: { sortable: true }
  },
  {
    name: '每单订购下限',
    code: 'lower_limit',
    width: 130,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '订购倍数',
    code: 'multiple',
    width: 100,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '配送日订购上限',
    code: 'upper_limit',
    width: 140,
    align: 'right',
    features: { sortable: true }
  },

  {
    name: ''
  }
]

export const storeGoodsOrderArr: ArtColumn[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '组织',
    code: 'org_name',
    width: 140,
    hidden: true,
    features: { sortable: true }
  },
  {
    name: '门店名称',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true }
  },
  {
    name: '商品代码',
    code: 'code',
    width: 110,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'bar_code',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'name',
    width: 280,
    features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '商品类别',
    code: 'category_name',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '单位',
    code: 'unit',
    width: 80,
    features: { sortable: true }
  },
  {
    name: '停购',
    code: 'stop_purchase',
    width: 80,
    features: { sortable: true }
  },
  {
    name: '每单订购下限',
    code: 'lower_limit',
    width: 130,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '订购倍数',
    code: 'multiple',
    width: 100,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '配送日订购上限',
    code: 'upper_limit',
    width: 140,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: '备货订货上限',
    code: 'stock_upper_limit',
    width: 140,
    align: 'right',
    features: { sortable: true }
  },
  {
    name: ''
  }
]

export const goodsSpecialArr: ArtColumn[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 110,
    features: { sortable: true }
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true }
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: 160,
    features: { sortable: true }
  },

  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '商品类别',
    code: 'category_name',
    width: 140,
    features: { sortable: true }
  },
  {
    name: '基本单位',
    code: 'unit',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '推荐商品',
    code: 'recommend',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '端头商品',
    code: 'end_cap',
    width: 100,
    features: { sortable: true }
  },
  {
    name: '端头推荐量',
    code: 'end_cap_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true }
  }
]

// 批量修改
export const batch = [
  {
    label: '每单订购下限',
    value: 'lower_limit'
  },
  {
    label: '订购倍数',
    value: 'multiple'
  },
  {
    label: '配送日订购上限',
    value: 'upper_limit'
  },
  {
    label: '备货订货上限',
    value: 'stock_upper_limit'
  }
]

// 批量修改--特性
export const batch_special = [
  {
    label: '推荐商品',
    value: 'recommend'
  },
  {
    label: '端头商品',
    value: 'end_cap'
  },
  {
    label: '端头推荐量',
    value: 'end_cap_quantity'
  }
]

export const TABLELISTES = {
  order_num: goodsOrderArr,
  store_order_num: storeGoodsOrderArr,
  order_special: goodsSpecialArr
}
export const PAGEURLLISTES = {
  order_num: '/erp/hxl.erp.itemorderattribute.page',
  store_order_num: '/erp/hxl.erp.storeorderattribute.page',
  order_special: '/erp/hxl.erp.itemorderfeature.page'
}
export const EXPORTURLLISTES = {
  order_num: '/erp/hxl.erp.itemorderattribute.export',
  store_order_num: '/erp/hxl.erp.storeorderattribute.export',
  order_special: '/erp/hxl.erp.itemorderfeature.export'
}
export const IMPORTURLLISTES = {
  order_num: '/erp/hxl.erp.itemorderattribute.import',
  store_order_num: '/erp/hxl.erp.storeorderattribute.import',
  order_special: '/erp/hxl.erp.itemorderfeature.import'
}
export const IMPORTDOWNLOADURLLISTES = {
  order_num: '/erp/hxl.erp.itemorderattributetemplate.download',
  store_order_num: '/erp/hxl.erp.storeorderattributetemplate.download',
  order_special: '/erp/hxl.erp.itemorderfeaturetemplate.download'
}

export const UPADTEAPI = {
  order_num: gettimechange,
  store_order_num: storeItemchange,
  order_special: getTimeChangeSpecial
}
