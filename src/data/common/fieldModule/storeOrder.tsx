import { XlbInputDialogProps } from '@xlb/components'
import { omit } from 'lodash'

export const StoreOrderKeyMap = {
  erpReceivingStore: 'erpReceivingStore',
  erpReceivingStores: 'erpReceivingStores',
  erpMemoInput: 'erpMemoInput',
  erpStorehouseId: 'erpStorehouseId',
  erpStoreFidType:'erpStoreFidType',
  erpStorehouseIdDeliveryOrder:'erpStorehouseIdDeliveryOrder'
}

export const storeOrderConfig: any[] = [
  {
    tag: 'ERP',
    label: '收货门店',
    id: StoreOrderKeyMap.erpReceivingStore,
    name: 'store_id',
    dependencies: ['org_ids'],
    fieldProps: (form: any) => {
      const data = {
        org_ids: form?.getFieldValue('org_ids') || undefined,
        status: true
      }
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids
      }
      return {
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        } as any,
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          data: data
        }
      }
    },
    formItemProps: {
      getValueFromEvent: (e: any) => {
        return e?.[0]
      }
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '收货门店',
    id: StoreOrderKeyMap.erpReceivingStores,
    name: 'store_id',
    dependencies: ['supplier_id'],
    fieldProps(formValue: any) {
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: false,
          url: '/erp-mdm/hxl.erp.store.short.page',
          data: {
            supplier_id: formValue.getFieldValue('supplier_id') || '',
            query_supply: true
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      getValueFromEvent: (event: Parameters<NonNullable<XlbInputDialogProps['onChange']>>[0]) => {
        return event ? event[0] : undefined
      },
      getValueProps: (value: any) => {
        return { value: value ? [value] : undefined }
      }
    },
    componentType: 'inputDialog'
  },
  {
    tag: 'ERP',
    label: '仓库',
    id: StoreOrderKeyMap.erpStorehouseId,
    name: 'storehouse_id',
    dependencies: ['store_id'],
    async request(params: any, anybaseURL: any, globalFetch: any) {
      const { store_id } = params
      if (!store_id) {
        return []
      }
      const result = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.storehouse.store.find`, {
        store_id
      })
      if (Array.isArray(result?.data)) {
        return result.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
    },
    // showSearch:true,
    handleDefaultValue(data: any) {
      const defaultStoreHouse = data.find((item: any) => item.default_flag) || data[0]
      return defaultStoreHouse?.value
    },
    componentType: 'select'
  },
  {
    tag: 'ERP',
    label: '备注',
    id: StoreOrderKeyMap.erpMemoInput,
    fieldProps: {
      style: {
        width: '100%'
      }
    },
    name: 'memo',
    componentType: 'input'
  },
  // 拉新配置防止影响
  {
    tag: 'ERP',
    label: '仓库',
    id: StoreOrderKeyMap?.erpStorehouseIdDeliveryOrder,
    name: 'storehouse_id',
    dependencies: ['store_id'],
    async request(params: any, anybaseURL: any, globalFetch: any) {
      const { store_id } = params
      let store_idT: number
      if (!Array.isArray(store_id) || !store_id.length) {
        console.warn('store_id不存在')
        return []
      }

      if (store_id.length > 1) {
        console.warn('多个store_id')
        return []
      }
      store_idT = store_id[0]
      const result = await globalFetch.post(`${anybaseURL}/erp-mdm/hxl.erp.storehouse.store.find`, {
        ...omit(params, 'store_ids'),
        store_id: store_idT
      })
      if (Array.isArray(result.data)) {
        return result.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
    },
    // showSearch:true,
    handleDefaultValue(data: any) {
      const defaultStoreHouse = data.find((item: any) => item.default_flag) || data[0]
      return defaultStoreHouse?.value
    },
    componentType: 'select'
  },
]