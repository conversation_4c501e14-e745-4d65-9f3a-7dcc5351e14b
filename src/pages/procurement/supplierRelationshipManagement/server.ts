import { XlbFetch as ErpRequest } from '@xlb/utils'

// 分页查询
export const getStoreItemSupplier = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplier.page', data)
}
// 获取生产商列表
export const getProSupplier = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.supplier.producerandexecutivestandard.find', data)
}

// 更新
export const updateStoreItemSupplier = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplier.update', data)
}

// 批量更新主供应商
export const batchUpdateStoreItemSupplierMain = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplier.main.batchupdate', data)
}

// 批量更新
export const batchUpdateStoreItemSupplier = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplier.batchupdate', data)
}

// 批量删除
export const batchDeleteStoreItemSupplier = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplier.batchdelete', data)
}
// 复制
export const copy = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplier.copy', data)
}
// 进项税率
export const getBaseparam = async () => {
  return await ErpRequest.post('/erp/hxl.erp.baseparam.read',{})
}
// 获取税率参数列表
export const getTaxRates = async () => {
  return await ErpRequest.post('/erp/hxl.erp.baseparam.read',{})
}
// 商品供货关系批量更新
export const batchStoreItemSupplier = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplier.batchupdateattr', data)
}

// 商品供货关系修改记录
export const getStoreItemSupplierHistory = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeitemsupplierlog.page', data)
}
