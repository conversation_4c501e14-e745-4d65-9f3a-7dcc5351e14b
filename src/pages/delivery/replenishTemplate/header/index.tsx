import { message } from 'antd';
import { useRef, useState } from 'react';
import { tableColumn } from '../data';
import Api from '../server';
// import { useKeepAliveRefresh } from '@/hooks'
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils';
import {
  XlbButton,
  XlbIcon,
  XlbProPageContainer,
  XlbProPageContainerRef,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTipsModal,
} from '@xlb/components';
import Item from '../item';
const Index: React.FC = () => {
  let refresh = () => {};
  const [record, setRecord] = useState<any>({});
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbProPageContainerRef>(null);
  const { enable_organization } = useBaseParams((state) => state);

  const render = (item: any) => {
    switch (item.code) {
      case 'name':
        return (item.render = (value: any, record: any) => (
          <div>
            <span
              className="link cursors"
              onClick={() => {
                setRecord(record);
                pageModalRef.current?.setOpen(true);
              }}
            >
              {value}
            </span>
          </div>
        ));
      default:
        return (item.render = (value: any) => (
          <div className="info">{value}</div>
        ));
    }
  };

  const deleteRow = async (
    dataSource: any[],
    selectRowKeys: React.Key[],
    fetchData: Function,
  ) => {
    await XlbTipsModal({
      tips: `是否确认删除${dataSource.filter((v) => v.id === selectRowKeys[0])[0]?.name}`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const { code } = await Api.delete({
          id: Number(selectRowKeys[0]),
        });

        if (code === 0) {
          message.success(
            `${dataSource.filter((v) => v.id === selectRowKeys[0])[0]?.name}已删除`,
          );
          fetchData();
        }
        return true;
      },
    });
  };
  tableColumn.map((v) => render(v));

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    pageConatainerRef?.current?.pageContainerRef.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbProPageContainer
        ref={pageConatainerRef}
        searchFieldProps={{
          formList: [
            { id: 'commonInput', label: '关键字', name: 'keyword' },
            {
              label: '组织',
              id: ErpFieldKeyMap?.erpOrgIds,
              hidden: !enable_organization,
            },
          ],
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.replenishtemplate.find',
          selectMode: 'single',
          showColumnsSetting: false,
          immediatePost: true,
          tableColumn: tableColumn?.filter((v) => {
            if (v.code == 'org_name') {
              return enable_organization;
            }
            return v
          }),
        }}
        extra={(context) => {
          refresh = context?.fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['补货模板', '编辑']) && (
                <XlbButton
                  style={{ order: 1 }}
                  type="primary"
                  label="新增"
                  onClick={() => {
                    setRecord({ id: -1 });
                    pageModalRef.current?.setOpen(true);
                  }}
                  icon={<XlbIcon size={16} name="jia" />}
                />
              )}
              {hasAuth(['补货模板', '删除']) && (
                <XlbButton
                  style={{ order: 2 }}
                  type="primary"
                  label="删除"
                  disabled={
                    !context?.selectRowKeys || !context?.selectRowKeys?.length
                  }
                  onClick={() =>
                    deleteRow(
                      context?.dataSource,
                      context?.selectRowKeys,
                      context?.fetchData,
                    )
                  }
                  icon={<span className="iconfont icon-shanchu" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      />
    </>
  );
};

export default Index;
