import { LStorage } from '@/utils/storage'
import {
  XlbBasicData,
  XlbButton,
  XlbIcon,
  XlbInputNumber,
  XlbMessage,
  XlbModal,
  XlbProForm,
  XlbTable,
  XlbTipsModal
} from '@xlb/components'
import { Checkbox, Form, InputNumber } from 'antd'
import Decimal from 'decimal.js'
import { cloneDeep } from 'lodash'
import { useRef, useState } from 'react'
import { batchOrderSave } from '../../server'
import { itemTableList } from './data'
import styles from './deliveryOrder.less'

const DeliveryOrderIndex = (props: any) => {
  const formRef = useRef<any>(null)
  const { open, setOpen } = props
  const [formUpdate] = Form.useForm()
  const [rowData, setRowData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [itemArr, setItemArr] = useState<any>(JSON.parse(JSON.stringify(itemTableList)))
  const [goodList, setGoodList] = useState<any>([]) //商品数组
  const [selectedStoreList, setSelectedStoreList] = useState<string[]>([]) //已选择门店
  const [selectedGoodList, setSelectedGoodList] = useState<number[]>([]) //已选择商品
  const modalRef = useRef(null)
  const userInfo = LStorage.get('userInfo')

  const getMult = (num1: number, num2: number, precision: number = 4) => {
    return new Decimal(num1 || 0)
      .mul(new Decimal(num2 || 0))
      .toNumber()
      ?.toFixed(precision)
  }

  const inputChange = (
    e: number | string | null,
    index: number,
    renderv: any,
    record: any,
    type: string
  ) => {
    console.log('99900:', renderv, record)
    setRowData((prev) => {
      const list = cloneDeep(prev)
      if (type === 'quantity') {
        // 更新对应的quantity
        const curItem = list[index].detail.find((v: any) => v.id === renderv) || {}
        curItem.quantity = e
        list[index][`${renderv}_quantity`] = e
      }
      return list
    })
  }

  //操作删除行
  const showDeleteModal = (record: any) => {
    // 删除选中的门店
    const updatedRowData = rowData.filter((store: any) => store.store_id !== record.store_id)

    // 若门店删光，清空商品相关数据，仅保留 itemArr 的第一个元素
    if (updatedRowData.length === 0) {
      setGoodList([])
      setItemArr([itemArr[0]]) // ✅ 只保留第一个元素
    }
    // updatedRowData 中不存在的门店，从 selectedStoreList 中删除
    setSelectedStoreList((prev) => prev.filter((store: any) => record.store_id != store))
    setRowData(updatedRowData)
  }

  const storeStrongRender = (item: any) => {
    if (item.children && item.children.length) {
      item.children.map((v: any) => {
        switch (v?.code) {
          case 'index':
            v.render = (value: any, record: any, index: number) => {
              return <div>{index + 1}</div>
            }
            break

          case 'action':
            v.render = (value: any, record: any, index: number) => {
              return (
                <div>
                  <XlbButton
                    icon={<XlbIcon name="shanchu" />}
                    type="text"
                    onClick={(e) => {
                      e.stopPropagation()
                      showDeleteModal(record)
                    }}
                  />
                </div>
              )
            }
            break
        }
      })
    }
  }

  const onChange = (e: any, list: any, i: number) => {
    const key = Number(e.target.value)

    // 获取modal中的table
    const checkBoxs = modalRef?.current
      ?.querySelector('thead')
      .querySelector('.art-table-header-row')!
      .getElementsByClassName('art-table-header-cell')

    if (key === 9999 || key === 1111) {
      for (let k = 0; k < checkBoxs.length - 1; k++) {
        k > 0 && (checkBoxs[k].querySelector('input[type="checkbox"]')!.checked = e.target.checked)
      }
      setSelectedGoodList(key === 9999 ? goodList.map((v) => v.id) : [])
    } else {
      setSelectedGoodList((prevList) => {
        const index = prevList.indexOf(key)
        if (index === -1) {
          return [...prevList, key]
        } else {
          const newList = [...prevList]
          newList.splice(index, 1)
          return newList
        }
      })
    }
  }
  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter') {
      e.preventDefault()
    }
  }
  // 添加商品
  const handleAddClick = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      url: '/erp/hxl.erp.storeorder.item.union.page',
      type: 'goods',
      dataType: 'lists',
      primaryKey: 'id',
      selectedList: rowData?.[0].detail.map((i: any) => ({
        ...i,
        id: i.item_id || i.id
      })),
      fieldNames: {
        idKey: 'id',
        nameKey: 'name'
      },
      data: {
        store_ids: rowData?.map((i) => i?.store_id),
        type: 'STOREHOUSE_DELIVERY',
        enabled: true
      }
    })
    if (list) {
      const existingItemIds = new Set(itemArr.slice(1).map((item: any) => item.id))
      const newItemArr = [itemArr[0], ...itemArr.slice(1)] // 先保留头部和旧商品

      list.forEach((item: any) => {
        if (!existingItemIds.has(item.id)) {
          const newCol = {
            title: (
              <span className={styles.checkText}>
                <input
                  className={styles.diyCheckBox}
                  type="checkbox"
                  key={item.id}
                  value={item.id}
                  onChange={(e: any) => onChange(e, list, item.id)}
                />
                {item.name + '/' + item.code}
              </span>
            ),
            code: item.id,
            id: item.id,
            width: 300,
            align: 'center',
            children: [
              {
                title: `采购规格:${item.purchase_spec || ''}`,
                code: item.id,
                width: 160,
                align: 'center',
                children: [
                  {
                    title: '配送量',
                    code: `${item.id}_quantity`,
                    align: 'center',
                    render: (value: any, record: any, index: number) => {
                      return record._edit ? (
                        <XlbInputNumber
                          onKeyDown={handleKeyDown}
                          min={0}
                          precision={4}
                          formatter={(value) => {
                            return `${value}`.replace(/[^0-9.]/g, '')
                          }}
                          value={value}
                          parser={(value) => {
                            return value ? value.replace(/[^0-9.]/g, '') : ''
                          }}
                          onChange={(e) => inputChange(e, index, item.id, record, 'quantity')}
                          onClick={(e) => {
                            e.stopPropagation()
                          }}
                        />
                      ) : (
                        <div style={{ textAlign: 'right' }}>{value}</div>
                      )
                    }
                  },
                  {
                    title: `门店库存量`,
                    code: `${item.id}_stock_quantity`,
                    align: 'center'
                  }
                ]
              }
            ],
            obj: item
          }

          newItemArr.push(newCol)
        }
      })

      // 3. 最终更新数据
      setItemArr([...newItemArr])
      setGoodList(JSON.parse(JSON.stringify(newItemArr.slice(1)))) // 去掉头部

      // 更新每个门店中的 detail 和 数量字段
      setRowData((prevRows) => {
        return prevRows.map((store) => {
          const updatedDetail = [...(store.detail || [])]
          const detailIds = new Set(updatedDetail.map((d) => d.id))

          list.forEach((item: any) => {
            const keyQty = `${item.id}_quantity`
            const keyStock = `${item.id}_stock_quantity`
            store[keyQty] = store[keyQty] ?? item.quantity ?? 0
            store[keyStock] = item?.store_id_stock_quantity_map?.[store?.store_id] ?? 0

            // 如果这个商品不在门店的 detail 中，才添加进去
            if (!detailIds.has(item.id)) {
              updatedDetail.push({
                id: item.id,
                item_id: item.id,
                item_code: item.code,
                purchase_spec: item.purchase_spec,
                item_name: item.name,
                quantity: 0,
                stock_quantity: item?.store_id_stock_quantity_map?.[store?.store_id] ?? 0
              })
            }
          })

          return {
            ...store,
            detail: updatedDetail,
            _click: false,
            _edit: false
          }
        })
      })
    }
  }
  const handleBatchStore = async () => {
    const list = await XlbBasicData({
      isMultiple: true,
      url: '/erp-mdm/hxl.erp.store.short.page',
      selectedList: rowData?.map((v) => ({ ...v, id: v.store_id })),
      primaryKey: 'id',
      type: 'store',
      dataType: 'lists',
      data: {
        status: true,
        center_flag: false
      },
      fieldNames: {
        idKey: 'id',
        nameKey: 'name'
      }
    })
    if (list) {
      const newList = list.map((v: any) => {
        return {
          ...v,
          store_id: v.id,
          store_name: v.store_name,
          store_type: v.store_group ? v.store_group.name : '',
          store_type_id: v.store_group ? v.store_group.id : '',
          detail: [],
          _click: false,
          _edit: false
        }
      })
      newList.map((item, index) => {
        if (item.newRow) {
          newList.splice(index, 1)
        }
      })
      setRowData([...newList])
    }
  }

  // 删除
  const deleteGoods = () => {
    if (selectedGoodList.length === 0 && selectedStoreList.length === 0) {
      return XlbMessage.error('请先选择要删除的门店或商品！')
    }
    // 处理门店删除
    if (selectedStoreList.length > 0) {
      setRowData((prevRowData) => {
        const newRowData = prevRowData.filter((item) => !selectedStoreList?.includes(item.id))
        // console.log(newRowData, 'newRowData ww')
        if (!newRowData?.length) {
          // 店删光时品也要全删除
          setGoodList([])
          setItemArr([itemArr[0]])
        }
        return newRowData
      })
      setSelectedStoreList([])
    }

    // 处理商品删除
    if (selectedGoodList.length > 0) {
      // 处理 itemArr
      const newItemArr = itemArr.filter((item: any) => !selectedGoodList.includes(item.id))

      setItemArr(newItemArr)
      setGoodList(newItemArr.slice(1))

      // 处理 rowData
      setRowData((prevRowData) => {
        return prevRowData.map((row) => {
          const newRow = { ...row }

          // 删除与选中商品相关的字段
          selectedGoodList.forEach((id) => {
            delete newRow[`${id}_quantity`]
            delete newRow[`${id}_stock_quantity`]
          })

          // 从 detail 中移除对应商品
          if (Array.isArray(newRow.detail)) {
            newRow.detail = newRow.detail.filter((item: any) => !selectedGoodList.includes(item.id))
          }

          return newRow
        })
      })

      // 清空选择列表
      setSelectedGoodList([])
    }
  }

  // 取消
  const onCancel = () => {
    formRef?.current?.resetFields()
    formUpdate.resetFields()
    setGoodList([])
    setSelectedGoodList([])
    setRowData([])
    setItemArr(JSON.parse(JSON.stringify(itemTableList)))
    setOpen(false)
  }

  // 确定
  const onConfirm = async () => {
    if (!goodList?.length) {
      // 保存前校验
      XlbMessage.error(`数据不能为空`)
      return
    }
    const data = {
      type: 'STOREHOUSE_DELIVERY',
      memo: formRef?.current?.getFieldValue('memo'),
      store_order_save_req_dto_list: rowData.map((v: any) => {
        return {
          ...v,
          type: 'STOREHOUSE_DELIVERY',
          detail: null,
          details: v.detail
        }
      })
    }
    setIsLoading(true)

    const res = await batchOrderSave(data)
    setIsLoading(false)
    if (res.code === 0) {
      if (!res?.data?.length) {
        XlbMessage.success(`生成门店订单成功`)
      } else {
        XlbTipsModal({
          title: '批量制单结果',
          tips: (
            <div style={{ fontSize: '14px' }}>
              <p style={{ color: '#86909c', marginBottom: '10px' }}>
                由于部分商品不在对应门店的经营范围内，或数量设置为0，以下仓配单有部分商品未被添加到仓配单中：
              </p>
              <p>{res?.data?.join('、')}</p>
            </div>
          ),
          isCancel: false,
          okText: '知道了'
        })
      }
      onCancel()
    }
  }

  // 批量修改
  const batchUpdate = async () => {
    const bool = await XlbTipsModal({
      tips: (
        <>
          <div style={{ width: 'auto', fontSize: '16px' }} className="i-flex">
            <Form colon form={formUpdate} autoComplete="off" layout="inline">
              <Form.Item name="quantity" initialValue={0} label="数量">
                <InputNumber
                  controls={false}
                  step={0.001}
                  min={0}
                  max={9999999}
                  size={'small'}
                  style={{ width: 100, height: 27, marginLeft: 10 }}
                />
              </Form.Item>
            </Form>
          </div>
        </>
      ),
      isCancel: true,
      title: '批量设置',
      width: 360
    })
    if (bool) {
      setRowData((prevRowData) => {
        return prevRowData.map((row) => {
          console.log('1111:', row)
          const updatedRow = { ...row }
          console.log('2222', updatedRow)
          console.log('selectedGoodList====>', selectedGoodList)

          // 更新 row 的每个被选中的商品字段
          selectedGoodList.forEach((goodId) => {
            updatedRow[`${goodId}_quantity`] = formUpdate.getFieldValue('quantity')
          })
          updatedRow.detail = row.detail.map((item) => {
            if (selectedGoodList.includes(item.id)) {
              return {
                ...item,
                quantity: formUpdate.getFieldValue('quantity')
              }
            }
            return item
          })

          return updatedRow
        })
      })
      formUpdate.resetFields()
    } else {
      formUpdate.resetFields()
    }
  }

  if (rowData?.length) {
    itemArr.map((v: any) => storeStrongRender(v))
  }
  itemArr[0].title = (
    <Checkbox
      disabled={
        !goodList?.length
        // goodList.filter((v) => v.obj.generate_replenishment_order_state).length === goodList.length
      }
      value={selectedGoodList.length === goodList.length ? 1111 : 9999}
      checked={goodList.length && selectedGoodList.length === goodList.length}
      onChange={(e: any) => onChange(e, goodList, null)}
    >
      商品名称/商品代码
    </Checkbox>
  )

  return (
    <XlbModal
      open={open}
      centered
      // @ts-ignore
      width={'90%'}
      isCancel={true}
      className={styles.dragModel}
      wrapClassName="xlbDialog"
      title={'仓配单批量制单'}
      confirmLoading={isLoading}
      onCancel={() => onCancel()}
      onOk={() => onConfirm()}
    >
      <div ref={modalRef} style={{ marginTop: '12px' }} className={styles.formWrapCus}>
        <XlbProForm
          formList={[
            {
              id: 'commonInput',
              label: '单据备注',
              name: 'memo',
              fieldProps: {
                width: 624
              }
            }
          ]}
          ref={formRef}
        />
        <div style={{ marginLeft: 16 }}>
          <XlbButton.Group>
            <XlbButton
              label="添加门店"
              onClick={() => handleBatchStore()}
              type="primary"
              disabled={rowData?.some((i) => i?.detail?.length > 0)}
              icon={<XlbIcon name="jia" />}
            />
            <XlbButton
              label="添加商品"
              disabled={!rowData?.length}
              type="primary"
              onClick={() => handleAddClick()}
              icon={<XlbIcon name="jia" />}
            />
            <XlbButton
              label="删除"
              disabled={selectedGoodList.length === 0 && selectedStoreList.length === 0}
              onClick={() => deleteGoods()}
              type="primary"
              icon={<XlbIcon name="shanchu" />}
            />

            <XlbButton
              label="批量修改"
              disabled={selectedGoodList.length === 0}
              onClick={() => batchUpdate()}
              type="primary"
              icon={<XlbIcon name="xiugai" />}
            />
          </XlbButton.Group>
          {/* table */}
          <div className={rowData.length ? styles.table_box : ''} style={{ marginTop: 8 }}>
            <XlbTable
              dataSource={rowData}
              columns={itemArr}
              rowKey="store_id"
              key={rowData.length + '——' + goodList.length}
              isLoading={isLoading}
              emptyCellHeight={document.body.clientHeight - 335}
              style={{ height: 'calc(100vh - 335px - 120px)' }}
              hideOnSinglePage={true}
              selectMode="multiple"
              selectedRowKeys={selectedStoreList}
              onSelectRow={(value) => {
                setSelectedStoreList(value)
              }}
            />
          </div>
        </div>
      </div>
    </XlbModal>
  )
}

export default DeliveryOrderIndex
