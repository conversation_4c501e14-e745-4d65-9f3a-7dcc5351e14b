import { XlbFetch as ErpRequest } from '@xlb/utils';

//获取数据

//审核
export const auditInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.audit', data);
};
export const storeorderCopy = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.copy', data);
};
export const batchaudit = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.batchaudit', data);
};
export const invalid = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.invalid', data);
};
export const confirm = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.confirm', data);
};
//合并
export const mergeInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.merge', data);
};
// 批量制单
export const batchOrderSave = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.batchsave', data);
};
// 打印
export const postPrint = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.print', data);
};
// 生成缺货补货单
export const replenishment = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.replenishment', data);
};