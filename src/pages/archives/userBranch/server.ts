import {XlbFetch as ErpRequest } from '@xlb/utils'
import { UserDeptFindReqDTO, UserDeptSaveReqDTO, UserDeptUpdateReqDTO } from './type'

export default {
  // 获取数据
  getData: async (data: UserDeptFindReqDTO) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.userdept.find', { data })
  },
  // 新增
  add: async (data: UserDeptSaveReqDTO) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.userdept.save', { data })
  },

  // 删除
  delete: async (data: { id: number }) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.userdept.delete', { data })
  },

  // 修改
  update: async (data: UserDeptUpdateReqDTO) => {
    return await ErpRequest.post('/erp-mdm/hxl.erp.userdept.update', { data })
  }

}