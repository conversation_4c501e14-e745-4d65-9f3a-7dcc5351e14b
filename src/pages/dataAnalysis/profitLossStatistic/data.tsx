import { columnWidthEnum } from '@/data/common/constant';
import { XlbTableColumnProps } from '@xlb/components';

export const classLevelList = [
  {
    label: '按当前类别',
    value: '0',
  },
  {
    label: '按顶级类别',
    value: '1',
  },
];

// 损益结果
export const syResultArr = [
  {
    label: '盘盈',
    value: '0',
  },
  {
    label: '盘亏',
    value: '1',
  },
];

//生成补货单
export const profitLossTypes = [
  {
    label: '库存盘点',
    value: '0',
  },
  {
    label: '库存调整',
    value: '1',
  },
  {
    label: '库存成本调整',
    value: '2',
  },
];
// 时间类型
export const TimeTypes: any[] = [
  {
    label: '制单时间',
    value: 0,
  },
  {
    label: '审核时间',
    value: 1,
  },
  {
    label: '处理时间',
    value: 2,
  },
  {
    label: '批复时间',
    value: 3,
  },
];
// 时间类型
export const summaryTypes = [
  {
    value: '0',
    label: '门店',
  },
  {
    value: '1',
    label: '商品档案',
  },
  {
    value: '2',
    label: '商品类别',
  },
  {
    value: '3',
    label: '商品部门',
  },
  {
    value: '4',
    label: '调整原因',
  },
];
export const formList: any[] = [
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    hidden: false,
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    label: '汇总条件',
    name: 'summary_types',
    type: 'select',
    allowClear: false,
    check: true,
    multiple: true,
    initialValue: ['0'],
    options: summaryTypes,
  },
  {
    label: '类别等级',
    name: 'category_level',
    type: 'select',
    allowClear: false,
    multiple: false,
    options: [],
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: false,
    hidden: false,
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        storeStatus: true,
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '仓库',
    name: 'storehouse_ids',
    type: 'select',
    options: [],
    allowClear: true,
    multiple: true,
    linkId: 'storehouse_ids_options',
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,
    multiple: true,
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    allowClear: true,
    treeModalConfig: {
      // @ts-ignore
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      //   data: {
      //     enabled: true,
      //   },
      width: 360, // 模态框宽度
    },
  },
  {
    label: '商品部门',
    name: 'item_dept_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'productDept',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
    },
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    allowClear: true,
    options: [
      {
        label: '基本单位',
        value: 'BASIC',
      },
      {
        label: '库存单位',
        value: 'STOCK',
      },
      {
        label: '批发单位',
        value: 'WHOLESALE',
      },
      {
        label: '采购单位',
        value: 'PURCHASE',
      },
      {
        label: '配送单位',
        value: 'DELIVERY',
      },
    ],
  },
  {
    label: '损益类型',
    name: 'report_type',
    type: 'select',
    allowClear: true,
    check: true,
    // multiple: true,
    options: profitLossTypes,
  },
  {
    label: '损益结果',
    name: 'result_type',
    type: 'select',
    allowClear: true,
    check: true,
    multiple: false,
    options: syResultArr,
    hidden: true,
  },
  {
    label: '调整原因',
    name: 'reason_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'storeAdjustReasons',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
    },
    // type: 'dialog',
    // clear: true,
    // check: true,
    hidden: true,
  },
];
export const originTableList: XlbTableColumnProps<any> = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },

  {
    name: '损益数量',
    code: 'quantity',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益金额',
    code: 'money',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益金额(去税)',
    code: 'no_tax_money',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益零售金额',
    code: 'retail_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
];
export const tableList: XlbTableColumnProps<any> = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },

  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品部门',
    code: 'item_dept_name',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '损益数量',
    code: 'quantity',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益金额',
    code: 'money',
    width: 90,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益金额(去税)',
    code: 'no_tax_money',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益零售金额',
    code: 'retail_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '调整原因',
    code: 'reason_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
];
export const tableListDetail: XlbTableColumnProps<any> = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '单据号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 160,
    features: { sortable: true, format: 'TIME' },
    align: 'left',
  },
  {
    name: '门店',
    code: 'store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 140,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 120,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '商品部门',
    code: 'item_dept_name',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '损益类别',
    code: 'report_name',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '调整原因',
    code: 'reason_name',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '损益结果',
    code: 'result_name',
    width: 100,
    features: { sortable: true },
    align: 'left',
  },
  {
    name: '损益数量',
    code: 'quantity',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '基本单位',
    code: 'unit',
    width: 90,
    hidden: false,
    features: { sortable: true },
    align: 'right',
  },
  // {
  //   name: '库存单位',
  //   code: 'unit',
  //   hidden: true,
  //   width: 90,
  //   features: { sortable: true },
  //   align: 'right',
  // },
  // {
  //   name: '批发单位',
  //   code: 'unit',
  //   width: 90,
  //   hidden: true,
  //   features: { sortable: true },
  //   align: 'right',
  // },
  // {
  //   name: '采购单位',
  //   code: 'unit',
  //   width: 90,
  //   hidden: true,
  //   features: { sortable: true },
  //   align: 'right',
  // },
  // {
  //   name: '配送单位',
  //   code: 'unit',
  //   width: 90,
  //   hidden: true,
  //   features: { sortable: true },
  //   align: 'right',
  // },
  {
    name: '损益金额',
    code: 'money',
    width: 110,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益金额(去税)',
    code: 'no_tax_money',
    width: 140,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '损益零售金额',
    code: 'retail_money',
    width: 120,
    features: { sortable: true },
    align: 'right',
  },
];

export const dateReasonsColumns: any = [
  {
    name: '序号',
    code: 'index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '条件名称',
    code: 'name',
    width: 180,
    features: { sortable: true },
    align: 'left',
  },
];