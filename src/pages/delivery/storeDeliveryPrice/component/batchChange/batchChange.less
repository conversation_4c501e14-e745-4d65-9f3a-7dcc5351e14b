.storeDeliveryPriceBatch{

}
.box {
  position: relative;
  // width: 860px;
  margin: 20px 10px;
  padding: 10px;
  border: 1px solid #D8D8D8;
  border-radius: 5px;
  .title {
    position: absolute;
    top: -13px;
    left: 20px;
    padding: 0 13px;
    background: white;
  }
  :global .ant-form-item {
    display: inline-block;
    margin: 0 30px 0 10px;
    .ant-select-selector {
      height: 28px !important;
      line-height: 28px !important;
      .ant-select-selection-search{
        height: 26px;
      }
      .ant-select-selection-item{
        height: 26px;
      }
    }
  }
  :global .ant-space-item {
    display: inline-block;
  }
  :global label.ant-checkbox-wrapper.ant-checkbox-wrapper-in-form-item{
    width: 154px;
  }
  :global .ant-radio-wrapper {
    line-height: 26px;
  }
  :global .ant-input-affix-wrapper > .ant-input {
    height: 26px;
  }
  // :global .ant-input-affix-wrapper {
  //   margin: 0 10px;
  // }
  :global .ant-select {
    display: inline-block;
    width: 140px;
  }
  :global .ant-input-suffix{
    height: 26px;
  }
  

}

  .dragModel {
    :global .ant-modal-body {
        padding: 0;
    }
}
  