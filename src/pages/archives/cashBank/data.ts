import {columnWidthEnum} from "@/data/common/constant";
import { XlbTableColumnProps } from '@xlb/components';

export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '营业外收支项目名称',
    code: 'name',
    width: 260,
    features: { sortable: true }
  },
  {
    name: '状态',
    code: 'status',
    width: 100,
    // features: { sortable: true }
  }
]
