import { useBaseParams } from '@/hooks/useBaseParams';
import { exportPage } from '@/services/system';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { history } from '@@/core/history';
import {
  XlbBasicData,
  XlbBlueBar,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbSelect,
  XlbTable,
  XlbTableColumnProps,
  XlbTipsModal,
  XlbTree,
} from '@xlb/components';
import { Form, Input, Layout, message, Select } from 'antd';
import { cloneDeep } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { goodsType, replenishTemplateArr, types } from '../data';
import Api from '../server';
import ModifyHistory from './component/modifyHistory';
import styles from './index.less';
const { Sider, Content } = Layout;
const { Option } = Select;
const Item: React.FC = (props: any) => {
  const { record, onBack } = props;
  const enable_organization = useBaseParams(
    (state) => state.enable_organization,
  );
  const [isLoading, setisLoading] = useState<boolean>(false);
  const [rowData, setRowData] = useState<any[]>([]);
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(replenishTemplateArr)),
  );
  const [historyVisible, setHistoryVisible] = useState<boolean>(false); //修改记录
  const [chooseList, setChooseList] = useState<any[]>([]);
  const [pagin, setPagin] = useState({
    pageSize: 200000,
    pageNum: 1,
    total: 0,
  });
  const formBox = useRef<HTMLDivElement | null>(null);
  const [storeModal, setStoreModal] = useState<any>({
    modalVisible: false,
    storeItem: {},
    modalState: {
      modalType: 'store',
    },
  });
  const [form] = Form.useForm();
  const [preId, setId] = useState(null);

  const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
  const tableRef = useRef(null);

  // 读取
  const getRead = async (id: any) => {
    setisLoading(true);
    const params = {
      id: id,
    };
    const res = await Api.getItem(params);
    setisLoading(false);
    if (res.code == 0) {
      form.setFieldsValue({
        ...res.data,
        store_names: res.data?.stores?.map((v: any) => v.store_name).join(','),
        store_ids: res.data?.stores?.map((v: any) => v.store_id),
      });
    }
  };

  const getGoods = async (id: any) => {
    setisLoading(true);
    const params = {
      replenish_template_id: id,
    };
    const res = await Api.goodsItem(params);
    setisLoading(false);
    if (res.code == 0) {
      const data = res.data?.map((item) => {
        return {
          ...item,
          fidKey: item?.type + '_' + item?.item_id,
          id: item?.item_id,
          code: item?.item_code,
          item_bar_code: item?.item_bar_code,
          name: item?.item_name,
        };
      });
      setRowData(data || []);
      setPagin({
        ...pagin,
        pageNum: 1,
        total: data?.length,
      });
    }
  };

  //递归Id
  const getId = (tree: any, ids: any = []) => {
    for (const item of tree) {
      ids.push(item.id);
      if (item.children && item.children.length > 0) {
        getId(item.children, ids);
      }
    }
    return ids;
  };

  // 保存
  const saveStore = async () => {
    if (!(await form.validateFields())) return;
    const details = rowData.map((v: any, i: number) => {
      return {
        ...v,
        value: v?.value && v?.value != 'NULL' ? v.value : '0',
        delivery_price: v?.delivery_price ? v.delivery_price : '0',
        type: v?.type ? v.type : 'NULL',
      };
    });
    setisLoading(true);
    const params = {
      name: form.getFieldValue('name'),
      store_ids: form.getFieldValue('store_ids')
        ? form.getFieldValue('store_ids')
        : null,
      unit_type: form.getFieldValue('unit_type'),
      items: details,
    };
    const res = !preId
      ? await Api.add(params)
      : await Api.update({ ...params, id: preId });
    setisLoading(false);
    if (res.code == 0) {
      message.success('保存成功');
      onBack(true);
    }
  };

  //添加
  const addGoods = async () => {
    const list = await XlbBasicData({
      type: 'goods',
      url: '/erp-mdm/hxl.erp.item.short.page',
      isMultiple: true,
      dataType: 'lists',
      primaryKey: 'id',
      resetForm: true,
      nullable: false,
      selectedList: rowData || [],
      data: {
        status: 1,
      },
    });
    if (list) {
      const labelList = list.map(
        (item: any) =>
          (item = {
            ...item,
            value: null,
            delivery_price: '0.0000',
            type: null,
            fidKey: item.type + '_' + item?.id,
            item_id: item?.id,
            item_code: item?.code,
            item_bar_code: item?.bar_code,
            item_name: item?.name,
            unit:
              form.getFieldValue('unit_type') == 'DELIVERY'
                ? item?.delivery_unit
                : item?.unit,
            purchase_price:
              form.getFieldValue('unit_type') == 'DELIVERY'
                ? Number(item?.purchase_price * item?.delivery_ratio).toFixed(4)
                : Number(item?.purchase_price),
            id: item?.id,
            name: item?.name,
          }),
      );
      setRowData([...rowData, ...labelList]);
      setPagin({
        ...pagin,
        pageNum: 1,
        total: [...rowData, ...labelList]?.length,
      });
    }
  };

  const handleOk = async (list: any) => {
    if (storeModal.modalState.modalType === 'item') {
    } else if (storeModal.modalState.modalType === 'store') {
      form.setFieldsValue({
        store_ids: list.map((v: any) => v.id),
        store_names: list.map((v: any) => v.store_name).join(','),
      });
    }

    handleCancel();
  };

  //弹框取消事件
  const handleCancel = () => {
    setStoreModal({
      ...storeModal,
      modalVisible: false,
    });
  };

  //删除
  const deleteStore = async () => {
    // console.log(chooseList,'deleteStore')
    await XlbTipsModal({
      tips: `是否删除选中的${chooseList?.length}个商品?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        // console.log(chooseList,rowData,'删除')
        const chooseListIds = chooseList?.map((i) =>
          i?.split('_')?.[1]?.toString(),
        );
        let rowDataTmp = cloneDeep(rowData);
        rowDataTmp = rowDataTmp?.filter(
          (t) =>
            !chooseListIds?.includes(t?.fidKey.split('_')?.[1]?.toString()),
        );
        setRowData([...rowDataTmp]);

        setChooseList([]);
        return true;
      },
    });
  };

  const importItemConfirm = async (e: any) => {
    if (e?.state) {
      const code = rowData.map((v: any) => v.item_code);
      const repeatArr = e.content?.filter((v: any) =>
        code.includes(v.item_code),
      );

      // 重复数据被替换
      repeatArr.forEach((item: any) => {
        const index = rowData.findIndex((e) => e.item_code == item.item_code);
        if (index > -1) {
          rowData[index] = item;
        } else {
          rowData.push(item);
        }
      });

      const newArr = e.content?.filter((v: any) => !code.includes(v.item_code));
      if (repeatArr.length || newArr.length) {
        message.success(
          '成功新增' + newArr.length + '条，更新' + repeatArr.length + '条',
        );
      }
      const labelList = newArr.map(
        (item: any) =>
          (item = {
            ...item,
            value: item?.value ? item?.value : '0.0000',
            delivery_price: item?.delivery_price
              ? item?.delivery_price
              : '0.0000',
            type: item?.type ? item?.type : 'NULL',
            purchase_price:
              form.getFieldValue('unit_type') == 'DELIVERY'
                ? Number(item?.purchase_price * item?.delivery_ratio).toFixed(4)
                : Number(item?.purchase_price),
            id: item?.id,
            item_bar_code: item?.bar_code || item?.item_bar_code,
            name: item?.name,
          }),
      );
      setRowData([...rowData, ...labelList]);
      rowData?.forEach((item: any, index: number) => {
        item.fidKey = item?.type + '_' + item?.item_id;
      });
      setPagin({
        ...pagin,
        pageNum: 1,
        total: [...rowData, ...labelList]?.length,
      });
    } else {
      await XlbTipsModal({
        tips: (
          <>
            <div className="danger">以下商品有误, 请核实！</div>
            <div>
              {e.error_messages?.map((v, i) => {
                return <div key={i}>{v}</div>;
              })}
            </div>
          </>
        ),
      });
    }
  };

  // 导出
  const exportItem = async () => {
    const data = {
      id: preId,
    };
    setisLoading(true);
    const res = await exportPage(
      '/erp/hxl.erp.replenishtemplate.item.export',
      data,
      { responseType: 'blob' },
    );
    setisLoading(false);
    const download = new Download();
    download.filename = '补货模板.xlsx';
    download.xlsx(res?.data);
  };

  const toDecimal = (x: any, fixed: any) => {
    let f = parseFloat(x);
    if (isNaN(f)) {
      return 0;
    }
    let mathNum = 0;
    switch (fixed) {
      case 2:
        mathNum = 100;
        break;
      case 3:
        mathNum = 1000;
        break;
      default:
        mathNum = 10000;
        break;
    }
    f = Math.round(x * mathNum) / mathNum;
    let s = f.toString();
    let rs = s.indexOf('.');
    if (rs < 0) {
      rs = s.length;
      s += '.';
    }
    while (s.length <= rs + fixed) {
      s += '0';
    }
    return s;
  };

  const itemRender = (item: any) => {
    switch (item.code) {
      // case '_index':
      //   item.render = (value: any, record: any, index: number) => {
      //     return value === '合计' ? (
      //       <div className="info overwidth">{value}</div>
      //     ) : (
      //       <div className="info overwidth">{(pagin.pageNum - 1) * pagin.pageSize + 1 + index}</div>
      //     )
      //   }
      //   break
      case 'item_type':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {goodsType.find((e) => e.value === value)?.label}
            </div>
          );
        };
        break;
      case 'purchase_price':
        item.name = enable_organization ? '组织采购价' : '档案采购价';
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {Number(value || 0).toFixed(4)}
            </div>
          );
        };
        break;
      case 'type':
        item.render = (value: any, record: any, index: number) => {
          return hasAuth(['补货模板/配送价', '查询']) ? (
            <div className="overwidth">
              <Select
                style={{ width: '100%' }}
                defaultValue={value}
                onChange={(e) => {
                  record.type = e;
                  if (e === 'NULL') {
                    record.delivery_price = '0.0000';
                    record.value = '0.0000';
                  } else if (e === 'RATIO') {
                    record.delivery_price = Number(
                      record.purchase_price * (1 + record.value / 100),
                    ).toFixed(4);
                  } else if (e === 'MONEY') {
                    record.delivery_price = Number(
                      record.purchase_price + Number(record.value),
                    ).toFixed(4);
                  } else if (e === 'FIXED_MONEY') {
                    record.delivery_price = Number(record.value).toFixed(4);
                  }
                }}
              >
                {types?.map((item: any) => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            </div>
          ) : (
            <div className="info overwidth">{'——'}</div>
          );
        };
        break;
      case 'value':
        item.render = (value: any, record: any, obj) => {
          return hasAuth(['补货模板/配送价', '查询']) ? (
            hasAuth(['补货模板', '编辑']) &&
            record._click &&
            record.type !== 'NULL' &&
            record.type !== null ? (
              <Input
                key={record[item.code]}
                className="full-box"
                autoComplete={'off'}
                id={item.code + '-' + obj.index.toString()}
                defaultValue={toDecimal(value, 4)}
                onFocus={(e) => e.target.select()}
                onChange={(e) => (record.value = e.target.value)}
                onBlur={(e) => {
                  if (
                    !regPos.test(e.target.value) ||
                    Number(e.target.value) < 0 ||
                    Number(e.target.value) > 999999999.9999
                  ) {
                    message.error('数值范围：0.0000≤录入数值≤999999999.9999');
                    record.value = '0.0000';
                    return;
                  } else {
                    record.value = Number(record.value).toFixed(4);
                    if (record.type === 'NULL') {
                      record.delivery_price = '0.0000';
                      record.value = '0.0000';
                    } else if (record.type === 'RATIO') {
                      record.delivery_price = Number(
                        Number(record.purchase_price) *
                          (1 + record.value / 100),
                      ).toFixed(4);
                    } else if (record.type === 'MONEY') {
                      console.log(record, '0000000000');
                      record.delivery_price = Number(
                        Number(record.purchase_price) + Number(record.value),
                      ).toFixed(4);
                    } else if (record.type === 'FIXED_MONEY') {
                      record.delivery_price = Number(record.value).toFixed(4);
                    }
                  }
                }}
                style={{ textAlign: 'right' }}
              />
            ) : (
              <div className="info overwidth">
                {record.type === 'NULL' || record.type === null
                  ? '——'
                  : Number(value)?.toFixed(4)}
              </div>
            )
          ) : (
            <div className="info overwidth">{'——'}</div>
          );
        };
        break;
      case 'delivery_price':
        item.render = (value: any, record: any) => {
          return (
            <div className="info overwidth">
              {record.type === 'NULL' || record.type === null
                ? '——'
                : Number(value)?.toFixed(4)}
            </div>
          );
        };
        break;
      // default:
      //   return (item.render = (value: any, record: any, index: number) => (
      //     <div className="info overwidth">{value}</div>
      //   ))
    }
  };

  useEffect(() => {
    if (record?.id > 0) {
      setId(record.id);
      getRead(record.id);
      getGoods(record.id);
    } else {
      form.setFieldValue('unit_type', 'DELIVERY');
    }

    itemArr.map((v) => itemRender(v));
    setItemArr([...itemArr]);
  }, []);

  return (
    <div
      style={{
        padding: 12,
        height: 'calc(100vh - 120px)',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <XlbButton.Group>
        {hasAuth(['补货模板', '编辑']) && (
          <XlbButton
            type="primary"
            label="保存"
            loading={isLoading}
            onClick={saveStore}
            icon={<XlbIcon size={16} name="baocun" />}
          />
        )}
        {hasAuth(['补货模板', '编辑']) && (
          <XlbButton
            type="primary"
            label="返回"
            onClick={() => onBack()}
            icon={<XlbIcon size={16} name="fanhui" />}
          />
        )}
      </XlbButton.Group>

      <div
        className={'form_header_box'}
        ref={formBox}
        style={{ marginTop: '10px' }}
      >
        <div style={{ display: 'inline-block' }}>
          <Form autoComplete="off" layout="inline" colon={true} form={form}>
            <Form.Item
              label="模板名称："
              name="name"
              rules={[{ required: true, message: '模板名称不能为空' }]}
            >
              <XlbInput
                style={{ width: 180 }}
                maxLength={10}
                size="small"
                allowClear
              />
            </Form.Item>
            <Form.Item label="应用门店" name="store_ids">
              {/* <Input
                style={{ width: 180 }}
                size="small"
                placeholder="请选择应用门店"
                suffix={<SearchOutlined style={{ color: '#666666' }} />}
                onClick={() =>
                  setStoreModal({
                    ...storeModal,
                    modalState: {
                      isMultiple: true,
                      modalType: 'store',
                      filterData: {
                        center_flag: false,
                        filter_exist_replenish_template: true,
                        replenish_template_id: (history.location.state as any).id
                      },
                      hasChosenItem: form.getFieldValue('store_names')
                        ? form.getFieldValue('store_ids')
                        : ''
                    },
                    modalVisible: true
                  })
                }
              /> */}
              <XlbInputDialog
                style={{ width: 180 }}
                dialogParams={{
                  isMultiple: true,
                  dataType: 'lists',
                  type: 'store',
                  data: {
                    center_flag: false,
                    filter_exist_replenish_template: true,
                    replenish_template_id: (history.location.state as any)?.id,
                  },
                }}
                fieldNames={{
                  idKey: 'id',
                  nameKey: 'store_name',
                }}
              ></XlbInputDialog>
            </Form.Item>
            <Form.Item label="单位" name="unit_type">
              <XlbSelect
                defaultValue={'DELIVERY'}
                size="small"
                style={{ width: 180 }}
                disabled={rowData?.length > 0}
              >
                <XlbSelect.Option value={'DELIVERY'}>配送单位</XlbSelect.Option>
                <XlbSelect.Option value={'BASIC'}>基本单位</XlbSelect.Option>
              </XlbSelect>
            </Form.Item>
          </Form>
        </div>
      </div>

      <XlbBlueBar title={'商品明细'} hasMargin />

      <Layout className={styles.layoutBg}>
        <Sider width={230}>
          <div className={styles.leftGoods}>
            <XlbTree
              url="/erp-mdm/hxl.erp.category.find"
              type="search"
              dataType={'lists'}
              onSelect={async (node, keys, meta) => {
                setisLoading(true);
                const data = {
                  ...form.getFieldsValue(),
                  page_number: pagin.pageNum - 1,
                  page_size: pagin.pageSize,
                  item_category_ids: keys || [],
                  replenish_template_id: record?.id,
                };
                const res = await Api.goodsItem(data);
                setisLoading(false);
                if (res.code == '0') {
                  setRowData(res?.data || []);
                  setPagin({
                    ...pagin,
                    pageNum: 1,
                    total: res.data?.length,
                  });
                }
              }}
              renderText={(data: any) => (
                <span>{data?.category_code + '|' + data?.name}</span>
              )}
            />
          </div>
        </Sider>
        <Content>
          <div className={styles.right}>
            <div style={{ marginBottom: 10, marginLeft: 10 }}>
              <XlbButton.Group>
                {hasAuth(['补货模板', '编辑']) && (
                  <XlbButton
                    type="primary"
                    label="添加"
                    onClick={() => {
                      addGoods();
                    }}
                    icon={<XlbIcon size={16} name="jia" />}
                  />
                )}

                {hasAuth(['补货模板', '编辑']) && (
                  <XlbButton
                    type="primary"
                    label="删除"
                    disabled={!chooseList.length}
                    onClick={deleteStore}
                    icon={<XlbIcon size={16} name="shanchu" />}
                  />
                )}
                {hasAuth(['补货模板', '导入']) && (
                  <XlbButton
                    type="primary"
                    label="导入"
                    onClick={async () => {
                      await XlbImportModal({
                        importUrl: `${process.env.BASE_URL}/erp/hxl.erp.replenishtemplate.item.import?unit_type=${form.getFieldValue(
                          'unit_type',
                        )}`,
                        templateUrl: `${process.env.BASE_URL}/erp/hxl.erp.replenishtemplate.template.download`,
                        templateName: '补货模板商品导入',
                        callback: (res) => {
                          if (res.code === 0) {
                            importItemConfirm(res?.data);
                          }
                        },
                      });
                    }}
                    icon={<XlbIcon size={16} name="daoru" />}
                  />
                )}
                {hasAuth(['补货模板', '导出']) && (
                  <XlbButton
                    type="primary"
                    label="导出"
                    disabled={!rowData.length || isLoading || !preId}
                    onClick={exportItem}
                    icon={<XlbIcon size={16} name="daochu" />}
                  />
                )}

                <XlbButton
                  type="primary"
                  label="修改记录"
                  onClick={() => setHistoryVisible(true)}
                  icon={<XlbIcon size={16} name="shenqing" />}
                />
              </XlbButton.Group>
            </div>
            <XlbTable
              ref={tableRef}
              primaryKey="fidKey"
              isLoading={isLoading}
              style={{ height: 'calc(100vh - 323px)' }}
              selectMode="multiple"
              columns={itemArr}
              dataSource={rowData}
              onSelectRow={(value, data) => {
                setChooseList(value);
              }}
              total={rowData?.length}
              showSearch
              estimatedRowHeight={43}
            />
          </div>
        </Content>
      </Layout>
      <ModifyHistory
        visible={historyVisible}
        id={preId}
        setVisible={setHistoryVisible}
        // onCancel={() => setHistoryVisible(false)}
      />
    </div>
  );
};

export default Item;
