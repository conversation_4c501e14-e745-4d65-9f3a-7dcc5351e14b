interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/xlb_erp/heightLowInventoryGoods/index',
    component: '@/pages/dataAnalysis/heightLowInventoryGoods/index',
    title: '高低库存商品',
    subTitle: '库存分析',
    subMenu: 'dataqueries',
    tabClass: 'heightLowInventoryGoods'
  },
  {
    path: '/xlb_erp/profitLossStatistic/index',
    component: '@/pages/dataAnalysis/profitLossStatistic/index',
    title: '损益统计报表',
    subTitle: '库存分析',
    subMenu: 'dataqueries',
    tabClass: 'profitLossStatistic',
  }
];

export { routeList as dataAnalysisRouteList };