import { Modal, Form, message } from 'antd'
import { useEffect, useState } from 'react'
import { LStorage } from '@/utils/storage'
import Api from '../server'
import NiceModal from '@ebay/nice-modal-react'
import { XlbInputDialog,XlbModal,XlbTipsModal } from '@xlb/components'

const CopyChange = (props: any) => {
  const { tabKey } = props
  const { visible, resolve, hide } = NiceModal.useModal()
  const [form] = Form.useForm()
  const [loading, setloading] = useState(false)
  const userInfo = LStorage.get('userInfo')

  const handleCancel = (flag: boolean) => {
    resolve(flag)
    hide()
  }

  const handleOk = async () => {
    if (!form.getFieldValue('target_store_ids')) {
      XlbTipsModal({
        title: '请先选择修改门店',
      })
      return false
    }
    if (!form.getFieldValue('source_store_id')?.length) {
      XlbTipsModal({
        title: '请先选择参照门店',
      })
      return
    }
    const data = {
      target_store_ids: form.getFieldValue('target_store_ids'),
      source_store_id: form.getFieldValue('source_store_id')[0]
    }
    setloading(true)
    let res: any = {}
    if (tabKey == 'store_order_num') {
      res = await Api.copyStore(data)
    } else {
      res = await Api.copySpecial(data)
    }

    setloading(false)
    if (res.code === 0) {
      message.success('复制成功')
      handleCancel(true)
      form.resetFields()
    }
  }

  useEffect(() => {
    if (visible) {
      if (userInfo.store_name && userInfo.store.enable_delivery_center) {
        form.setFieldsValue({
          source_store_id: [userInfo.store_id]
        })
      }
    }
  }, [visible])
  return (
    <XlbModal
      title={tabKey == 'store_order_num' ? '门店订购属性复制' : '订购特性复制'}
      centered
      open={visible}
      maskClosable={false}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields()
        handleCancel(false)
      }}
      width={290}
      confirmLoading={loading}
    >
      <Form form={form}>
        <Form.Item label="修改门店：" name="target_store_ids">
          <XlbInputDialog
            style={{ width: '148px' }}
            dialogParams={{
              type: 'store',
              isMultiple: true,
              nullable: true,
              data: {
                center_flag: tabKey == 'store_order_num' ? null : true,
                status: true
              }
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name'
            }}
            width={260}
          />
        </Form.Item>
        <Form.Item label="参照门店：" name="source_store_id">
          <XlbInputDialog
            style={{ width: '148px' }}
            dialogParams={{
              type: 'store',
              isMultiple: false,
              nullable: true,
              data: {
                center_flag: tabKey == 'store_order_num' ? null : true,
                status: true
              }
            }}
            fieldNames={{
              idKey: 'id',
              nameKey: 'store_name'
            }}
            width={260}
          />
        </Form.Item>
      </Form>
    </XlbModal>
  )
}

export default NiceModal.create(CopyChange)
