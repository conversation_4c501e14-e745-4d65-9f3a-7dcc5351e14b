import { hasAuth } from '@/utils/kit';
import safeMath from '@/utils/safeMath';
import toFixed from '@/utils/toFixed';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbDatePicker,
  XlbInput,
  XlbModal,
  XlbSelect,
  XlbTable,
  type XlbTableColumnProps,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { itemTableList, Options1 } from './data';
const DeliveryOrderItem = (props: any) => {
  const modal = NiceModal.useModal();
  const { orderId } = props;
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [itemArrdetail] = useState<XlbTableColumnProps<any>[]>(
    itemTableList.map((i) => ({
      ...i,
      code: i.code == 'index' ? '_index' : i.code,
      hidden: i.code == 'operation' ? true : i.hidden,
    })) as any[],
  );
  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const formBox = useRef<HTMLDivElement | null>(null);
  const [footerData, setFooterData] = useState<any[]>([]);

  const [form] = XlbBasicForm.useForm();
  const [info, setInfo] = useState({
    state: 'INIT',
  });
  const InvoiceRender = (item: any) => {
    switch (item.code) {
      case 'less_status':
        return (item.render = (value: any, record: any) =>
          record.index !== '合计' && (
            <div className="info overwidth">
              {lessStateSwitch(record)}
              {/* {info.state === 'INIT' ? '' : lessStateSwitch(record)} */}
            </div>
          ));
      case 'less_quantity':
        return (item.render = (value: any, record: any) =>
          !record.newRow && (
            <div className="info overwidth">
              {info.state !== 'INIT'
                ? (
                    Number(record.quantity) - Number(record.actual_quantity)
                  ).toFixed(3)
                : '0.000'}
            </div>
          ));
      case 'money':
      case 'price':
      case 'basic_price':
        return (item.render = (value: any, record: any) => (
          <div className="info overwidth">
            {hasAuth(['门店补货单/配送价', '查询'])
              ? Number(value)?.toFixed(item.code === 'money' ? 2 : 4)
              : '****'}
          </div>
        ));
      case 'actual_quantity':
      case 'actual_basic_quantity':
        return (item.render = (value: any, record: any) =>
          !record.newRow && (
            <div className="info overwidth">{Number(value).toFixed(3)}</div>
          ));
      case 'basic_stock_quantity':
        return (item.render = (value: any, record: any) =>
          !record.newRow &&
          record.index !== '合计' && (
            <div className="info overwidth">
              {value !== null
                ? toFixed(
                    safeMath.divide(value, record.delivery_ratio),
                    'QUANTITY',
                    true,
                  )
                : '——'}
            </div>
          ));
    }
  };
  const lessStateSwitch = (v: any) => {
    const less_quantity = v.quantity - v.actual_quantity;
    if (less_quantity === 0) {
      return <span style={{ color: '#47CC28' }}>正常</span>;
    }
    if (less_quantity === v.quantity) {
      return <span style={{ color: '#F53B3B' }}>无货</span>;
    }
    if (less_quantity !== 0 && less_quantity !== v.quantity) {
      return <span style={{ color: '#FFAA31' }}>缺货</span>;
    }
  };

  //读取信息
  const readinfo = async (fid: any, summary: boolean = false) => {
    setIsLoading(true);
    const data = {
      fid: fid,
      summary: summary,
    };
    const res = await XlbFetch.post('/erp/hxl.erp.requestorder.read', data);
    setIsLoading(false);
    if (res.code === 0) {
      let money = 0,
        actual_quantity = 0,
        quantity = 0,
        actual_basic_quantity = 0,
        price = 0;
      res.data.details.map((v: any) => {
        money += parseFloat(v.money);
        actual_quantity += parseFloat(v.actual_quantity);
        quantity += parseFloat(v.quantity);
        price += parseFloat(v.price);
        actual_basic_quantity += parseFloat(v.actual_basic_quantity);
      });
      footerData[0] = {
        index: '合计',
        money: hasAuth(['门店补货单/配送价', '查询'])
          ? money.toFixed(2)
          : '****',
        quantity: quantity.toFixed(3),
        actual_quantity: actual_quantity.toFixed(3),
        less_quantity: (quantity - actual_quantity).toFixed(3),
        price: hasAuth(['门店补货单/配送价', '查询'])
          ? price.toFixed(4)
          : '****',
        actual_basic_quantity: actual_basic_quantity.toFixed(3),
      };
      setFooterData(footerData);
      setInfo({ ...info, state: res.data.state });
      setRowData(res.data.details);
      setPagin({
        ...pagin,
        total: res.data.details.length,
      });
      form.setFieldsValue({
        order_type: res.data.order_type,
        item_dept_names: res.data.item_dept_names,
        in_store_name: res.data.in_store_name,
        store_name: res.data.store_name,
        store_id: res.data.store_id,
        storehouse_name: res.data.storehouse_name,
        out_store_name: res.data.out_store_name,
        fid: res.data.fid,
        memo: res.data.memo,
        create_by: res.data.create_by,
        audit_by: res.data.audit_by,
        audit_time: res.data.audit_time?.slice(0, 10),
        create_time: res.data.create_time?.slice(0, 10),
        update_time: res.data.update_time?.slice(0, 10),
        valid_date: res.data.valid_date
          ? moment(res.data.valid_date)
          : undefined,
        delivery_date: res.data.delivery_date
          ? moment(res.data.delivery_date)
          : undefined,
      });
    }
  };

  useEffect(() => {
    // 设置合计行
    footerData[0] = {};
    footerData[0].index = '合计';
    footerData[0].money = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.money), 0)
      .toFixed(2);
    footerData[0].quantity = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.quantity), 0)
      .toFixed(3);
    footerData[0].tax_money = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.tax_money), 0)
      .toFixed(2);
    footerData[0].basic_quantity = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.basic_quantity), 0)
      .toFixed(3);
    footerData[0].present_quantity = rowData
      .reduce((sum, v) => sum + Number(v?.newRow ? 0 : v.present_quantity), 0)
      .toFixed(3);
    setFooterData([...footerData]);
  }, [JSON.stringify(rowData)]);

  useEffect(() => {
    itemArrdetail.map((v) => {
      v.name === '操作' ? (v.hidden = true) : null;
    });
    readinfo(orderId);
  }, []);

  itemArrdetail.map((v) => InvoiceRender(v));

  return (
    <XlbModal
      width={1000}
      open={modal.visible}
      title={'门店补货单详情'}
      isCancel={true}
      footer={null}
      onOk={async () => {
        modal.hide();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
    >
      <div
        className="modelBox col-flex"
        style={{ height: '100%', overflowY: 'auto', overflowX: 'hidden',padding: '10px 0' }}
      >
        <header>
          <div ref={formBox}>
            <XlbBasicForm colon form={form} autoComplete="off" layout="inline">
              <XlbBasicForm.Item label="补货门店" name="store_name">
                <XlbInput
                  style={{ width: '180px', padding: '0 11px' }}
                  disabled={true}
                />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="发货门店" name="out_store_name">
                <XlbSelect style={{ width: 180 }} disabled></XlbSelect>
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="发货仓库" name="storehouse_name">
                <XlbSelect style={{ width: 180 }} disabled></XlbSelect>
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="单据号" name="fid">
                <XlbInput size="small" style={{ width: '180px' }} disabled />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="单据状态">
                <XlbInput
                  disabled
                  size="small"
                  value={Options1.find((s) => s.value === info.state)?.label}
                  style={{ width: '180px' }}
                />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="商品部门" name="item_dept_names">
                <XlbInput disabled size="small" style={{ width: '180px' }} />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="配送日期" name="delivery_date">
                <XlbDatePicker style={{ width: '180px' }} disabled />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="有效日期" name="valid_date">
                <XlbDatePicker style={{ width: '180px' }} disabled />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item label="留言备注" name="memo">
                <XlbInput style={{ width: '480px' }} disabled />
              </XlbBasicForm.Item>
            </XlbBasicForm>
          </div>
        </header>
        <XlbTable
          isLoading={isLoading}
          dataSource={rowData}
          columns={itemArrdetail}
          total={rowData?.length ?? 0}
          keepDataSource={false}
          showSearch
          style={{ flex: 1 }}
        />
      </div>
    </XlbModal>
  );
};

export default DeliveryOrderItem;
