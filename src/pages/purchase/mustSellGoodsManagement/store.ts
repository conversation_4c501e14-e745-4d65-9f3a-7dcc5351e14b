import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface Iprops {
  record: any;
  setRecord: any;
  isFresh: boolean;
  setIsFresh: any;
}

export const useStore = create<Iprops, [['zustand/immer', unknown]]>(
  immer((set) => ({
    record: null,
    setRecord: (data: any) => {
      set((state) => {
        state.record = data;
      });
    },
    isFresh: false,
    setIsFresh: (data: boolean) => {
      set((state) => {
        state.isFresh = data;
      });
    },
  })),
);
