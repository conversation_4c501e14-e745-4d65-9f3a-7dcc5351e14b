export enum PurchaseType {
  CollectivePurchase = 'COLLECTIVE_PURCHASE', // 集采品
  CollectiveSale = 'COLLECTIVE_SALE', // 集售品
  GroundPurchase = 'GROUND_PURCHASE', // 地采品
  ShopPurchase = 'SHOP_PURCHASE', // 店采品
}

export const PURCHASE_TYPE_LIST = [
  {
    label: '集采品',
    value: PurchaseType.CollectivePurchase,
  },
  {
    label: '集售品',
    value: PurchaseType.CollectiveSale,
  },
  {
    label: '地采品',
    value: PurchaseType.GroundPurchase,
  },
  {
    label: '店采品',
    value: PurchaseType.ShopPurchase,
  },
];
