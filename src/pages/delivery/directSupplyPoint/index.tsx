import { hasAuth } from '@/utils';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbInputDialog,
  XlbMessage,
  XlbPageContainer,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { useRef } from 'react';
import { tableList } from './data';
import { configdelete, create } from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

const Index = () => {
  const pageRef = useRef<any>(null);
  const [form] = XlbBasicForm.useForm<any>();
  const [formEdit] = XlbBasicForm.useForm<any>();

  const prevPost = () => {
    const { ...rest } = form.getFieldsValue(true);
    return {
      ...rest,
      store_house_id: rest?.store_house_id ? [rest?.store_house_id] : undefined,
    };
  };
  const getCenterStoreList = async () => {
    if (!formEdit.getFieldValue('store_id')) {
      formEdit.setFieldValue('centeroption', []);
      return;
    }
    const res = await XlbFetch.post('/erp/hxl.erp.deliverycenterstore.find', {
      store_id: formEdit.getFieldValue('store_id')?.[0],
    });
    if (res?.code == 0) {
      const options = res?.data?.map((item: any) => ({
        label: item.center_store_name,
        value: item.center_store_id,
      }));
      formEdit.setFieldValue('centeroption', options);
    }
  };
  const getStorehouseList = async () => {
    if (!formEdit.getFieldValue('center_store_id')) {
      formEdit.setFieldValue('storehouseoption', []);
      return;
    }
    const res = await XlbFetch.post('/erp/hxl.erp.storehouse.store.find', {
      store_id: formEdit.getFieldValue('center_store_id'),
    });
    if (res?.code == 0) {
      const options = res?.data
        ?.filter((v: any) => v.distribution)
        .map((item: any) => ({
          label: item.name,
          value: item.id,
          default_flag: item.default_flag,
        }));
      formEdit.setFieldValue('storehouseoption', options);
      if (!!options?.length) {
        const defaultStoreHouse =
          options.find((item: any) => item.default_flag) || options[0];
        formEdit.setFieldValue('storehouse_id', defaultStoreHouse?.value);
      }
    }
  };
  const openItem = async () => {
    formEdit.setFieldsValue({
      supplier_ids: [],
      store_id: null,
      center_store_id: null,
      storehouse_id: null,
      storehouseoption: [],
      centeroption: [],
    });
    await XlbTipsModal({
      title: '新增直供加点配置',
      isCancel: true,
      bordered: true,
      tips: (
        <div
          style={{
            padding: 20,
          }}
        >
          <XlbBasicForm form={formEdit}>
            <XlbBasicForm.Item
              noStyle
              dependencies={['store_id', 'center_store_id']}
              shouldUpdate
            >
              {({ getFieldValue }) => {
                return (
                  <>
                    <XlbBasicForm.Item
                      label="供应商"
                      name="supplier_ids"
                      dependencies={[]}
                      rules={[{ required: true, message: '请选择供应商' }]}
                    >
                      <XlbInputDialog
                        style={{
                          width: 224,
                        }}
                        dialogParams={{
                          type: 'supplier',
                          dataType: 'lists',
                          isLeftColumn: true,
                          isMultiple: true,
                          primaryKey: 'id',
                          data: {
                            enabled: true,
                          },
                        }}
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      label="门店"
                      name="store_id"
                      dependencies={[]}
                      rules={[{ required: true, message: '请选择门店' }]}
                    >
                      <XlbInputDialog
                        style={{
                          width: 224,
                        }}
                        onChange={(value: any, options: any, form: any) => {
                          getCenterStoreList();
                          form?.setFieldsValue({
                            center_store_id: null,
                            storehouse_id: null,
                            storehouseoption: [],
                            centeroption: [],
                          });
                        }}
                        dialogParams={{
                          type: 'store',
                          dataType: 'lists',
                          isLeftColumn: true,
                          isMultiple: false,
                          primaryKey: 'id',
                          data: {
                            enabled: true,
                            center_flag: false,
                          },
                        }}
                        fieldNames={
                          {
                            idKey: 'id',
                            nameKey: 'store_name',
                          } as any
                        }
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      label="配送中心门店"
                      name="center_store_id"
                      rules={[
                        { required: true, message: '请选择配送中心门店' },
                      ]}
                    >
                      <XlbSelect
                        style={{
                          width: 256,
                        }}
                        options={getFieldValue('centeroption')}
                        disabled={!getFieldValue('store_id')?.length}
                        // @ts-ignore
                        onChange={(value: any, options: any, form: any) => {
                          getStorehouseList();
                          form?.setFieldsValue({
                            storehouse_id: null,
                          });
                        }}
                        allowClear={false}
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item
                      label="配送中心仓"
                      name="storehouse_id"
                      rules={[{ required: true, message: '请选择配送中心仓' }]}
                    >
                      <XlbSelect
                        style={{
                          width: 256,
                        }}
                        options={getFieldValue('storehouseoption')}
                        disabled={!getFieldValue('center_store_id')}
                        allowClear={false}
                      />
                    </XlbBasicForm.Item>
                  </>
                );
              }}
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      ),
      onOkBeforeFunction: async () => {
        try {
          await formEdit.validateFields();
        } catch (err: any) {
          return false;
        }
        const params = { ...formEdit.getFieldsValue(true) };
        pageRef?.current?.setLoading(true);
        const res = await create({
          center_store_id: params?.center_store_id,
          store_id: params?.store_id?.[0],
          storehouse_id: params?.storehouse_id,
          supplier_ids: params?.supplier_ids,
        });
        pageRef?.current?.setLoading(false);
        if (res?.code === 0) {
          XlbMessage.success('操作成功');
          formEdit.resetFields();
          pageRef?.current?.fetchData();
          return true;
        }
      },
    });
  };
  const handleDelete = async (selectRowKeys: any) => {
    XlbTipsModal({
      tips: `已选择${selectRowKeys.length}条数据，是否确定删除?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await configdelete({ ids: selectRowKeys });
        res?.code === 0 && XlbMessage.success('操作成功');
        pageRef?.current?.fetchData();
        return true;
      },
    });
  };

  return (
    <>
      <XlbPageContainer
        url={'/erp/hxl.erp.directsupplypoint.config.find'}
        tableColumn={tableList}
        prevPost={prevPost}
        immediatePost={true}
      >
        <ToolBtn showColumnsSetting>
          {(current) => {
            pageRef.current = current;
            return (
              <XlbButton.Group>
                {hasAuth(['直供加点配置', '查询']) && (
                  <XlbButton
                    key="query"
                    label="查询"
                    type="primary"
                    disabled={current.loading}
                    onClick={() => {
                      current.fetchData();
                    }}
                    icon={<XlbIcon name="sousuo" />}
                  />
                )}
                {hasAuth(['直供加点配置', '编辑']) && (
                  <XlbButton
                    label="新增"
                    type="primary"
                    disabled={current.loading}
                    onClick={() => openItem()}
                    icon={<XlbIcon name="jia" />}
                  />
                )}
                {hasAuth(['直供加点配置', '删除']) && (
                  <XlbButton
                    label="删除"
                    type="primary"
                    disabled={
                      current.loading || current.selectRowKeys?.length === 0
                    }
                    onClick={() => handleDelete(current.selectRowKeys)}
                    icon={<XlbIcon name="shanchu" />}
                  />
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={[
              {
                label: '供应商',
                name: 'supplier_ids',
                type: 'inputDialog',
                dependencies: [],
                dialogParams: (params: any) => {
                  return {
                    type: 'supplier',
                    dataType: 'lists',
                    isLeftColumn: true,
                    isMultiple: true,
                    primaryKey: 'id',
                    data: {
                      enabled: true,
                      org_ids: params?.org_ids || [],
                    },
                  };
                },
              },
              {
                label: '配送中心门店',
                name: 'centen_store_ids',
                type: 'inputDialog',
                dependencies: [],
                dialogParams: (params: any) => {
                  return {
                    type: 'store',
                    dataType: 'lists',
                    isLeftColumn: true,
                    isMultiple: true,
                    primaryKey: 'id',
                    data: {
                      enabled: true,
                      center_flag: true,
                    },
                  };
                },
                fieldNames: {
                  idKey: 'id',
                  nameKey: 'store_name',
                } as any,
              },
              {
                label: '配送中心仓',
                name: 'store_house_id',
                type: 'select',
                options: [],
                dependencies: ['centen_store_ids'],
                handleDefaultValue: (data: any, formData: any) => {
                  if (data?.length === 0) {
                    return null;
                  }
                  const defaultStoreHouse =
                    data.find((item: any) => item.default_flag) || data[0];
                  return defaultStoreHouse?.value;
                },
                // @ts-ignore
                selectRequestParams: (params: any, form: any) => {
                  form?.setFieldsValue({
                    store_house_id: null,
                  });
                  if (params?.centen_store_ids?.length == 1) {
                    return {
                      url: '/erp/hxl.erp.storehouse.store.find',
                      postParams: {
                        store_id: params?.centen_store_ids?.[0],
                      },
                      responseTrans(data) {
                        const options = data
                          .filter((v: any) => v.distribution)
                          .map((item: any) => ({
                            label: item.name,
                            value: item.id,
                            default_flag: item.default_flag,
                          }));
                        return options;
                      },
                    };
                  }
                },
              },
              {
                label: '门店',
                name: 'store_ids',
                type: 'inputDialog',
                dependencies: [],
                dialogParams: (params: any) => {
                  return {
                    type: 'store',
                    dataType: 'lists',
                    isLeftColumn: true,
                    isMultiple: true,
                    primaryKey: 'id',
                    data: {
                      enabled: true,
                      center_flag: false,
                    },
                  };
                },
                fieldNames: {
                  idKey: 'id',
                  nameKey: 'store_name',
                } as any,
              },
            ]}
            form={form}
            isHideDate={true}
          />
        </SearchForm>
        <Table key="id" selectMode="multiple" primaryKey="id" />
      </XlbPageContainer>
    </>
  );
};
export default Index;
