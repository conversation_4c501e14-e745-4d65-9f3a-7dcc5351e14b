export const PayModeKeyMap = {
  erpPayModelChecked: 'erpPayModelChecked',
  erpPayModelChecked2: 'erpPayModelChecked2',
  erpPayOrgIdsMultiple: 'erpPayOrgIdsMultiple',
  erpCenterStoreIdsMultipleFPay: 'erpCenterStoreIdsMultipleFPay'
}

export const payModeConfig: any[] = [
  {
    tag: 'ERP',
    label: '应用模块',
    id: PayModeKeyMap.erpPayModelChecked,
    componentType: 'group',
    formItemProps: {
      label: '应用模块',
      colon: true,
      style: {
        paddingLeft: '52px'
      }
    },
    fieldProps: {
      formList: [
        {
          componentType: 'checkbox',
          name: 'un_login_days_flag',
          id: 'erpPayModelCheckedgroup1',
          tag: 'ERP',
          group: false,
          colon: false,
          fieldProps: {
            options: [
              { label: 'POS业务', value: 'pos' },
              { label: '门店结算', value: 'store_settlement' }
              // { label: '供应商结算', value: 'supplier_settlement' },
              // { label: '批发客户结算', value: 'wholesale_settlement' }
            ],
            style: {
              width: '120px'
            }
          }
        }
      ]
    }
  },
  {
    tag: 'ERP',
    label: '应用模块',
    id: PayModeKeyMap.erpPayModelChecked2,
    componentType: 'group',
    formItemProps: {
      label: '',
      colon: true,
      style: {
        paddingLeft: '121px',
        marginTop: '-18px'
      }
    },
    fieldProps: {
      formList: [
        {
          componentType: 'checkbox',
          name: 'un_login_days_flag',
          id: 'erpPayModelCheckedgroup1',
          tag: 'ERP',
          group: false,
          colon: false,
          fieldProps: {
            options: [
              // { label: 'POS业务', value: 'pos' },
              // { label: '门店结算', value: 'store_settlement' },
              { label: '供应商结算', value: 'supplier_settlement' },
              { label: '批发客户结算', value: 'wholesale_settlement' }
            ],
            style: {
              width: '120px'
            }
          }
        }
      ]
    }
  },
  {
    tag: 'ERP',
    label: '组织',
    id: PayModeKeyMap.erpPayOrgIdsMultiple,
    name: 'org_ids',
    componentType: 'select',
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    },
    formItemProps: {
      style: {
        width: 300
      }
    },
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(anybaseURL + '/erp-mdm/hxl.erp.org.find', {
        company_ids: formValues.company_ids || '',
        level: 2
      })
      if (res.code == 0) {
        return res.data.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
      return []
    }
  },
  {
    tag: 'ERP',
    id: PayModeKeyMap?.erpCenterStoreIdsMultipleFPay,
    name: 'store_ids',
    dependencies: ['org_ids'],
    formItemProps: {
      label: '门店',
      style: {
        width: 300
      }
    },
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids'])
      }
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            center_flag: false
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },

    componentType: 'inputDialog'
  }
]