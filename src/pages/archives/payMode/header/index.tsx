import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { useBaseParams } from '@/hooks/useBaseParams';
import { hasAuth } from '@/utils/kit';
import { XlbProPageContainer } from '@xlb/components';
import type { FC } from 'react';

const Index: FC = () => {
  const { enable_organization } = useBaseParams((state) => state);

  return (
    <XlbProPageContainer // 查询
      searchFieldProps={{
        formList: [
          { id: 'keyword', label: '关键字' },
          {
            id: ErpFieldKeyMap?.erpOrgIdsMultiple,
            label: '组织',
            name: 'org_ids',
            hidden: () => !enable_organization,
            onChange: (e: string[], form: any, _: any) => {
              // 删除已删除的组织下的应用门店
              const c_orgIds = e || [];
              const c_soreItems =
                form
                  .getFieldValue(0)
                  ?.filter((item: any) =>
                    c_orgIds?.includes(item?.org_parent_id),
                  ) ?? [];
              form.setFieldsValue({
                0: c_soreItems,
                store_ids: c_soreItems?.map((j: any) => j?.id) || [],
                select_store: c_soreItems?.length ? true : false,
              });
            },
          },
          {
            id: ErpFieldKeyMap?.erpCenterStoreIdsMultipleF,
            label: '应用门店',
            itemSpan: 8,
            onChange: (e, form, options) => {
              form?.setFieldsValue({
                0: options,
                select_store: e?.length ? true : false,
              });
            },
          },
        ].filter((t: any) => !(!enable_organization && t.name == 'org_ids')),
      }}
      tableFieldProps={{
        url: '/erp/hxl.erp.paymentmethod.find',
        tableColumn: [
          {
            name: '序号',
            code: '_index',
            width: 50,
            align: 'center',
          },
          {
            name: '支付方式名称',
            code: 'name',
            width: 200,
            features: { sortable: true, details: true },
          },
          {
            name: '组织',
            code: 'org_names',
            width: 280,
            features: { sortable: true },
            render: (value, record, index) => {
              return (
                <>{record?.organizations?.map((t: any) => t.name).join(',')}</>
              );
            },
            hidden: () => !enable_organization,
          },
        ].filter((t) => !(!enable_organization && t.code == 'org_names')),
        immediatePost: true,
        selectMode: 'single',
        keepDataSource: false,
        showColumnsSetting: true,
      }}
      deleteFieldProps={{
        name: '删除',
        showField: 'poi_id',
        url: hasAuth(['支付方式', '删除'])
          ? '/erp/hxl.erp.paymentmethod.delete'
          : '',
      }}
      addFieldProps={{
        name: '新增',
        url: hasAuth(['支付方式', '编辑'])
          ? '/erp/hxl.erp.paymentmethod.save'
          : '',
      }}
      details={{
        mode: 'modal',
        isCancel: true,
        width: 400,
        disabled(formValues) {
          return formValues?.flag;
        },
        title: (obj) => {
          return <div>{obj?.id ? '编辑' : '新增'}</div>;
        },
        hiddenSaveBtn: true,
        queryFieldProps: {
          params: (obj: any) => {
            return {
              ...obj,
              id: obj?.id ? obj.id : -1,
              org_ids: obj?.organizations?.map((t: any) => t.id) || [],
              store_ids: obj?.stores?.map((t: any) => t.id) || [],
              select_store: obj?.stores?.length ? true : false,
            };
          },
        },
        primaryKey: 'id',
        formList: [
          {
            componentType: 'form',
            fieldProps: {
              formList: [
                {
                  id: ErpFieldKeyMap.erpPoiId,
                  label: '支付方式名称',
                  name: 'name',
                  itemSpan: 24,
                  rules: [{ required: true, message: '支付方式名称不能为空' }],
                  fieldProps: { maxLength: 10 },
                },
                {
                  id: ErpFieldKeyMap?.erpPayOrgIdsMultiple,
                  label: '组织',
                  rules: [
                    ({ getFieldValue }) => ({
                      required: enable_organization && getFieldValue('pos'),
                      message: '请选择组织',
                    }),
                  ],
                  dependencies: ['enable_organization', 'pos'],
                  hidden: (formValues) =>
                    !enable_organization || !formValues?.pos,
                  fieldProps: {
                    tooltip:
                      '此组织和门店仅应用pos业务，不影响门店结算、供应商结算、批发客户结算',
                  },
                  onChange: (e: string[], form: any, _: any) => {
                    // 删除已删除的组织下的应用门店
                    const c_orgIds = e || [];
                    const c_soreItems =
                      form
                        .getFieldValue(0)
                        ?.filter((item: any) =>
                          c_orgIds?.includes(item?.org_parent_id),
                        ) ?? [];
                    form.setFieldsValue({
                      0: c_soreItems,
                      store_ids: c_soreItems?.map((j: any) => j?.id) || [],
                      select_store: c_soreItems?.length ? true : false,
                    });
                  },
                },
                {
                  id: ErpFieldKeyMap?.erpCenterStoreIdsMultipleFPay,
                  label: '应用门店',
                  dependencies: ['pos'],
                  hidden: (formValues) => !formValues?.pos,
                  onChange: (e, form, options) => {
                    form?.setFieldsValue({
                      0: options,
                      select_store: e?.length ? true : false,
                    });
                  },
                },
                {
                  id: ErpFieldKeyMap.erpPoiId,
                  label: '备注',
                  name: 'memo',
                  itemSpan: 24,
                },
                {
                  id: ErpFieldKeyMap.erpPayModelChecked,
                  label: '应用模块',
                  itemSpan: 24,
                },
                {
                  id: ErpFieldKeyMap.erpPayModelChecked2,
                  label: '',
                  itemSpan: 24,
                },
              ],
            },
          },
        ],
        updateFieldProps: {
          url: '/erp/hxl.erp.paymentmethod.update',
        },
      }}
    />
  );
};

export default Index;
