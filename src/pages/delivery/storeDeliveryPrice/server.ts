import { XlbFetch as ErpRequest } from "@xlb/utils";

//获取数据 
export const getStoreDeliveryPrice = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.page',data)
}
//复制
export const copyStoreDeliveryPrice = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.copy',data)
}
//批量修改
export const batchUpdate = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.batchupdate',data)
}
 //更新
export const Update = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliveryprice.update',data)
}
//获取修改记录
export const gethistory = async (data: any) => {
    return await ErpRequest.post('/erp/hxl.erp.storedeliverypricelog.page',data)
}
