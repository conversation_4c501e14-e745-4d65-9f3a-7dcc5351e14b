interface IRoute {
  component?: any;
  exact?: boolean;
  path?: string;
  routes?: IRoute[];
  wrappers?: string[];
  title?: string;
  __toMerge?: boolean;
  __isDynamic?: boolean;
  [key: string]: any;
}

export const routeList: IRoute[] = [
  {
    path: '/xlb_erp/itemSku/index',
    component: '@/pages/purchasement/itemSku/header/index',
    title: '品类规划表',
    subTitle: '数据查询',
    subMenu: 'purchase',
    tabClass: 'stockTypeSetting',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/priceAdjustment/index',
    component: '@/pages/procurement/priceAdjustment/index',
    title: '调价管理',
    subTitle: '业务操作',
    subMenu: 'purchase',
    tabClass: 'priceAdjustment',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/purchaseLatestPrice/index',
    component: '@/pages/procurement/purchaseLatestPrice/index',
    title: '采购最新进价',
    subTitle: '数据查询',
    subMenu: 'purchase',
    tabClass: 'puchaseLatestPrice',
  },
  {
    path: '/xlb_erp/newItemPurchasePlan/index',
    component: '@/pages/purchase/newItemPurchasePlan/index',
    title: '新品采购计划',
    subTitle: '业务操作',
    tabClass: 'newItemPurchasePlan',
    subMenu: 'purchase',
  },
  {
    path: '/xlb_erp/newItemPurchasePlan/item/index',
    component: '@/pages/purchase/newItemPurchasePlan/item/index',
    title: '关联统配单',
    tabClass: 'newItemPurchasePlan',
    subMenu: 'purchase',
  },
  {
    path: '/xlb_erp/purchaseOrdering/index',
    component: '@/pages/procurement/purchaseOrdering/index',
    title: '采购智能订货',
    subTitle: '业务操作',
    subMenu: 'purchase',
    tabClass: 'purchaseOrdering',
  },
  {
    path: '/xlb_erp/newYearGoodsPlanExport/index',
    component: '@/pages/procurement/newYearGoodsPlanExport/index',
    title: '采购计划报表',
    subTitle: '数据查询',
    showTitle: '采购计划报表',
    subMenu: 'purchase',
    tabClass: 'newYearGoodsPlanExport',
  },
  {
    path: '/xlb_erp/newYearGoodsPlan/index',
    component: '@/pages/procurement/newYearGoodsPlan/index',
    title: '采购计划',
    subTitle: '业务操作',
    showTitle: '采购计划',
    subMenu: 'purchase',
    tabClass: 'newYearGoodsPlan',
  },
  {
    path: '/xlb_erp/orderDemandPool/index',
    component: '@/pages/procurement/orderDemandPool/index',
    title: '订单池',
    subTitle: '业务操作',
    showTitle: '订单池',
    subMenu: 'purchase',
    tabClass: 'orderDemandPool',
  },
  {
    path: '/xlb_erp/stockPlan/index',
    component: '@/pages/procurement/stockPlan/index',
    title: '备货计划',
    subTitle: '业务操作',
    showTitle: '备货计划',
    subMenu: 'purchase',
    tabClass: 'stockPlan',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/purchaseReport/index',
    component: '@/pages/procurement/purchaseReport/index',
    title: '采购汇总明细',
    subTitle: '数据查询',
    subMenu: 'purchase',
    tabClass: 'purchaseReport',
  },
  {
    path: '/xlb_erp/orderWatch/index',
    component: '@/pages/purchase/orderWatch/index',
    title: '订单监控',
    subTitle: '数据查询',
    subMenu: 'purchase',
    tabClass: 'orderWatch',
  },
  {
    path: '/xlb_erp/purchaseShare/index',
    component: '@/pages/procurement/purchaseShare/header/index',
    title: '采购共享配置',
    subTitle: '基础配置',
    subMenu: 'purchase',
    tabClass: 'purchaseShare',
  },
  {
    path: '/xlb_erp/wavePickingConfig/index',
    component: '@/pages/procurement/wavePickingConfig/index',
    title: '波次配置',
    subTitle: '基础配置',
    tabClass: 'wavePickingConfig',
    subMenu: 'purchase',
    wrappers: ['@/pages/public/proPageProvide'],
  },
  {
    path: '/xlb_erp/deliveryQuantityManagement/index',
    component: '@/pages/procurement/deliveryQuantityManagement/index',
    title: '起送量配置',
    subTitle: '基础配置',
    subMenu: 'purchase',
    tabClass: 'deliveryQuantityManagement',
  },

  {
    path: '/xlb_erp/purchasePrice/index',
    component: '@/pages/procurement/purchasePrice/index',
    title: '采价计划',
    subTitle: '业务操作',
    subMenu: 'purchase',
    tabClass: 'purchasePrice',
  },
  {
    path: '/xlb_erp/purchasePrice/item',
    component: '@/pages/procurement/purchasePrice/item',
    title: '采价计划详情',
    subTitle: '业务操作',
    subMenu: 'purchase',
    tabClass: 'purchasePrice',
    hidden: true,
  },
  {
    path: '/xlb_erp/mustSellGoodsManagement/index',
    component: '@/pages/purchase/mustSellGoodsManagement/index',
    title: '必卖品货盘管理',
    subTitle: '业务操作',
    tabClass: 'mustSellGoodsManagement',
    subMenu: 'purchase',
  },
  {
    path: '/xlb_erp/mustSellGoodsManagement/item',
    component: '@/pages/purchase/mustSellGoodsManagement/item',
    title: '必卖品管理详情',
    tabClass: 'mustSellGoodsManagement',
    subMenu: 'purchase',
  },
  {
    path: '/xlb_erp/mustSellGoodsDetail/index',
    component: '@/pages/purchase/mustSellGoodsDetail/index',
    title: '必卖品明细',
    subTitle: '数据查询',
    tabClass: 'mustSellGoodsDetail',
    subMenu: 'purchase',
  },
  {
    path: '/xlb_erp/purchaseReplenishAnalysis/index',
    component: '@/pages/purchase/purchaseReplenishAnalysis/index',
    title: '采购补货分析',
    subTitle: '业务操作',
    subMenu: 'purchase',
    tabClass: 'purchaseReplenishAnalysis',
  },
  {
    path: '/xlb_erp/orderValidDay/index',
    component: '@/pages/procurement/orderValidDay/index',
    title: '订单有效天数',
    subTitle: '基础配置',
    subMenu: 'purchase',
    tabClass: 'orderValidDay',
  },
  {
    path: '/xlb_erp/storehouseParams/index',
    component: '@/pages/procurement/storehouseParams/index',
    title: '仓供配置',
    subTitle: '基础配置',
    subMenu: 'purchase',
    tabClass: 'storehouseParams',
  },
  {
    path: '/xlb_erp/supplierRelationshipManagement/index',
    component: '@/pages/procurement/supplierRelationshipManagement/header/index',
    title: '商品供货关系',
    subTitle: '基础配置',
    subMenu: 'purchase',
    tabClass: 'supplierRelationshipManagement',
  },
  {
    path: '/xlb_erp/orderParamsConfig/index',
    component: '@/pages/procurement/orderParamsConfig/index',
    title: '订货参数配置',
    subTitle: '基础配置',
    subMenu: 'purchase',
    tabClass: 'orderParamsConfig',
  },
  {
    path: '/xlb_erp/storehouseParams/index',
    component: '@/pages/procurement/storehouseParams/index',
    title: '仓供配置',
    subTitle: '基础配置',
    subMenu: 'purchase',
    tabClass: 'storehouseParams',
  },
];

export { routeList as purchasementRouteList };