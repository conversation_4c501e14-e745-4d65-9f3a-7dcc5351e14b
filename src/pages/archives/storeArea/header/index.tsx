import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { exportPage } from '@/services/system';
import { hasAuth } from '@/utils';
import Download from '@/utils/downloadBlobFile';
import { wujieBus } from '@/wujie/utils';
import {
  XlbBasicForm,
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbMessage,
  XlbModal,
  XlbProPageContainer,
  XlbTipsModal,
} from '@xlb/components';
import { DataType } from '@xlb/components/dist/components/XlbTree/type';
import { FormInstance } from 'antd';
import { useEffect, useState } from 'react';
import Api from '../server';
import BatchChange from './component/batchChange';

export default () => {
  const [visible, setVisible] = useState(false);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [editAddTree, setEditAddTree] = useState<{
    visible?: boolean;
    data?: any;
    type?: string;
  }>({});
  const [form] = XlbBasicForm.useForm();
  let fetch = () => {};

  const importStores = async (context: {
    dataSource: any[];
    selectRow: any[];
    form?: FormInstance;
  }) => {
    const { dataSource, form } = context;

    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storearea.store.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storeareatemplate.download`,
      templateName: '门店区域门店明细导入',
    });
    if (res.code !== 0) return;
    let data = res.data?.stores;
    const errInfo = [];
    if (dataSource?.length) {
      const ids = dataSource.map((i) => i.id);
      errInfo.push(
        ...data
          .filter((i: any) => ids.includes(i.id))
          .map((i: any) => `【${i.store_name}】`),
      );
      data = data.filter((i: any) => !ids.includes(i.id));
    }
    if (errInfo.length) {
      await XlbTipsModal({
        tips: '以下门店已存在，系统已自动过滤',
        tipsList: errInfo,
      });
    }
    form?.setFieldValue('stores', [...dataSource, ...data]);
  };

  const exportStores = async (id: any) => {
    const data = {
      id: id,
    };

    const res = await exportPage('/erp-mdm/hxl.erp.storearea.export', data, {
      responseType: 'blob',
    });
    if (res?.code) return;
    const download = new Download();
    download.filename = '门店导出.xlsx';
    download.xlsx(res?.data);
  };

  const onMenuClick = async (key: any, data: any) => {
    if (key === 'add' && data.level === 5)
      return XlbMessage.error('当前节点不可操作');
    if ((key === 'delete' || key === 'edit') && data.level === 0)
      return XlbMessage.error('当前节点不可操作');
    if (key === 'delete') {
      await XlbTipsModal({
        tips: `是否要删除分类${data.name}`,
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await Api.categoryDelete({ id: data.id });
          if (res.code === 0) {
            getTreeData();
            XlbMessage.success('操作成功');
          }
          return true;
        },
      });
    } else {
      if (key === 'edit') {
        form.setFieldValue('name', data.name);
      } else {
        form.setFieldValue('name', undefined);
      }
      setEditAddTree({
        visible: true,
        type: key,
        data: { ...data },
      });
    }
  };

  const onOk = async () => {
    const values = await form.validateFields();
    if (!values) return;
    const data = { name: values.name, id: undefined };
    if (editAddTree.type === 'edit') {
      data.id = editAddTree.data.id;
    }
    const res =
      editAddTree.type === 'add'
        ? await Api.categorySave(data)
        : await Api.categoryUpdate(data);
    if (res.code === 0) {
      getTreeData();
      XlbMessage.success('操作成功');
      setEditAddTree({ ...editAddTree, visible: false });
    }
  };

  const getTreeData = async () => {
    const res = await Api.categoryFind({});
    if (res.code === 0) {
      setTreeData(res.data);
    }
  };

  const exportItem = async (setIsLoading: any, list: any, e: any) => {
    setIsLoading(true);
    let res = null;
    res = await Api.export({ ids: list });
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    getTreeData();
  }, []);

  return (
    <>
      <BatchChange
        visible={visible}
        onCancel={() => setVisible(false)}
        onOk={() => {
          setVisible(false);
          fetch();
        }}
      />
      <XlbModal
        width={400}
        visible={editAddTree?.visible}
        title={editAddTree?.type === 'add' ? '新增分类' : '编辑分类'}
        onOk={onOk}
        onCancel={() => setEditAddTree({ ...editAddTree, visible: false })}
        isCancel={true}
      >
        <XlbBasicForm form={form} style={{ marginTop: 12 }}>
          <XlbBasicForm.Item
            label="名称"
            name="name"
            rules={[{ required: true, message: '请输入分类名称' }]}
            labelCol={{ span: 5 }}
          >
            <XlbInput
              style={{ width: 260 }}
              placeholder="请输入分类名称"
              maxLength={30}
            />
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </XlbModal>
      <XlbProPageContainer
        searchFieldProps={{ formList: ['keyword'] }}
        details={{
          primaryKey: 'id',
          title: (obj) => {
            return <div>{obj?.id ? '编辑' : '新增'}</div>;
          },
          formList: [
            {
              componentType: 'blueBar',
              fieldProps: {
                title: '门店区域信息',
              },
              children: [
                {
                  componentType: 'form',
                  fieldProps: {
                    width: 960,
                    formList: [
                      {
                        label: '门店区域名称',
                        name: 'name',
                        id: 'commonInput',
                        fieldProps: { maxLength: 10, width: '100%' },
                        rules: [
                          {
                            required: true,
                            message: '门店区域名称不允许为空',
                          },
                        ],
                      },
                      {
                        id: ErpFieldKeyMap.erpStoreAreaIds,
                      },
                      {
                        id: ErpFieldKeyMap.erpStoreAreaCategoriesId,
                        name: 'category_ids',
                        label: '门店区域分类',
                        rules: [
                          {
                            required: true,
                            message: '门店区域分类不允许为空',
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
            {
              componentType: 'blueBar',
              fieldProps: {
                title: '门店信息',
              },
              children: [
                {
                  componentType: 'editTable',
                  name: 'stores',
                  fieldProps: {
                    dialogParams: {
                      type: 'store',
                      dataType: 'lists',
                      isMultiple: true,
                    },
                    selectMode: 'multiple',
                    style: { height: 'calc(100vh - 374px)' },
                    columns: [
                      {
                        name: '序号',
                        code: '_index',
                        width: 60,
                        align: 'center',
                      },
                      {
                        name: '门店代码',
                        code: 'store_code',
                        width: 120,
                        features: { sortable: true },
                      },
                      {
                        name: '门店名称',
                        code: 'store_name',
                        width: 160,
                        features: { sortable: true },
                      },
                      {
                        name: '门店分组',
                        code: 'store_group_name',
                        width: 100,
                        features: { sortable: true },
                      },
                      {
                        name: '店铺模式',
                        code: 'management_type',
                        width: 100,
                        features: { sortable: true },
                        render: (text) =>
                          text == '0' ? '直营' : text === '1' ? '加盟' : null,
                      },
                      {
                        name: '配送类型',
                        code: 'delivery_type',
                        width: 100,
                        render: (text) =>
                          text == 'DIRECT'
                            ? '直营'
                            : text === 'JOIN'
                              ? '加盟'
                              : null,
                        features: { sortable: true },
                      },
                    ],
                    extra(context) {
                      return (
                        <XlbButton.Group>
                          {hasAuth(['门店区域', '导入']) ? (
                            <XlbButton
                              type="primary"
                              onClick={() => importStores(context)}
                              icon={<XlbIcon name="daoru" />}
                            >
                              导入
                            </XlbButton>
                          ) : null}
                          {hasAuth(['门店区域', '导出']) ? (
                            <XlbButton
                              type="primary"
                              disabled={
                                !context.dataSource?.length ||
                                !context.form?.getFieldValue('id')
                              }
                              onClick={() =>
                                exportStores(context.form?.getFieldValue('id'))
                              }
                              icon={<XlbIcon name="daochu" />}
                            >
                              导出
                            </XlbButton>
                          ) : null}
                        </XlbButton.Group>
                      );
                    },
                  },
                },
              ],
            },
          ],
          queryFieldProps: {
            url: '/erp-mdm/hxl.erp.storearea.read',
            afterPost(data, oldFormValues) {
              return {
                ...data,
                store_group_ids: data.store_groups?.length
                  ? data.store_groups.map((i: any) => i.id)
                  : [],
                category_ids: data.store_area_categories?.length
                  ? data.store_area_categories[0].id
                  : undefined,
              };
            },
          },
          updateFieldProps: {
            url: hasAuth(['门店区域', '编辑'])
              ? '/erp-mdm/hxl.erp.storearea.update'
              : undefined,
            beforePost(formValues) {
              return {
                ...formValues,
                store_ids: formValues?.stores?.map((i: any) => i.id),
              };
            },
          },
          saveFieldProps: {
            url: '/erp-mdm/hxl.erp.storearea.save',
            beforePost(formValues) {
              return {
                ...formValues,
                store_ids: formValues?.stores?.map((i: any) => i.id),
              };
            },
          },
        }}
        addFieldProps={{
          name: '新增',
          url: hasAuth(['门店区域', '编辑'])
            ? '/erp-mdm/hxl.erp.storearea.save'
            : undefined,
        }}
        deleteFieldProps={{
          url: hasAuth(['门店区域', '删除'])
            ? '/erp-mdm/hxl.erp.storearea.delete'
            : undefined,
        }}
        extra={(context) => {
          fetch = context.fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['门店区域', '导出']) ? (
                <XlbButton
                  type="primary"
                  disabled={!context?.selectRowKeys?.length}
                  onClick={(e) =>
                    exportItem(context?.setLoading, context?.selectRowKeys, e)
                  }
                  icon={<XlbIcon name="daochu" />}
                >
                  导出
                </XlbButton>
              ) : null}
              <XlbButton
                type="primary"
                onClick={() => setVisible(true)}
                icon={<XlbIcon name="shezhi" />}
              >
                批量设置
              </XlbButton>
            </XlbButton.Group>
          );
        }}
        treeFieldProps={{
          leftUrl: undefined,
          dataSource: treeData,
          leftKey: 'category_id',
          dataType: DataType.LISTS,
          menu: [
            { key: 'add', name: '新增分类' },
            { key: 'edit', name: '编辑分类' },
            { key: 'delete', name: '删除分类' },
          ],
          onMenuClick: onMenuClick,
        }}
        tableFieldProps={{
          url: '/erp-mdm/hxl.erp.storearea.find',
          selectMode: 'multiple',
          tableColumn: [
            {
              name: '序号',
              code: '_index',
              width: 70,
              align: 'center',
            },
            {
              name: '门店区域名称',
              code: 'name',
              features: { sortable: true, details: true },
              width: 160,
            },
            {
              name: '门店区域分类',
              code: 'store_area_categories',
              features: { sortable: true },
              width: 120,
              render: (text) =>
                Array.isArray(text) && text?.length ? text[0].name : '',
            },
          ],
          immediatePost: true,
        }}
      />
    </>
  );
};