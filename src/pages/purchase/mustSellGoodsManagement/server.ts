import { default as XlbFetch } from '@/utils/XlbFetch';

// 导出
export const exportGoods = async (data: any) => {
  return await XlbFetch('/erp/hxl.erp.mustsellitemassortment.export', data);
};

// 详情导出
export const exportGoodsDetail = async (data: any) => {
  return await XlbFetch(
    '/erp/hxl.erp.mustsellitemassortment.inner.detail.export',
    data,
  );
};

// 审核
export const aduitGoods = async (data: { fid: string }) => {
  return await XlbFetch('/erp/hxl.erp.mustsellitemassortment.audit', data);
};

// 反审核
export const readuitGoods = async (data: { fid: string }) => {
  return await XlbFetch('/erp/hxl.erp.mustsellitemassortment.reaudit', data);
};

// 更新
export const updateGoods = async (data: any) => {
  return await XlbFetch(
    '/erp/hxl.erp.mustsellitemassortment.batchupdate',
    data,
  );
};
