import {XlbFetch as ErpRequest } from '@xlb/utils';

export const currencyEmail = {
  CNY: '人民币',
};

export const payAccountEmail = {
  BASIC: '基本存款账户',
  GENERAL: '一般存款账户',
};

export default {
  saveCBSPaymentEntity: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.cbscashbank.save', data),
  updateStoreCBSPaymentEntity: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.cbscashbank.sort.update', data),
  deleteCBSPaymentEntity: async (data: any) =>
    await ErpRequest.post('/erp/hxl.erp.cbscashbank.delete', data),
};
