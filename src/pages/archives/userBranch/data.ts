import {columnWidthEnum} from "@/data/common/constant";
import type { XlbTableColumnProps } from '@xlb/components'


export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center'
  },
  {
    name: '用户部门名称',
    code: 'name',
    width: 160,
    features: { sortable: true,details:true },
    // features: { sortable: true}
  },
  {
    name: '部门负责人',
    code: 'leader',
    width: 160,
    features: { sortable: true }
  },
  {
    name: '',
    code: '',
  }
]
