import type { XlbTableColumnProps } from '@xlb/components'

export const summaryType: any[] = [
  // {
  //   label: '申请组织',
  //   value: 0
  // },
  {
    label: '调出门店',
    value: 'STORE'
  },
  {
    label: '商品档案',
    value: 'ITEM'
  },
  {
    label: '商品类别',
    value: 'ITEM_CATEGORY'
  },
  {
    label: '商品品牌',
    value: 'ITEM_BRAND'
  },
  {
    label: '生产商',
    value: 'PRODUCER_SUPPLIER'
  },
  {
    label: '供应商',
    value: 'SUPPLIER'
  },
  {
    label: '产品批次',
    value: 'BATCH_NUMBER'
  },
  {
    label: '异常类别',
    value: 'ABNORMAL_TYPE'
  },
  {
    label: '异常二级',
    value: 'ABNORMAL_SUB_TYPE'
  },
  // {
  //   label: '单据',
  //   value: 9
  // },
  {
    label: '调入门店',
    value: 'IN_STORE'
  }
]
export const auditorType: any[] = [
  {
    label: '审核',
    value: 'AUDIT'
  },
  {
    label: '处理拒绝',
    value: 'HANDLE_REFUSE'
  },
  {
    label: '处理通过',
    value: 'HANDLE'
  },
  {
    label: '批复拒绝',
    value: 'APPROVE_REFUSE'
  },
  {
    label: '批复通过',
    value: 'APPROVE'
  }
]
export const unitType: any[] = [
  {
    label: '基本单位',
    value: 'BASIC'
  },
  {
    label: '配送单位',
    value: 'DELIVERY'
  }
]
export const dateType: any[] = [
  {
    label: '审核时间',
    value: 0
  },
  {
    label: '处理时间',
    value: 1
  },
  {
    label: '批复时间',
    value: 2
  }
]

export const indexItem: XlbTableColumnProps<any> = {
  name: '序号',
  code: '_index',
  width: 80,
  align: 'center'
}
export const deliveryQuantity: XlbTableColumnProps<any>[] = [
  {
    name: '调入数量',
    code: 'delivery_in_quantity',
    width: 100,
    features: { sortable: true },
    align: 'right',
    hidden: true,
    render(text, record, index) {
      return Number(text || 0).toFixed(2)
    }
  },
  {
    name: '调出数量',
    code: 'delivery_in_quantity',
    width: 100,
    hidden: true,
    features: { sortable: true },
    align: 'right',
    render(text, record, index) {
      return Number(text || 0).toFixed(2)
    }
  },
  {
    name: '退货数量',
    code: 'quantity',
    width: 100,
    features: { sortable: true },
    align: 'right',
    render(text, record, index) {
      return Number(text || 0).toFixed(2)
    }
  }
]
export const returnRate: XlbTableColumnProps<any> = {
  name: '退货率',
  code: 'return_rate_str',
  width: 100,
  features: { sortable: true },
  align: 'right',
  render(text, record, index) {
    return text || '-'
  }
}
export const applyStore: XlbTableColumnProps<any> = {
  name: '调出门店',
  code: 'store_name',
  width: 150,
  features: { sortable: true },
  align: 'left'
}
export const warehouse: XlbTableColumnProps<any> = {
  name: '调入门店',
  code: 'in_store_name',
  width: 150,
  features: { sortable: true },
  align: 'left'
}
export const orgName: XlbTableColumnProps<any> = {
  name: '调出组织',
  code: 'org_name',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const warehouseOrgName: XlbTableColumnProps<any> = {
  name: '调入组织',
  code: 'in_org_name',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const itemUnit: XlbTableColumnProps<any> = {
  name: '单位',
  code: 'unit',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const productCode: XlbTableColumnProps<any> = {
  name: '商品代码',
  code: 'item_code',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const productName: XlbTableColumnProps<any> = {
  name: '商品名称',
  code: 'item_name',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const productBranding: XlbTableColumnProps<any> = {
  name: '商品品牌',
  code: 'item_brand_name',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const producerSupplierName: XlbTableColumnProps<any> = {
  name: '生产厂商信息',
  code: 'producer_supplier_name',
  width: 150,
  features: { sortable: true },
  align: 'left'
}
export const supplierName: XlbTableColumnProps<any> = {
  name: '供应商信息',
  code: 'supplier_name',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const exceptionCategory: XlbTableColumnProps<any> = {
  name: '异常类别',
  code: 'abnormal_type_name',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const anomalyLevel2: XlbTableColumnProps<any> = {
  name: '异常二级',
  code: 'abnormal_sub_type_name',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const descriptionException: XlbTableColumnProps<any> = {
  name: '异常描述',
  code: 'abnormal_desc',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const productLots: XlbTableColumnProps<any> = {
  name: '产品批次',
  code: 'batch_number',
  width: 120,
  features: { sortable: true },
  align: 'left'
}
export const categoryName: XlbTableColumnProps<any> = {
  name: '商品类别',
  code: 'item_category_name',
  width: 100,
  align: 'left'
}
export const time: XlbTableColumnProps<any>[] = [
  {
    name: '调入时间',
    code: 'delivery_in_date',
    width: 120,
    features: { sortable: true, format: 'TIME' },
    align: 'left'
  },
  {
    name: '申请时间',
    code: 'return_date',
    width: 120,
    features: { sortable: true, format: 'TIME' },
    align: 'left'
  }
]
export const tableColumnEnum: Record<
  | 'STORE' // 门店
  | 'IN_STORE' // 仓
  | 'ITEM' // 商品档案
  | 'ITEM_CATEGORY' // 商品类别
  | 'ITEM_BRAND' // 商品品牌
  | 'PRODUCER_SUPPLIER' // 生产商
  | 'SUPPLIER' // 供应商
  | 'BATCH_NUMBER' // 产品批次
  | 'ABNORMAL_TYPE' // 异常类别
  | 'ABNORMAL_SUB_TYPE', // 异常二级
  // | 9, // 单据
  XlbTableColumnProps<any>[]
> = {
  // 门店
  STORE: [
    orgName, // 组织
    applyStore // 申请门店
  ],
  // 商品档案
  ITEM: [
    productCode,
    productName
    // productBranding,
  ],
  // 商品类别
  ITEM_CATEGORY: [categoryName],
  // 商品品牌
  ITEM_BRAND: [productBranding],
  // 生产商
  PRODUCER_SUPPLIER: [producerSupplierName],
  // 供应商
  SUPPLIER: [supplierName],
  // 产品批次
  BATCH_NUMBER: [productLots],
  // 异常类别
  ABNORMAL_TYPE: [exceptionCategory],
  // 异常二级
  ABNORMAL_SUB_TYPE: [anomalyLevel2],
  // 单据
  // 9: [
  //   {
  //     name: '单据号',
  //     code: 'fid',
  //     width: 120,
  //     align: 'center',
  //     features: { sortable: true }
  //   },
  //   {
  //     name: '审核状态',
  //     code: 'state',
  //     width: 120,
  //     align: 'center',
  //     features: { sortable: true },
  //     render: (text: any) => {
  //       switch (text) {
  //         case 'INIT':
  //           return '初始化'
  //         case 'AUDIT':
  //           return '审核'
  //         case 'HANDLE':
  //           return '处理通过'
  //         case 'HANDLE_REFUSE':
  //           return '处理拒绝'
  //         case 'APPROVE':
  //           return '批复通过'
  //         case 'APPROVE_REFUSE':
  //           return '批复拒绝'
  //         default:
  //           return ''
  //       }
  //     }
  //   },
  //   {
  //     name: '申请原因',
  //     code: 'reason',
  //     width: 120,
  //     features: { sortable: true },
  //     align: 'center'
  //   },
  //   orgName, // 组织
  //   applyStore, // 申请门店
  //   {
  //     name: '调往门店',
  //     code: 'in_store_name',
  //     width: 120,
  //     features: { sortable: true },
  //     align: 'center'
  //   },
  //   productBranding,
  //   productCode,
  //   productName,
  //   categoryName,
  //   {
  //     name: '单位',
  //     code: 'item_unit',
  //     width: 100,
  //     features: { sortable: true },
  //     align: 'center'
  //   },
  //   {
  //     name: '调入数量',
  //     code: 'delivery_in_quantity',
  //     width: 100,
  //     features: { sortable: true },
  //     align: 'center',
  //     render(text, record, index) {
  //       return Number(text || 0).toFixed(2)
  //     }
  //   },
  //   {
  //     name: '退货数量',
  //     code: 'apply_quantity',
  //     width: 100,
  //     features: { sortable: true },
  //     align: 'center',
  //     render(text, record, index) {
  //       return Number(text || 0).toFixed(2)
  //     }
  //   },
  //   productLots,
  //   producerSupplierName,
  //   supplierName,
  //   exceptionCategory,
  //   anomalyLevel2,
  //   descriptionException,
  //   returnRate,
  //   {
  //     name: '时间',
  //     code: 'apply_date',
  //     features: { sortable: true },
  //     width: columnWidthEnum.ITEM_SPEC,
  //     align: 'center'
  //   },
  //   {
  //     name: '操作人',
  //     code: 'operator',
  //     features: { sortable: true },
  //     width: 100,
  //     align: 'center'
  //   }
  // ],
  IN_STORE: [warehouseOrgName, warehouse, itemUnit, ]
}
