import { exportPage } from '@/services/system';
import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import toFixed from '@/utils/toFixed';
import { UploadOutlined } from '@ant-design/icons';
import {
  XlbIcon as IconFont,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbPageContainer,
} from '@xlb/components';
import { type FC, useEffect } from 'react';
import { filterArr, formList, tableList } from './data';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const StrongDataAnalysis: FC = () => {
  const [form] = XlbBasicForm.useForm();

  let refresh = () => {};

  useEffect(() => {}, []);
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'valid_days':
        item.render = (value: any) => {
          return <div className="overwidth">{Number(value)?.toFixed(1)}</div>;
        };
        break;
      case 'stock_price':
        item.render = (value: any) => {
          return <div className="overwidth">{Number(value)?.toFixed(4)}</div>;
        };
        break;
      case 'stock_money':
        item.render = (value: any) => {
          return <div className="overwidth">{Number(value)?.toFixed(2)}</div>;
        };
        break;
      case 'base_stock_quantity':
      case 'avg_sale_quantity':
      case 'upper_limit':
        item.render = (value: any) => {
          return (
            <div className="overwidth">{toFixed(value, 'QUANTITY', true)}</div>
          );
        };
        break;
      case 'stock_quantity':
        item.render = (value: any, record: any) => {
          return (
            <div
              className="info overwidth"
              style={{
                color:
                  Number(value) > Number(record.upper_limit)
                    ? 'red'
                    : Number(value) < Number(record.base_stock_quantity)
                      ? 'green'
                      : '',
              }}
            >
              {' '}
              {toFixed(value, 'QUANTITY', true)}
            </div>
          );
        };
        break;
      case 'stop_purchase':
        return (item.render = (value: any) => {
          return value === true ? (
            <div className="overwidth">
              <span>是</span>
            </div>
          ) : value === false ? (
            <div className="overwidth">
              <span>否</span>
            </div>
          ) : (
            <div className="overwidth">
              <span></span>
            </div>
          );
        });
      default:
        item.render = (value: any) => (
          <div className="info overwidth">{value}</div>
        );
        break;
    }
    return item;
  };

  //处理查询参数
  const prevPost = () => {
    const params = form.getFieldsValue();
    const data = {
      ...params,
      ...Object.fromEntries(
        filterArr.map((item) => [
          item.value,
          params.checkValue.includes(item.value),
        ]),
      ),
      company_id: LStorage.get('userInfo').company_id,
      operator_store_id: LStorage.get('userInfo').store_id,
    };
    delete data.checkValue;
    return { ...data };
  };

  // 导出
  const exportItem = async (requestForm: any, setLoading: any, e: any) => {
    setLoading(true);
    const data = prevPost();
    const res = await exportPage('/erp/hxl.erp.stockmaxmin.export', data, {
      responseType: 'blob',
    });
    const download = new Download();
    download.filename = '高低库存商品.xlsx';
    download.xlsx(res.data);
    setLoading(false);
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.stockmaxmin.page'}
      tableColumn={tableList.map((v) => tableRender(v))}
      prevPost={prevPost}
    >
      <SearchForm>
        <XlbForm
          isHideDate
          formList={formList}
          form={form}
          getFormRecord={refresh}
          initialValues={{
            unit_type: 'PURCHASE',
            stock_quantity_symbol: '>',
            stock_money_symbol: '>',
            sale_day: 28,
            checkValue: ['delivery_out_order', 'wholesale_order', 'pos_order'],
            store_ids: [LStorage.get('userInfo').store_id],
          }}
        />
      </SearchForm>
      <ToolBtn>
        {({ fetchData, dataSource, requestForm, loading, setLoading }: any) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['高低库存商品', '查询']) && (
                <XlbButton
                  loading={loading}
                  label="查询"
                  type="primary"
                  onClick={() => {
                    fetchData();
                  }}
                  icon={
                    <IconFont name="sousuo" color="currentColor" size={16} />
                  }
                />
              )}
              {hasAuth(['高低库存商品', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={loading || !dataSource?.length}
                  onClick={(e) => exportItem(requestForm, setLoading, e)}
                  icon={<UploadOutlined size={16} />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        selectMode={false}
        emptyCellHeight={300}
        bordered={false}
        primaryKey={'id'}
      />
    </XlbPageContainer>
  );
};

export default StrongDataAnalysis;