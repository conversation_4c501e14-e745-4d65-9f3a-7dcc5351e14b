export interface PurchasePlanDetailResDTO {
  /**
   * @name 创建人
   */
  create_by?: string

  /**
   * @name 创建时间
   */
  create_time?: string

  /**
   * @name 商品条码
   */
  item_barcode?: string

  /**
   * @name 商品代码
   */
  item_code?: string

  /**
   * @name 商品名称
   */
  item_name?: string

  /**
   * @name 采购规格
   */
  item_spec?: string

  /**
   * @name 金额
   */
  money?: number

  /**
   * @name
   */
  org_id?: number

  /**
   * @name 所属组织
   */
  org_name?: string

  /**
   * @name 采购计划id
   */
  purchase_plan_id?: number

  /**
   * @name 单位
   */
  purchase_unit?: string

  /**
   * @name 订单员
   */
  purchaser?: string

  /**
   * @name 金额
   */
  quantity?: number

  /**
   * @name 所属门店id
   */
  store_id?: number

  /**
   * @name 所属门店名称
   */
  store_name?: string

  /**
   * @name 供应商id
   */
  supplier_id?: number

  /**
   * @name 供应商名称
   */
  supplier_name?: string
}

export interface PurchasePlanResDTO {
  /**
   * @name 开始时间
   */
  begin_time?: string

  /**
   * @name 创建人
   */
  create_by?: string

  /**
   * @name 创建时间
   */
  create_time?: string

  /**
   * @name 状态
   */
  enable?: boolean

  /**
   * @name 结束时间
   */
  end_time?: string

  /**
   * @name 采购计划id
   */
  id?: number

  /**
   * @name 金额
   */
  money?: number

  /**
   * @name 计划名称
   */
  name?: string

  /**
   * @name
   */
  org_id?: number

  /**
   * @name 所属组织
   */
  org_name?: string

  /**
   * @name 金额
   */
  quantity?: number
}
