import safeMath from '@/utils/safeMath';
import { useModal } from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbInputDialog,
  XlbMessage,
  XlbModal,
  XlbRadio,
  XlbTipsModal,
} from '@xlb/components';
import { Fragment, useState } from 'react';
import { copyWholeSalePrice } from '../../server';
import style from './copy.less';
import XlbProgress from './progress';

const Copy = (props: any) => {
  const { handleCancel, getData, currentTab } = props;
  const [apiCalls, setApiCalls] = useState(0);
  const modal = useModal();
  const [form] = XlbBasicForm.useForm();
  const [storeList, setStoreList] = useState<any>([]);
  const [progress, setProgress] = useState<any>({
    open: false,
    tips: '',
    percentNum: 0,
  });

  const [loading, setloading] = useState<boolean>(false);

  const handleOk = async (apiCalls = 0) => {
    if (!form.getFieldValue('revise_names')) {
      XlbTipsModal({
        tips:
          currentTab === 'StorePrice' ? `请先选择修改门店` : '请先选择修改客户',
      });
      return;
    } else {
      if (!form.getFieldValue('reference_names')) {
        XlbTipsModal({
          tips:
            currentTab === 'StorePrice'
              ? `请先选择参照门店`
              : `请先选择参照客户`,
        });
        return;
      }
    }
    if (
      currentTab == 'CustumerPrice' &&
      !form.getFieldValue('revise_names_1')
    ) {
      XlbTipsModal({
        tips: `请先选择修改门店`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    } else if (
      currentTab == 'CustumerPrice' &&
      !form.getFieldValue('reference_names_1')
    ) {
      XlbTipsModal({
        tips: `请先选择参照门店`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    const radiovalue = form.getFieldValue('radioValue');
    if (!radiovalue) {
      XlbTipsModal({
        tips: `请先选择商品`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    } else {
      if (
        form.getFieldValue('radioValue') == 2 &&
        (form.getFieldValue('item_category_ids') == undefined ||
          form.getFieldValue('item_category_ids') == '')
      ) {
        XlbTipsModal({
          tips: `请选择商品类别`,
          isConfirm: true,
          isCancel: false,
        });
        return;
      }
      if (
        form.getFieldValue('radioValue') == 3 &&
        (form.getFieldValue('item_ids') == undefined ||
          form.getFieldValue('item_ids') == '')
      ) {
        XlbTipsModal({
          tips: `请选择商品档案`,
          isConfirm: true,
          isCancel: false,
        });
        return;
      }
    }

    console.log(form.getFieldValue('revise_ids'), form.getFieldValue('reference_ids'));
    if (
      form
        .getFieldValue('revise_ids')
        .includes(form.getFieldValue('reference_ids'))
    ) {
      XlbTipsModal({
        tips:
          currentTab === 'StorePrice'
            ? `修改门店和参照门店不能一致`
            : '修改客户和参照客户不能一致',
        isConfirm: true,
        isCancel: false,
      });
      return;
    } else if (
      currentTab === 'CustumerPrice' &&
      form
        .getFieldValue('revise_ids_1')
        .includes(
          form.getFieldValue('reference_ids_1')
            ? form.getFieldValue('reference_ids_1')[0]
            : '',
        )
    ) {
      XlbTipsModal({
        tips: `修改门店和参照门店不能一致`,
        isConfirm: true,
        isCancel: false,
      });
      return;
    }
    const apiCallsCount = apiCalls + 1;
    const targets = form.getFieldValue('revise_names')
      ? form.getFieldValue('revise_ids')
      : null;
    const targetsNames = form.getFieldValue('revise_names').split(',');
    const target_store_ids = [targets[apiCalls]];
    const revise_names = targetsNames[apiCalls];
    const data = {
      source_client_id:
        currentTab === 'CustumerPrice'
          ? form.getFieldValue('reference_ids')
          : '',
      target_store_ids:
        currentTab === 'CustumerPrice'
          ? (form.getFieldValue('revise_ids_1') ?? '')
          : target_store_ids,
      source_store_ids:
        currentTab === 'StorePrice'
          ? [form.getFieldValue('reference_ids')]
          : (form.getFieldValue('reference_ids_1') ?? []),
      target_client_ids: currentTab === 'StorePrice' ? [] : target_store_ids,
      item_category_ids: form.getFieldValue('item_category_names')
        ? form.getFieldValue('item_category_ids')
        : null,
      item_ids: form.getFieldValue('item_names')
        ? form.getFieldValue('item_ids')
        : null,
      revise_names,
    };
    setApiCalls(apiCallsCount);
    const len = storeList.length;
    const percentNum =
      safeMath.divide(Number(apiCallsCount) || 1, Number(len)) * 100;
    setProgress({
      ...progress,
      tips: storeList[apiCalls]?.name || '',
      percentNum,
      open: true,
    });
    setloading(true);
    const res = await copyWholeSalePrice(data);
    setloading(false);
    if (res?.code === 0) {
      if (apiCallsCount <= len - 1) {
        handleOk(apiCallsCount);
      } else {
        setProgress({
          ...progress,
          open: false,
        });
        XlbMessage.success('更新成功');
        setApiCalls(0);
        modal.resolve(false);
        modal.hide();
        // getData(1)
        form.setFieldsValue({
          radioValue: '',
        });
        form.resetFields();
      }
    } else {
      setProgress({
        ...progress,
        open: false,
      });
      form.resetFields();
    }
  };

  return (
    <XlbModal
      title={'复制'}
      centered
      isCancel={true}
      open={modal.visible}
      maskClosable={false}
      keyboard={false}
      onOk={() => handleOk(0)}
      onCancel={() => {
        modal.hide();
        modal.resolve(false);
      }}
      width={418}
      confirmLoading={loading}
    >
      <XlbBasicForm
        form={form}
        labelCol={{ span: 8 }}
        style={{ padding: '10px 0' }}
      >
        <div className={style.box}>
          <p className={style.title}>
            {currentTab === 'StorePrice' ? '复制门店' : '复制客户'}
          </p>
          <XlbBasicForm.Item
            name="revise_names"
            style={{
              display: 'inline-block',
              marginLeft: '32px',
              marginTop: '0px',
            }}
            label={currentTab === 'StorePrice' ? '修改门店' : '修改客户'}
            colon={false}
          >
            <XlbInputDialog
              dialogParams={{
                type: currentTab === 'StorePrice' ? 'store' : 'wholesaler',
                isMultiple: true,
                data: {
                  status: 1,
                },
              }}
              onChange={(val: any, list: any) => {
                if (!val) {
                  form.setFieldsValue({
                    revise_ids: undefined,
                    revise_names: undefined,
                  });
                  setStoreList([]);
                  return;
                }
                setStoreList(list);
                if (currentTab === 'StorePrice') {
                  form.setFieldsValue({
                    revise_ids: val,
                    revise_names: list.map((i: any) => i.store_name).join(','),
                  });
                } else {
                  form.setFieldsValue({
                    revise_ids: val,
                    revise_names: list.map((i: any) => i.name).join(','),
                  });
                }
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: currentTab === 'StorePrice' ? 'store_name' : 'name',
              }}
              width={180}
            />
          </XlbBasicForm.Item>
          <XlbBasicForm.Item
            name="reference_names"
            style={{ display: 'inline-block', marginLeft: '32px' }}
            label={currentTab === 'StorePrice' ? '参照门店' : '参照客户'}
            colon={false}
          >
            <XlbInputDialog
              dialogParams={{
                type: currentTab === 'StorePrice' ? 'store' : 'wholesaler',
                isMultiple: false,
                data: {
                  status: 1,
                },
              }}
              onChange={(val: any, list: any) => {
                if (!val) {
                  form.setFieldsValue({
                    reference_ids: undefined,
                    reference_names: undefined,
                  });
                  return;
                }
                if (currentTab === 'StorePrice') {
                  form.setFieldsValue({
                    reference_ids: val?.[0],
                    reference_names: list?.[0]?.store_name,
                  });
                } else {
                  form.setFieldsValue({
                    reference_ids: val?.[0],
                    reference_names: list?.[0]?.name,
                  });
                }
              }}
              fieldNames={{
                idKey: 'id',
                nameKey: currentTab === 'StorePrice' ? 'store_name' : 'name',
              }}
              width={180}
            />
          </XlbBasicForm.Item>
          {currentTab !== 'StorePrice' ? (
            <Fragment>
              <XlbBasicForm.Item
                name="revise_names_1"
                style={{ display: 'inline-block', marginLeft: '32px' }}
                label={'修改门店'}
                colon={false}
              >
                <XlbInputDialog
                  dialogParams={{
                    type: 'store',
                    isMultiple: true,
                    data: {
                      status: 1,
                    },
                  }}
                  fieldNames={{
                    idKey: 'id',
                    nameKey: 'store_name',
                  }}
                  onChange={(val: any, list: any) => {
                    if (!val) {
                      form.setFieldsValue({
                        revise_ids_1: undefined,
                        revise_names_1: undefined,
                      });
                      return;
                    }
                    form.setFieldsValue({
                      revise_ids_1: val,
                      revise_names_1: list
                        .map((i: any) => i.store_name)
                        .join(','),
                    });
                  }}
                  width={180}
                />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item
                name="reference_names_1"
                style={{ display: 'inline-block', marginLeft: '32px' }}
                label={'参照门店'}
                colon={false}
              >
                <XlbInputDialog
                  dialogParams={{
                    type: 'store',
                    isMultiple: false,
                    data: {
                      status: 1,
                    },
                  }}
                  fieldNames={{
                    idKey: 'id',
                    nameKey: 'store_name',
                  }}
                  onChange={(val: any, list: any) => {
                    if (!val) {
                      form.setFieldsValue({
                        reference_ids_1: undefined,
                        reference_names_1: undefined,
                      });
                      return;
                    }
                    form.setFieldsValue({
                      reference_ids_1: val,
                      reference_names_1: list
                        .map((i: any) => i.store_name)
                        .join(','),
                    });
                  }}
                  width={180}
                />
              </XlbBasicForm.Item>
            </Fragment>
          ) : null}
        </div>
        <div className={style.box}>
          <p className={style.title}>选择商品</p>
          <XlbBasicForm.Item name="radioValue">
            <XlbRadio.Group>
              {/* <Space direction="vertical"> */}
              <XlbRadio value={1}>全部商品</XlbRadio>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <XlbRadio value={2}>商品类别</XlbRadio>
                <XlbBasicForm.Item name="item_category_names">
                  <XlbInputDialog
                    treeModalConfig={{
                      // @ts-ignore
                      title: '选择商品分类', // 标题
                      topLevelTreeCheckable: true,
                      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
                      dataType: 'lists',
                      checkable: true, // 是否多选
                      primaryKey: 'id',
                      data: {
                        enabled: true,
                      },
                    }}
                    onChange={(val: any, list: any) => {
                      if (!val) {
                        form.setFieldsValue({
                          item_category_ids: undefined,
                          item_category_names: undefined,
                        });
                        return;
                      }
                      form.setFieldsValue({
                        item_category_ids: val,
                        item_category_names: list
                          .map((i: any) => i.name)
                          .join(','),
                      });
                    }}
                    width={180}
                  />
                </XlbBasicForm.Item>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <XlbRadio value={3}>商品档案</XlbRadio>
                <XlbBasicForm.Item name="item_names">
                  <XlbInputDialog
                    dialogParams={{
                      type: 'goods',
                      isMultiple: true,
                      primaryKey: 'id',
                      dataType: 'lists',
                      data: {
                        enabled: true,
                      },
                    }}
                    onChange={(val: any, list: any) => {
                      if (!val) {
                        form.setFieldsValue({
                          item_ids: undefined,
                          item_names: undefined,
                        });
                        return;
                      }
                      form.setFieldsValue({
                        item_ids: val,
                        item_names: list.map((i: any) => i.name).join(','),
                      });
                    }}
                    width={180}
                  />
                </XlbBasicForm.Item>
              </div>
              {/* </Space> */}
            </XlbRadio.Group>
          </XlbBasicForm.Item>
        </div>
      </XlbBasicForm>
      <XlbProgress progress={progress} onCancel={setProgress} />
    </XlbModal>
  );
};
export default Copy;
