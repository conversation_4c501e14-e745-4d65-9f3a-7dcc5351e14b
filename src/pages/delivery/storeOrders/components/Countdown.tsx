import React, { useEffect, useState } from 'react'
import { useCountDown } from 'ahooks';
import moment from 'moment';


interface Iprops {
    submit_time: string
    limit_time: number
}
export default function CountdownBox(props: Iprops) {
    const { submit_time, limit_time } = props
    const [targetDate, setTargetDate] = useState<number>();
    const [open, setOpen] = useState(true)
    const [countdown, formattedRes] = useCountDown({
        leftTime: targetDate,  //剩余时间
        End: () => { }
    });


    useEffect(() => {
        // setTargetDate(60 * 1000)
        if (submit_time) {
            const tagTime = moment(submit_time).add(limit_time, 'minutes')
            const diffTime = moment().diff(tagTime, 'milliseconds')

            //目标时间 大于现在的时间戳展开弹框
            if (moment(tagTime).unix() > moment().unix()) {
                setTargetDate(Math.abs(diffTime))
            }
        }
    }, [submit_time])

    const Time = (props: any) => {
        const { style } = props
        return <div style={{ ...style, fontSize: 18, display: 'flex', alignItems: 'center', justifyContent: 'center' }} >
            <span className="icon iconfont icon-shijian" style={{ fontSize: 20 }} />
            <span style={{ fontFamily: 'Helvetica' }}>&nbsp;待支付  {`${formattedRes.minutes}:${formattedRes.seconds < 10 ? `0${formattedRes.seconds}` : formattedRes.seconds}`}</span>
        </div>
    }
    return (
        <div style={{ display: formattedRes.seconds !== 0 ? 'block' : 'none', position: 'absolute', bottom: 0, right: 200, width: 280, boxShadow: '0px 0px 8px 0px rgba(0, 0, 0, 0.19)', zIndex: 30, backgroundColor: 'white' }}>
            <div style={{ backgroundColor: '#3d66fe', borderTopRightRadius: 5, borderTopLeftRadius: 5, color: 'white', height: 44, padding: '10px 20px', display: 'flex', justifyContent: 'space-between' }}>
                {!open ? <Time /> : <span style={{ fontSize: 18 }}>提示</span>}
                <span className={`icon iconfont ${open ? 'icon-shouqi1' : 'icon-fangdazhankai'}`} style={{ cursor: 'pointer' }} onClick={() => setOpen(!open)} />
            </div>

            {open &&
                <div style={{ color: '#3d66fe', height: 110, textAlign: 'center' }}>

                    <Time style={{ margin: '16px 0' }} />

                    <p style={{ width: 239, color: '#4E5969', marginLeft: 20.5 }}>{`请尽快完成支付，超过${limit_time}分钟，单据将 自动作废`}</p>
                </div>}
        </div>
    )
}
