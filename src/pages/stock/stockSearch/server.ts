import { XlbFetch as ErpRequest } from '@xlb/utils';

// 查询
const allQuery = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stock.page', data);
};
//明细查询
const stockDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockdetail.page', data);
};
//商品汇总
const itemDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stock.store.item.page', data);
};
//门店汇总
const stockStore = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stock.store.page', data);
};
// 仓汇总
const storehouse = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stock.storehouse.page', data);
};

// 账号管理仓库查询
const getStock = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.storehouse.store.find', data);
};
//供货主体查询
const getSupplierMainBody = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.suppliermainbody.find', data);
};
//供货主体查询
const maxlevelRead = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.category.maxlevel.read', data);
};
//重新核算成本
const recosting = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stock.cost.account', data);
};

export default {
  allQuery,
  stockDetail,
  itemDetail,
  stockStore,
  storehouse,
  getStock,
  getSupplierMainBody,
  maxlevelRead,
  recosting,
};