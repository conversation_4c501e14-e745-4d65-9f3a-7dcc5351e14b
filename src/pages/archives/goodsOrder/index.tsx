import { ErpFieldKeyMap } from '@/data/common/fieldListConfig'
import { hasAuth, LStorage } from '@/utils'
import {
  XlbButton,
  XlbIcon,
  XlbImportModal,
  XlbInputNumber,
  XlbProPageContainer,
  XlbProPageContainerWithMemo,
  XlbSelect,
} from '@xlb/components';
import { toFixed } from '@xlb/utils'
import { Fragment, useRef, type FC } from 'react'
import BatchChange from './component/batchChange'
import BatchChangeSpecial from './component/batchChangeSpecial'
import NiceModal from '@ebay/nice-modal-react'
import copyBatch from './component/copyBatch'
import { useBaseParams } from '@/hooks/useBaseParams'
import { message } from 'antd'
import { itemType, UPADTEAPI } from './data'
import ViewDeliveryUnit from './component/viewDeliveryUnit'

const Index: FC = () => {
  const { enable_organization } = useBaseParams((state) => state)
  const ref = useRef<any>()
  const userInfo = LStorage.get('userInfo')
  const authority = {
    import: hasAuth(['商品订购属性', '导入']),
    export: hasAuth(['商品订购属性', '导出']),
    edit: hasAuth(['商品订购属性', '编辑'])
  }

  const updateItem: (value: any, record: any, tabKey: any, code: any) => Promise<void> = async (
    value,
    record,
    tabKey,
    code
  ) => {
    if (record?.general_store_flag && code == 'multiple') {
      message.error('普通门店不允许设置订购倍数')
      return
    }
    const type =
      tabKey == 'order_special'
        ? record.type
        : tabKey == 'store_order_num'
        ? 'REQUEST'
        : 'WHOLESALE'
    const params = {
      item_id: record.item_id,
      store_id: record.store_id,
      type,
      lower_limit: record.lower_limit,
      multiple: record.multiple,
      upper_limit: record.upper_limit,
      stock_upper_limit: record.stock_upper_limit,
      end_cap: record.end_cap,
      start_cap: record.start_cap,
      recommend: record.recommend,
      end_cap_quantity: record.end_cap_quantity,
      [code]: value === '' || isNaN(Number(value)) ? undefined : value
    }
    const res = await UPADTEAPI[tabKey as keyof typeof UPADTEAPI]?.(params)
    if (res.code === 0) {
      message.success('修改成功')
      ref?.current?.pageContainerRef?.current[tabKey]?.fetchData?.()
    }
  }

  const importItem = async (typeKey: string, fetchData: () => void) => {
    const importUrl: { [key in string]: string } = {
      order_num: `${process.env.BASE_URL}/erp/hxl.erp.itemorderattribute.import`,
      store_order_num: `${process.env.BASE_URL}/erp/hxl.erp.storeorderattribute.import`,
      order_special: `${process.env.BASE_URL}/erp/hxl.erp.itemorderfeature.import`
    }
    const templateUrl: { [key in string]: string } = {
      order_num: `${process.env.BASE_URL}/erp/hxl.erp.itemorderattributetemplate.download`,
      store_order_num: `${process.env.BASE_URL}/erp/hxl.erp.storeorderattributetemplate.download`,
      order_special: `${process.env.BASE_URL}/erp/hxl.erp.itemorderfeaturetemplate.download`
    }
    const res = await XlbImportModal({
      importUrl: importUrl[typeKey],
      templateUrl: templateUrl[typeKey]
    })
    if (res.code === 0) {
      fetchData?.()
    }
  }

  const batchChange = async (tabKey: string, search: any = {}, fetchData: () => void) => {
    let res: any
    if (tabKey == 'order_special') {
      res = await NiceModal.show(BatchChangeSpecial, {
        tabKey,
        search,
        enableOrganization: enable_organization
      })
    } else {
      res = await NiceModal.show(BatchChange, {
        tabKey,
        enableOrganization: enable_organization
      })
    }
    if (res) {
      fetchData?.()
    }
  }

  const copyItem = async (tabKey: string, fetchData: () => void) => {
    const res = await NiceModal.show(copyBatch, {
      tabKey
    })
    if (res) {
      fetchData?.()
    }
  }

  const compareRed = (text: any, record: any): boolean => {
    if (text !== record?.item_delivery_unit) {
      return true
    }
    if (Array.isArray(record?.detail_units) && record?.detail_units?.length) {
      return !!record?.detail_units?.find(
        (item: any) => item.distribution_unit !== record?.item_delivery_unit,
      );
    }
    return false
  }
  const isRedClick = (text: any, record: any): boolean => {
    if (Array.isArray(record?.detail_units) && record?.detail_units?.length) {
      if (text !== record?.item_delivery_unit) {
        return true
      }
      return !!record?.detail_units?.find(
        (item: any) => item.distribution_unit !== record?.item_delivery_unit,
      );
    }
    return false
  }

  return (
    <Fragment>
      <XlbProPageContainerWithMemo
        ref={ref}
        tabFieldProps={{
          defaultActiveKey: 'order_num',
          name: 'tabKey',
          items: [
            {
              label: '批发订购属性',
              key: 'order_num',
              children: {
                searchFieldProps: {
                  formList: [
                    {
                      label: '组织',
                      name: 'org_ids',
                      id: ErpFieldKeyMap?.billOrgIds,
                      hidden: !enable_organization,
                      onChange: (e: string[], form: any, _: any) => {
                        if (e.length > 0) {
                          form.setFieldsValue({
                            store_ids: null
                          })
                        }
                      }
                    },
                    {
                      label: '配送中心',
                      name: 'store_ids',
                      rules: [{ required: true, message: '请选择配送中心' }],
                      id: ErpFieldKeyMap?.erpCenterStoreIdsMultiple
                    },
                    {
                      label: '商品档案',
                      name: 'item_ids',
                      id: ErpFieldKeyMap?.erpitemIds,
                      fieldProps: {
                        dialogParams: {
                          type: 'goods',
                          dataType: 'lists',
                          isMultiple: true,
                          data: {
                            filter_item_types: ['COMBINATION', 'MAKEBILL']
                          }
                        }
                      }
                    },
                    {
                      label: '商品类别',
                      name: 'item_category_ids',
                      id: ErpFieldKeyMap?.erpRateStatisticsItemCategory
                    },
                    {
                      label: '停购',
                      name: 'stop_purchase',
                      id: 'commonSelect',
                      options: [
                        { label: '是', value: true },
                        { label: '否', value: false }
                      ]
                    }
                  ],
                  initialValues: {
                    type: 'WHOLESALE'
                  }
                },
                extra: (content) => {
                  return (
                    <>
                      {authority.import ? (
                        <XlbButton
                          type="primary"
                          onClick={() => importItem('order_num', content.fetchData)}
                          icon={<XlbIcon name="daoru" />}
                        >
                          导入
                        </XlbButton>
                      ) : null}
                      {authority.edit ? (
                        <XlbButton
                          type="primary"
                          disabled={!content?.dataSource?.length}
                          onClick={() => batchChange('order_num', {}, content.fetchData)}
                          icon={<XlbIcon name="piliang" />}
                        >
                          批量设置
                        </XlbButton>
                      ) : null}
                    </>
                  )
                },
                tableFieldProps: {
                  prevPost: (formValue: any) => {
                    if (!formValue?.store_ids) {
                      ref?.current?.formRef?.current?.order_num.validateFields()
                      return false
                    }
                    return formValue
                  },
                  primaryKey: 'item_id',
                  url: '/erp/hxl.erp.itemorderattribute.page',
                  tableColumn: [
                    {
                      name: '序号',
                      code: '_index',
                      width: 50,
                      align: 'center'
                    },
                    {
                      name: '组织',
                      code: 'org_name',
                      width: 140,
                      hidden: !enable_organization
                      // features: { sortable: true }
                    },
                    {
                      name: '配送中心',
                      code: 'store_name',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '商品代码',
                      code: 'code',
                      width: 110,
                      features: { sortable: true }
                    },
                    {
                      name: '商品条码',
                      code: 'bar_code',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '商品名称',
                      code: 'name',
                      width: 280,
                      features: { sortable: true }
                    },
                    {
                      name: '采购规格',
                      code: 'purchase_spec',
                      width: 160,
                      features: { sortable: true }
                    },
                    {
                      name: '商品类型',
                      code: 'item_type',
                      width: 100,
                      features: { sortable: true },
                      render: (text) => itemType.find((s) => s.value === text)?.label
                    },
                    {
                      name: '商品类别',
                      code: 'category_name',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '基本单位',
                      code: 'unit',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '批发单位',
                      code: 'wholesale_unit',
                      width: 120,
                      features: { sortable: true },
                      render: (text, r) => {
                        return <div style={{ color: r?.origin_wholesale_unit !== text ? 'red' : '' }}>{text}</div>;
                      },
                    },
                    {
                      name: '配送转换率',
                      code: 'wholesale_ratio',
                      width: 140,
                      features: { sortable: true },
                    },
                    {
                      name: '停购',
                      code: 'stop_purchase',
                      width: 80,
                      features: { sortable: true },
                      render: (text) => (text ? '是' : '否')
                    },
                    {
                      name: '每单订购下限',
                      code: 'lower_limit',
                      width: 130,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) =>
                              updateItem(Number(e.target.value), record, 'order_num', 'lower_limit')
                            }
                          />
                        ) : (
                          toFixed(text, 'QUANTITY')
                        )
                      }
                    },
                    {
                      name: '订购倍数',
                      code: 'multiple',
                      width: 100,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) =>
                              updateItem(Number(e.target.value), record, 'order_num', 'multiple')
                            }
                          />
                        ) : (
                          text
                        )
                      }
                    },
                    {
                      name: '配送日订购上限',
                      code: 'upper_limit',
                      width: 140,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) =>
                              updateItem(Number(e.target.value), record, 'order_num', 'upper_limit')
                            }
                          />
                        ) : (
                          toFixed(text, 'QUANTITY')
                        )
                      }
                    }
                  ],
                  selectMode: 'single',
                  showColumnsSetting: true,
                  changeColumnAndResetDataSource: false
                },
                exportFieldProps: {
                  url: authority.export ? '/erp/hxl.erp.itemorderattribute.export' : undefined,
                  fileName: '商品订购属性'
                }
              }
            },
            {
              label: '门店订购属性',
              key: 'store_order_num',
              children: {
                searchFieldProps: {
                  formList: [
                    {
                      label: '组织',
                      name: 'org_ids',
                      id: ErpFieldKeyMap?.billOrgIds,
                      hidden: !enable_organization,
                      onChange: (e: string[], form: any, _: any) => {
                        if (e.length > 0) {
                          form.setFieldsValue({
                            store_ids: null
                          })
                        }
                      }
                    },
                    {
                      label: '门店',
                      name: 'store_ids',
                      id: ErpFieldKeyMap?.billStoreIds
                    },
                    {
                      label: '商品档案',
                      name: 'item_ids',
                      id: ErpFieldKeyMap?.erpitemIds,
                      fieldProps: {
                        dialogParams: {
                          type: 'goods',
                          dataType: 'lists',
                          isMultiple: true,
                          data: {
                            filter_item_types: ['COMBINATION', 'MAKEBILL']
                          }
                        }
                      }
                    },
                    {
                      label: '商品类别',
                      name: 'item_category_ids',
                      id: ErpFieldKeyMap?.erpRateStatisticsItemCategory
                    },
                    {
                      label: '停购',
                      name: 'stop_purchase',
                      id: 'commonSelect',
                      options: [
                        { label: '是', value: true },
                        { label: '否', value: false }
                      ]
                    },
                    {
                      label: '',
                      id: ErpFieldKeyMap.erpIsDefault,
                      fieldProps: {
                        options: [
                          {
                            label: '仅查询主档差异配送单位商品',
                            value: 'show_diff_distribution_unit',
                          },
                        ],
                      },
                    },
                  ],
                  initialValues: {
                    type: 'WHOLESALE',
                    store_id: userInfo.store_id
                  }
                },
                extra: (content) => {
                  return (
                    <>
                      {authority.import ? (
                        <XlbButton
                          type="primary"
                          onClick={() => importItem('store_order_num', content.fetchData)}
                          icon={<XlbIcon name="daoru" />}
                        >
                          导入
                        </XlbButton>
                      ) : null}
                      {authority.edit ? (
                        <XlbButton
                          type="primary"
                          disabled={!content?.dataSource?.length}
                          onClick={() => batchChange('store_order_num', {}, content.fetchData)}
                          icon={<XlbIcon name="piliang" />}
                        >
                          批量设置
                        </XlbButton>
                      ) : null}
                      {authority.edit ? (
                        <XlbButton
                          type="primary"
                          disabled={!content?.dataSource?.length}
                          onClick={() => copyItem('store_order_num', content.fetchData)}
                          icon={<XlbIcon name="fuzhi" />}
                        >
                          复制
                        </XlbButton>
                      ) : null}
                    </>
                  )
                },
                tableFieldProps: {
                  url: '/erp/hxl.erp.storeorderattribute.page',
                  tableColumn: [
                    {
                      name: '序号',
                      code: '_index',
                      width: 50,
                      align: 'center'
                    },
                    {
                      name: '组织',
                      code: 'org_name',
                      width: 140,
                      hidden: !enable_organization
                      // features: { sortable: true }
                    },
                    {
                      name: '门店名称',
                      code: 'store_name',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '商品代码',
                      code: 'code',
                      width: 110,
                      features: { sortable: true }
                    },
                    {
                      name: '商品条码',
                      code: 'bar_code',
                      width: 140
                      // features: { sortable: true }
                    },
                    {
                      name: '商品名称',
                      code: 'name',
                      width: 280,
                      features: { sortable: true }
                    },
                    {
                      name: '采购规格',
                      code: 'purchase_spec',
                      width: 160
                      // features: { sortable: true }
                    },
                    {
                      name: '商品类型',
                      code: 'item_type',
                      width: 100,
                      // features: { sortable: true },
                      render: (text) => itemType.find((s) => s.value === text)?.label
                    },
                    {
                      name: '商品类别',
                      code: 'category_name',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '基本单位',
                      code: 'unit',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '配送单位',
                      code: 'delivery_unit',
                      width: 140,
                      features: { sortable: true },
                      render: (t, r) => {
                        return (
                          <div
                            style={{ color: compareRed(t,  r) ? 'red' : 'auto' }}
                            className={isRedClick(t, r) ? 'link' : ''}
                            onClick={() => {
                              if (isRedClick(t, r)) {
                                NiceModal.show(ViewDeliveryUnit, { item: r });
                              }
                            }}
                          >
                            {t}
                          </div>
                        );
                      },
                    },
                    {
                      name: '配送转换率',
                      code: 'delivery_ratio',
                      width: 140,
                      features: { sortable: true },
                    },
                    {
                      name: '停购',
                      code: 'stop_purchase',
                      width: 80,
                      features: { sortable: true },
                      render: (text) => (text ? '是' : '否')
                    },
                    {
                      name: '拆零',
                      code: 'demolition_state',
                      width: 80,
                      features: { sortable: true },
                      render: (text) => (text ? '是' : text == false ? '否' : '')
                    },
                    {
                      name: '每单订购下限',
                      code: 'lower_limit',
                      width: 130,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) => {
                              updateItem(e.target.value, record, 'store_order_num', 'lower_limit')
                            }}
                          />
                        ) : // toFixed(text, 'QUANTITY')
                        text === '' || text === null ? (
                          text
                        ) : (
                          toFixed(text, 'QUANTITY')
                        )
                      }
                    },
                    {
                      name: '订购倍数',
                      code: 'multiple',
                      width: 100,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) =>
                              updateItem(e.target.value, record, 'store_order_num', 'multiple')
                            }
                          />
                        ) : // toFixed(text, 'QUANTITY')
                        text === '' || text === null ? (
                          text
                        ) : (
                          toFixed(text, 'QUANTITY')
                        )
                      }
                    },
                    {
                      name: '配送日订购上限',
                      code: 'upper_limit',
                      width: 140,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) =>
                              updateItem(e.target.value, record, 'store_order_num', 'upper_limit')
                            }
                          />
                        ) : // toFixed(text, 'QUANTITY')
                        text === '' || text === null ? (
                          text
                        ) : (
                          toFixed(text, 'QUANTITY')
                        )
                      }
                    },
                    {
                      name: '备货订货上限',
                      code: 'stock_upper_limit',
                      width: 140,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) =>
                              updateItem(
                                e.target.value,
                                record,
                                'store_order_num',
                                'stock_upper_limit'
                              )
                            }
                          />
                        ) : // toFixed(text, 'QUANTITY')
                        text === '' || text === null ? (
                          text
                        ) : (
                          toFixed(text, 'QUANTITY')
                        )
                      }
                    }
                  ],
                  selectMode: 'single',
                  showColumnsSetting: true,
                  changeColumnAndResetDataSource: false,
                  afterPost: (data) => {
                    if (Array.isArray(data?.content)) {
                      return {
                        ...data,
                        content: data?.content?.map?.((item: any) => ({
                          ...item,
                          id: `${item.item_id}_${item.store_id}`
                        }))
                      }
                    }
                    return data
                  }
                },
                exportFieldProps: {
                  url: authority.export ? '/erp/hxl.erp.storeorderattribute.export' : undefined,
                  fileName: '商品订购属性'
                }
              }
            },
            {
              label: '订购特性',
              key: 'order_special',
              children: {
                searchFieldProps: {
                  formList: [
                    {
                      label: '组织',
                      name: 'org_ids',
                      id: ErpFieldKeyMap?.billOrgIds,
                      hidden: !enable_organization,
                      onChange: (e: string[], form: any, _: any) => {
                        if (e.length > 0) {
                          form.setFieldsValue({
                            store_ids: null
                          })
                        }
                      }
                    },
                    {
                      label: '门店',
                      name: 'store_ids',
                      id: ErpFieldKeyMap?.billStoreCenterId
                    },
                    {
                      label: '订购类型',
                      name: 'type',
                      id: 'commonSelect',
                      options: [
                        { label: '门店补货', value: 'REQUEST' },
                        { label: '批发订货', value: 'WHOLESALE' }
                      ],
                      fieldProps: {
                        allowClear: false
                      }
                    },
                    {
                      label: '商品档案',
                      name: 'item_ids',
                      id: ErpFieldKeyMap?.erpitemIds,
                      fieldProps: {
                        dialogParams: {
                          type: 'goods',
                          dataType: 'lists',
                          isMultiple: true,
                          data: {
                            filter_item_types: ['COMBINATION', 'MAKEBILL']
                          }
                        }
                      }
                    },
                    {
                      label: '商品类别',
                      name: 'item_category_ids',
                      id: ErpFieldKeyMap?.erpRateStatisticsItemCategory
                    },
                    {
                      label: '停购',
                      name: 'stop_purchase',
                      id: 'commonSelect',
                      options: [
                        { label: '是', value: true },
                        { label: '否', value: false }
                      ]
                    }
                  ],
                  initialValues: {
                    type: 'REQUEST',
                    store_id: userInfo.store_id
                  }
                },
                extra: (content) => {
                  return (
                    <>
                      {authority.import ? (
                        <XlbButton
                          type="primary"
                          onClick={() => importItem('order_special', content.fetchData)}
                          icon={<XlbIcon name="daoru" />}
                        >
                          导入
                        </XlbButton>
                      ) : null}
                      {authority.edit ? (
                        <XlbButton
                          type="primary"
                          disabled={!content?.dataSource?.length}
                          onClick={() =>
                            batchChange(
                              'order_special',
                              content?.form?.getFieldsValue?.(true),
                              content.fetchData
                            )
                          }
                          icon={<XlbIcon name="piliang" />}
                        >
                          批量设置
                        </XlbButton>
                      ) : null}
                      {authority.edit ? (
                        <XlbButton
                          type="primary"
                          disabled={!content?.dataSource?.length}
                          onClick={() => copyItem('order_special', content?.fetchData)}
                          icon={<XlbIcon name="fuzhi" />}
                        >
                          复制
                        </XlbButton>
                      ) : null}
                    </>
                  )
                },
                tableFieldProps: {
                  primaryKey: 'item_id',
                  url: '/erp/hxl.erp.itemorderfeature.page',
                  tableColumn: [
                    {
                      name: '序号',
                      code: '_index',
                      width: 50,
                      align: 'center'
                    },
                    {
                      name: '商品代码',
                      code: 'item_code',
                      width: 110,
                      features: { sortable: true }
                    },
                    {
                      name: '商品条码',
                      code: 'item_bar_code',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '商品名称',
                      code: 'item_name',
                      width: 280,
                      features: { sortable: true }
                    },
                    {
                      name: '采购规格',
                      code: 'purchase_spec',
                      width: 160,
                      features: { sortable: true }
                    },

                    {
                      name: '商品类型',
                      code: 'item_type',
                      width: 100,
                      features: { sortable: true },
                      render: (text) => itemType.find((s) => s.value === text)?.label
                    },
                    {
                      name: '商品类别',
                      code: 'category_name',
                      width: 140,
                      features: { sortable: true }
                    },
                    {
                      name: '基本单位',
                      code: 'unit',
                      width: 100,
                      features: { sortable: true }
                    },
                    {
                      name: '推荐商品',
                      code: 'recommend',
                      width: 100,
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbSelect
                            style={{ width: '100%' }}
                            defaultValue={text}
                            options={[
                              { label: '是', value: true },
                              { label: '否', value: false }
                            ]}
                            onChange={(e) => updateItem(e, record, 'order_special', 'recommend')}
                          />
                        ) : text ? (
                          '是'
                        ) : (
                          '否'
                        )
                      }
                    },
                    {
                      name: '端头商品',
                      code: 'end_cap',
                      width: 100,
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbSelect
                            style={{ width: '100%' }}
                            defaultValue={text}
                            options={[
                              { label: '是', value: true },
                              { label: '否', value: false }
                            ]}
                            onChange={(e) => updateItem(e, record, 'order_special', 'end_cap')}
                          />
                        ) : text ? (
                          '是'
                        ) : (
                          '否'
                        )
                      }
                    },
                    {
                      name: '端头推荐量',
                      code: 'end_cap_quantity',
                      width: 120,
                      align: 'right',
                      features: { sortable: true },
                      render: (text: any, record: any) => {
                        return record?._click && authority.edit ? (
                          <XlbInputNumber
                            defaultValue={text}
                            onBlur={(e) =>
                              updateItem(
                                Number(e.target.value),
                                record,
                                'order_special',
                                'end_cap_quantity'
                              )
                            }
                          />
                        ) : (
                          text ?? 0
                        )
                      }
                    }
                  ],
                  selectMode: 'single',
                  showColumnsSetting: true,
                  changeColumnAndResetDataSource: false
                },
                exportFieldProps: {
                  url: authority.export ? '/erp/hxl.erp.itemorderfeature.export' : undefined,
                  fileName: '商品订购属性'
                }
              }
            }
          ]
        }}
        setFormValues={(oldValues, newValues) => ({
          ...newValues,
          item_ids: oldValues.item_ids,
          item_category_ids: oldValues.item_category_ids,
          stop_purchase: oldValues.stop_purchase ?? undefined
        })}
      />
    </Fragment>
  )
}
export default Index