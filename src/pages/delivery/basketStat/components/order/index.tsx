// import safeMath from '@/utils/safeMath';
import NiceModal from '@ebay/nice-modal-react';

import { Row, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';

import {
  XlbBasicForm,
  XlbInput,
  XlbModal,
  XlbSelect,
  XlbTable,
} from '@xlb/components';
import { readBasket } from '../../server';
import {
  inItemTableList,
  itemTableList,
  Options1,
  outItemTableList,
} from './data';

const Order = (props: any) => {
  const modal = NiceModal.useModal();
  const { order_fid } = props;
  const [rowData, setRowData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [itemArr, setItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(itemTableList)),
  );

  const [pagin, setPagin] = useState({
    pageSize: 200,
    pageNum: 1,
    total: 0,
  });
  const XlbBasicFormBox = useRef<HTMLDivElement | null>(null);
  const [form] = XlbBasicForm.useForm();
  const [footerData, setFooterData] = useState<any[]>([]);

  const [info, setInfo] = useState({
    state: 'INIT',
  });
  const InvoiceRender = (item: any) => {
    switch (item.code) {
      case 'money':
        item.render = (value: any, record: any, index: number) => {
          return record.index === '合计' ? (
            <div className="info overwidth">{value}</div>
          ) : (
            <div className="info overwidth">{Number(value).toFixed(2)}</div>
          );
        };
        break;
      case 'out_money':
        item.render = (value: any, record: any, index: number) => {
          return info.state == 'HANDLE' ? (
            <div className="info overwidth">-</div>
          ) : record.index === '合计' ? (
            <div className="info overwidth">{value}</div>
          ) : (
            <div className="info overwidth">{Number(value).toFixed(2)}</div>
          );
        };
        break;
      case 'out_quantity':
        item.render = (value: any, record: any, index: number) => {
          return info.state == 'HANDLE' ? (
            <div className="info overwidth">-</div>
          ) : (
            <div className="info overwidth">{value}</div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any) => {
          return (
            <Tooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div className="info overwidth">{value}</div>
            </Tooltip>
          );
        };
        break;
    }
  };

  //读取
  const readinfo = async (fid: any) => {
    setIsLoading(true);
    const res = await readBasket({ fid });
    setIsLoading(false);
    // console.log(res);
    if (res.code === 0) {
      res.data.details.map((v: any) => {
        v.money = v.money?.toFixed(2);
        v.price = v.price?.toFixed(4);
      });
      setInfo({ state: res.data.state });
      setRowData(res.data.details);
      setPagin({
        ...pagin,
        total: res.data.details.length,
      });
      form.setFieldsValue({
        out_store_name: res.data.out_store_name,
        out_store_id: res.data.out_store_id,
        store_name: res.data.store_name,
        store_id: res.data.store_id,
        fid: res.data.fid,
        memo: res.data.memo,
        flag: res.data.flag,
        create_by: res.data.create_by,
        create_time: res.data.create_time,
        audit_by: res.data.audit_by,
        audit_time: res.data.audit_time,
        handle_by: res.data.handle_by,
        handle_time: res.data.handle_time,
        approve_by: res.data.approve_by,
        approve_time: res.data.approve_time,
      });
    }
    setItemArr([
      ...itemArr,
      ...(res.data.flag ? outItemTableList : inItemTableList),
    ]);
  };

  useEffect(() => {
    // 设置合计行
    footerData[0] = {};
    footerData[0].index = '合计';
    footerData[0].money = rowData
      .reduce((sum, v) => sum + Number(v.money), 0)
      .toFixed(2);
    setFooterData([...footerData]);
    setPagin({
      ...pagin,
      pageSize: rowData.length || 200,
      total: rowData.length,
    });
  }, [JSON.stringify(rowData)]);

  useEffect(() => {
    readinfo(order_fid);
  }, []);

  itemArr.map((v) => InvoiceRender(v));

  return (
    <XlbModal
      width={1100}
      open={modal.visible}
      title={'物资进出单详情'}
      isCancel={true}
      onOk={async () => {
        modal.hide();
      }}
      onCancel={() => {
        modal.resolve(false);
        modal.hide();
      }}
      maskClosable
      footer={null}
    >
      <div
        className="modelBox col-flex"
        style={{ height: '100%', overflowY: 'auto', overflowX: 'hidden',margin: '10px 0' }}
      >
        <header>
          <div ref={XlbBasicFormBox}>
            <XlbBasicForm colon form={form} autoComplete="off" layout="inline">
              <div className="row-flex" style={{ width: '1300px' }}>
                <div style={{ width: '90%' }}>
                  <Row>
                    <XlbBasicForm.Item label="发货门店" name="out_store_name">
                      <XlbSelect style={{ width: 180 }} disabled></XlbSelect>
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="收货门店" name="store_name">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="进出方向" name="flag">
                      <XlbSelect style={{ width: 180 }} disabled>
                        <XlbSelect.Option value={false}>发货</XlbSelect.Option>
                        <XlbSelect.Option value={true}>退货</XlbSelect.Option>
                      </XlbSelect>
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="单据号" name="fid">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="单据状态">
                      <XlbInput
                        size="small"
                        style={{ width: '180px' }}
                        disabled
                        value={
                          Options1.find((s) => s.value === info.state)?.label
                        }
                      />
                    </XlbBasicForm.Item>
                    <XlbBasicForm.Item label="留言备注" name="memo">
                      <XlbInput style={{ width: 480, height: 26 }} disabled />
                    </XlbBasicForm.Item>
                  </Row>
                </div>
                <div style={{ width: '10%' }}></div>
              </div>
            </XlbBasicForm>
          </div>
        </header>
        <XlbTable
          isLoading={isLoading}
          dataSource={rowData}
          columns={itemArr}
          total={rowData?.length ?? 0}
          keepDataSource={false}
          // showSearch
          style={{ flex: 1 }}
        />
      </div>
    </XlbModal>
  );
};

export default Order;
