import { SummaryType } from '@/constants/purchaseOrdering';
import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth, LStorage } from '@/utils';
import { coverListToTree, flattenTree } from '@/utils/kit';
import {
  XlbButton,
  XlbIcon,
  XlbInputNumber,
  XlbMessage,
  XlbPageContainer,
  XlbProForm,
  XlbSelect,
  XlbTipsModal,
} from '@xlb/components';
import { ExportBtn } from '@xlb/components/dist/lowcodes/XlbProPageContainer/components';
import { useDebounceFn } from 'ahooks';
import dayjs from 'dayjs';
import { cloneDeep, flatMap } from 'lodash';
import { useRef, useState } from 'react';
import PurchaseOrderingDetailDrawer from './component/purchaseOrderingDetailDrawer';
import { tableColumn } from './data';
import styles from './index.less';
import Api from './server';

const Index = () => {
  const { ToolBtn, SearchForm, Table } = XlbPageContainer;
  const formRef = useRef<any['formRef']>();
  const formValueRef = useRef<any>();
  const pageRef = useRef<any>();
  const dataRef = useRef<any>();
  const tableRef = useRef<any>();
  const userInfo = LStorage.get('userInfo');
  let setSelects: any = null;
  let setSelectKeys: any = null;
  // 计算件数
  const getUnitNumber = (details: any[]) => {
    let nums = 0;
    details?.map((i) => {
      nums += Number(i?.order_qty || 0);
    });
    return Math.ceil(nums);
  };

  const groupTitle = (id: string, name: string, children: any[]) => {
    return (
      <div style={{ fontSize: '14px', fontWeight: 500 }}>
        <span style={{ color: '#1D2129' }}>
          <span>{id}</span>
          <span style={{ margin: '0 3px' }}>|</span>
          <span>{name}</span>
        </span>
        <span style={{ color: '#86909C', margin: '0 18px', fontWeight: 400 }}>
          商品数: <span style={{ color: '#F53F3F' }}>{children?.length}</span>
        </span>
        <span style={{ color: '#86909C', fontWeight: 400 }}>
          件数:{' '}
          <span style={{ color: '#F53F3F' }}>{getUnitNumber(children)}</span>
        </span>
      </div>
    );
  };

  const coverNewDataSource = (data: any) => {
    const dataSourceT = data
      ?.map((i: any) => {
        return {
          // ...i,
          id: i?.supplier_id,
          title: i?.supplier_name,
          groupTitle: groupTitle(
            i?.supplier_code,
            i?.supplier_name,
            i?.details,
          ),
          children: i?.details?.map((v: any) => {
            return {
              ...v,
              /**
               * 线上环境
               *  v?.item_id 有重复，这里改成联合key
               */
              id: [v?.supplier_id, v?.item_id].join('-'),
              details: v?.details?.map((it: any) => {
                return {
                  ...it,
                  prediction_day: dayjs(it?.prediction_day?.toString())?.format(
                    'YYYY-MM-DD',
                  ),
                };
              }),
            };
          }),
        };
      })
      .filter((t: any) => t?.children?.length);
    return dataSourceT;
  };

  const handleChangeNumber = useDebounceFn(
    async (e: any, record: any) => {
      // 获取搭赠数量
      let present_quantityT = 0;
      if (e > 0 && record?.supplier_id) {
        const { purchase_ratio, item_unit, item_id, supplier_id } = record;
        const res = await Api.supplierrebatepresent({
          quantity: e,
          ratio: purchase_ratio,
          item_id,
          supplier_id,
          unit: item_unit,
        });
        if (res.code === 0) {
          present_quantityT = res?.data?.present_quantity;
        }
      } else {
        present_quantityT = 0;
      }
      const rowData = cloneDeep(pageRef?.current?.dataSource);
      const dataSourceT: any[] = rowData?.map((i: any) => {
        /**
         * 这里id只取了第一层级，不会受到影响
         */
        if (record?.supplier_id == i?.id) {
          const iTmp = cloneDeep(i?.children);
          const newChildren = iTmp?.map((v: any) => {
            if (v.item_id == record?.item_id) {
              return {
                ...v,
                order_qty: e,
                present_qty: present_quantityT,
              };
            } else {
              return v;
            }
          });
          return {
            ...i,
            groupTitle: groupTitle(i?.supplier_code, i?.title, newChildren),
            children: newChildren,
          };
        } else if (record?.id === i?.id) {
          if (
            !formValueRef.current?.summary?.includes(SummaryType.BY_SUPPLIER)
          ) {
            return {
              ...i,
              order_qty: e,
              present_qty: present_quantityT,
            };
          }
          return i;
        } else {
          return i;
        }
      });
      pageRef?.current?.setDataSource(dataSourceT);
    },
    { wait: 500 },
  );
  // 切换供应商
  const handleChangeSupplier = async (record: any, source_supplier_id: any) => {
    const rowData = cloneDeep(dataRef.current);
    const list = flattenTree(cloneDeep(rowData)).map((item) => {
      delete item?.children;
      return item;
    });
    const targetIndex = list.findIndex(
      (item) =>
        item.supplier_id == source_supplier_id &&
        item.item_id == record.item_id,
    );
    list.splice(targetIndex, 1);
    list.push(record);
    const tree = coverListToTree(
      cloneDeep(list),
      { idKey: 'id', pidKey: 'pid' },
      (data) => {
        // 子节点
        if (data.item_id) {
          return {
            ...data,
            id: [data.item_id, data.supplier_id].join('-'),
            pid: data.supplier_id,
          };
        }
        // 父节点
        if (!data.item_id) {
          return {
            ...data,
            id: data.supplier_id,
            pid: null,
          };
        }
      },
    );
    dataRef.current = tree;
    const newList = cloneDeep(tree).map((i: any) => {
      return {
        ...i,
        details: i?.children?.map((v: any) => {
          return {
            ...v,
            _click:
              v.supplier_id == record?.supplier_id &&
              v.item_id == record.item_id
                ? true
                : false,
          };
        }),
      };
    });

    const newData = coverNewDataSource(newList);
    // 移动后勾选改动的行
    const select_item = newData
      ?.find((v: any) => v?.id === record?.supplier_id)
      ?.children?.find(
        (j: any) =>
          j?.supplier_id === record?.supplier_id &&
          j?.item_id === record?.item_id,
      );
    setSelects([select_item]);
    setSelectKeys([record?.supplier_id + '-' + record.item_id]);

    pageRef?.current?.setDataSource(newData);
    XlbMessage.success('供应商修改成功');
  };

  const getRed = (record: any) => {
    if (record.valid_days > record.warning_days) {
      return { color: '#FF2121' };
    }
    return {};
  };

  const [detailVisible, setDetailVisible] = useState(false);
  const [detail, setDetail] = useState<any>();
  const render = (item: any) => {
    switch (item.code) {
      case 'valid_days':
        item.render = (text: number, record: any) => {
          return <span style={getRed(record)}>{text?.toFixed(2)}</span>;
        };
        break;
      case 'order_qty':
        item.render = (text: number, record: any) => {
          if (record?._click) {
            return (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                }}
                style={getRed(record)}
              >
                <XlbInputNumber
                  style={{ textAlign: 'right' }}
                  min={1}
                  precision={0}
                  size="small"
                  className="full-box"
                  autoComplete={'off'}
                  onKeyDown={(e) => {
                    if (e.key === '.' || e.key === ',') {
                      e.preventDefault(); // 阻止输入小数点
                    }
                  }}
                  formatter={(value) => `${value}`.replace(/[.,]/g, '')} // 移除小数点
                  parser={(value) => value?.replace(/[.,]/g, '') || ''} // 解析时也移除
                  controls={false}
                  defaultValue={text}
                  onFocus={(e) => {
                    e?.target?.select();
                  }}
                  // 解决输入问题，将输入框变成不受控，加防抖控制接口调用和重新setData
                  onChange={(e: any) => {
                    record.order_qty = e;
                    handleChangeNumber.run(Math.round(e), record);
                    // console.log('useDebounceFn')
                    // record.order_qty = e?.target?.value
                    // handleChangeNumber(e, record)
                  }}
                />
              </div>
            );
          } else {
            return <div style={getRed(record)}>{text}</div>;
          }
        };
        break;
      case 'supplier_id':
        item.render = (text: string, record: any) => {
          if (record?._click) {
            const suppliers = record?.supplier_details?.map((i: any) => {
              return {
                label: i?.supplier_name,
                value: i?.supplier_id,
              };
            });
            return (
              <div
                onClick={(e) => {
                  e.stopPropagation();
                }}
                style={getRed(record)}
              >
                <XlbSelect
                  defaultValue={text}
                  onChange={(e: any, obj: any) => {
                    const source_supplier_id = record.supplier_id;
                    record.supplier_id = e;
                    record.supplier_name = obj?.label;
                    if (
                      formValueRef?.current?.summary?.includes(
                        SummaryType.BY_SUPPLIER,
                      )
                    ) {
                      handleChangeSupplier(record, source_supplier_id);
                    }
                  }}
                  size="small"
                  style={{ width: '100%' }}
                  allowClear={false}
                  showSearch
                  placeholder="请选择"
                  options={suppliers || []}
                />
              </div>
            );
          } else {
            // const curSupplier = record?.supplier_details?.find((v: any) => v?.main)
            // return curSupplier?.supplier_name
            return <span style={getRed(record)}>{record?.supplier_name}</span>;
          }
        };
        break;
      case 'advise_qty':
        item.render = (text: any, record: any) => (
          <span
            className="xlb-table-clickBtn"
            onClick={() => {
              setDetailVisible(true);
              setDetail(record);
            }}
          >
            {text}
          </span>
        );
        break;
      case 'item_name':
        break;
      default:
        item.render = (value: any, record: any) => {
          return <div style={getRed(record)}>{value}</div>;
        };
    }
  };

  // 生成采购订单
  const handleCreateOrder = async (
    dataSource: any[],
    selectRows: any[],
    fetchData: any,
    requestForm: any,
  ) => {
    const { storehouse_id } = formRef?.current?.getFieldsValue(true);
    let newDataRow = cloneDeep(dataSource);
    newDataRow = dataSource?.reduce((pre: any, next: any) => {
      return pre.concat(next?.children);
    }, []);
    /**
     * 两个来源的id都被同步修改了，所以能匹配上
     */
    const selectedKeys = selectRows?.map((item: any) => item.id);
    const selectedRows = newDataRow?.filter((i: any) =>
      selectedKeys?.includes(i?.id),
    );
    if (selectedRows?.some((i) => !i?.supplier_id)) {
      XlbTipsModal({
        tips: `供应商为空，不能生成采购订单！`,
      });
      return;
    }
    const chooseRows = requestForm?.summary?.includes(SummaryType.BY_SUPPLIER)
      ? selectedRows
      : selectRows;
    const res = await Api.createPurchaseOrder({
      details: chooseRows?.map((v) => {
        return {
          item_id: v?.item_id,
          present_quantity: v?.present_qty,
          quantity: v?.order_qty,
          ratio: v?.purchase_ratio,
          supplier_id: v?.supplier_id,
          unit: v?.item_unit,
        };
      }),
      store_id: selectRows?.[0]?.store_id,
      storehouse_id,
    });

    if (res.code === 0) {
      if (res?.data?.results?.length) {
        XlbTipsModal({
          tips: `${res?.data?.count}个商品已生成采购订单，以下商品系统已自动过滤！`,
          tipsList: res?.data?.results,
        });
      } else {
        XlbTipsModal({
          tips: `${res?.data?.count}个商品已生成采购订单！`,
        });
      }
      // 暂时取消
      // const resT = await Api.afterCreatePurchaseOrder({ details: dataSource });
      // if (resT.code === 0) {
      //   XlbMessage.success('操作成功');
      //   fetchData();
      // }
    }
  };

  const [isBySupplier, setIsBySupplier] = useState<boolean>(false);
  const prevPost = () => {
    const values = formRef?.current?.getFieldsValue(true);
    if (!values?.store_ids?.length) {
      XlbMessage.error('补货门店必选');
      return false;
    }
    if (values?.store_ids?.length === 1 && !values?.storehouse_id) {
      XlbMessage.error('补货仓库必选');
      return false;
    }
    if (
      values?.store_ids?.length > 1 &&
      values?.item_ids?.length !== 1 &&
      values?.supplier_ids?.length !== 1
    ) {
      XlbMessage.error('门店多选时，商品档案/供应商必须单选');
      return false;
    }
    setIsBySupplier(!!values?.summary?.includes(SummaryType.BY_SUPPLIER));
    return {
      ...values,
      storehouse_id: values?.storehouse_id || void 0,
      main_supplier:
        Number(values?.summary?.includes(SummaryType.BY_SUPPLIER_MAIN)) ||
        void 0,
    };
  };

  const afterPost = (data: any[]) => {
    dataRef.current = cloneDeep(data).map((i) => {
      const objT = {
        ...i,
        children: i?.details,
      };
      delete objT.details;
      return objT;
    });
    const isBySupplierJudge = formRef?.current
      ?.getFieldsValue(true)
      ?.summary?.includes(SummaryType.BY_SUPPLIER);
    if (!isBySupplierJudge) {
      return flatMap(
        coverNewDataSource(data).map((item: any) => item.children),
      );
    }
    return coverNewDataSource(data);
  };
  tableColumn.map((v) => render(v));
  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.purchase.order.analysis'}
      tableColumn={tableColumn}
      prevPost={prevPost}
      afterPost={afterPost}
      total={(data: any[]) => {
        if (
          formRef?.current
            ?.getFieldsValue(true)
            ?.summary?.includes(SummaryType.BY_SUPPLIER)
        ) {
          return data?.reduce((pre, cur) => {
            return pre + cur?.children?.length;
          }, 0);
        }
        return data?.length;
      }}
      ref={pageRef}
    >
      <SearchForm>
        <XlbProForm
          className={styles.formWrapCus}
          formList={[
            { id: ErpFieldKeyMap?.erpCenterStoreIds, label: '补货门店' },
            {
              id: ErpFieldKeyMap?.erpPurchaseStorehouseId,
              label: '补货仓库',
              fieldProps: {
                rootClassName: styles.myDropDown,
              },
            },
            { id: 'itemIds', label: '商品档案' },
            { id: 'suppilerIds', label: '供应商' },
            {
              id: ErpFieldKeyMap?.erpIsSuggestOrder,
              fieldProps: { width: 360 },
            },
            { id: ErpFieldKeyMap?.erpSummary },
          ]}
          initialValues={{
            summary: [SummaryType.BY_SUPPLIER_MAIN],
          }}
          formRef={formRef}
        />
        <PurchaseOrderingDetailDrawer
          visible={detailVisible}
          setVisible={setDetailVisible}
          record={detail}
        />
      </SearchForm>
      <ToolBtn>
        {({
          fetchData,
          loading,
          dataSource,
          selectRow,
          setSelectRow,
          setSelectRowKeys,
          columns,
          requestForm,
        }) => {
          setSelects = setSelectRow;
          setSelectKeys = setSelectRowKeys;
          formValueRef.current = requestForm;
          return (
            <XlbButton.Group>
              <XlbButton
                type="primary"
                label="查询"
                loading={loading}
                onClick={() => fetchData(1, true)}
                icon={<span className="iconfont icon-sousuo" />}
              />
              {hasAuth(['采购智能订货', '导出']) && (
                <ExportBtn
                  tableColumns={columns}
                  dataSource={dataSource}
                  formValues={requestForm}
                  exportFieldProps={{
                    url: '/erp/hxl.erp.purchase.order.analysis.export',
                    fileName: '智能订货.xlsx',
                  }}
                />
              )}

              {hasAuth(['采购智能订货', '编辑']) && (
                <XlbButton
                  type="primary"
                  icon={<XlbIcon name="daochu" />}
                  label="生成采购订单"
                  disabled={!selectRow?.length}
                  onClick={() =>
                    handleCreateOrder(
                      dataSource || [],
                      selectRow || [],
                      fetchData,
                      requestForm,
                    )
                  }
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        ref={tableRef}
        partentPrimaryKey="supplier_id"
        primaryKey="id"
        rowGrouping={isBySupplier}
        defaultOpenAll
        selectMode="multiple"
        disabledRow={(row: any) => row.groupTitle}
        useVirtual={{ vertical: true }}
      />
    </XlbPageContainer>
  );
};

export default Index;
