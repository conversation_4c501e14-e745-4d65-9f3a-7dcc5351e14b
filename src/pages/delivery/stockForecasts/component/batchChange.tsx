import { hasAuth } from '@/utils/kit';
import {
  XlbBasicForm,
  XlbButton,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbModal,
  XlbTipsModal,
} from '@xlb/components';
import { Form, message } from 'antd';
import { useEffect, useState } from 'react';
import Api from '../server';
import style from './batchChange.less';

const BatchChange = (props: any) => {
  const { visible, handleCancel, getData } = props;
  const [loading, setIsLoading] = useState(false);
  const [form] = Form.useForm();

  const Reg = /^[0-9]+\.{0,1}[0-9]{0,10}$/;
  const closePopup = () => {
    message.success('更新成功');
    handleCancel();
    getData();
    form.resetFields();
  };

  const batchSetting = async (data: any) => {
    setIsLoading(true);
    const res = await Api.batchUpdate(data);
    setIsLoading(false);
    if (res.code === 0) {
      closePopup();
    }
  };
  const handleOk = async () => {
    try {
      await form.validateFields();
    } catch (err: any) {
      throw err;
    }
    console.log(form.getFieldsValue(true), 111);
    if (
      !form.getFieldValue('store_ids') ||
      form.getFieldValue('store_ids')?.length === 0
    ) {
      XlbTipsModal({ tips: '请选择门店' });
      return;
    }
    if (
      !form.getFieldValue('item_category_ids') ||
      form.getFieldValue('item_category_ids')?.length === 0
    ) {
      XlbTipsModal({ tips: '请选择商品分类' });
      return;
    }

    if (
      form.getFieldValue('coefficient') &&
      (parseFloat(form.getFieldValue('coefficient')) <= 0 ||
        parseFloat(form.getFieldValue('coefficient')) > 99.99)
    ) {
      return message.error(`预测系数只能大于0和小于等于99.99`);
    }

    const data = {
      store_ids: form.getFieldValue('store_ids'),
      item_category_ids: form.getFieldValue('item_category_ids'),
      coefficient: form.getFieldValue('coefficient'),
    };
    batchSetting(data);
  };

  // 导入门店
  const importStores = async () => {
    const res = await XlbImportModal({
      importUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storename.import`,
      templateUrl: `${process.env.BASE_URL}/erp-mdm/hxl.erp.storecodetemplate.download`,
      templateName: '门店导入模板',
      callback: (res: any) => {
        if (res.code !== 0) return;
        form.setFieldsValue({
          store_ids: res?.data?.store_ids ? res?.data?.store_ids : [],
          store_names: res?.data?.store_names_str
            ? res?.data?.store_names_str
            : '',
        });
      },
    });
  };

  useEffect(() => {
    if (visible) {
      return;
    }
    // console.log(headerItem.business_start_time,'12122121')
    // setSelectedObj([]); //清空弹窗中已选择的数据
  }, [visible]);

  return (
    <XlbModal
      title={'批量设置'}
      centered
      open={visible}
      maskClosable={false}
      onOk={handleOk}
      isCancel={true}
      onCancel={() => {
        form.resetFields();
        handleCancel();
      }}
      width={500}
      confirmLoading={loading}
    >
      <div>
        <XlbBasicForm
          form={form}
          className={style.form_container}
          style={{ marginTop: 20 }}
        >
          <div className={style.box}>
            <p className={style.title}>修改范围</p>
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <XlbBasicForm.Item
                  label={'门店名称'}
                  name="store_ids"
                  style={{ marginRight: '10px' }}
                >
                  <XlbInputDialog
                    style={{ width: 160 }}
                    dialogParams={{
                      type: 'store',
                      dataType: 'lists',
                      isMultiple: true,
                      data: {
                        status: true,
                      },
                    }}
                    fieldNames={{
                      idKey: 'id',
                      nameKey: 'store_name',
                    }}
                  />
                </XlbBasicForm.Item>
                <XlbButton
                  type={'text'}
                  size="small"
                  onClick={() => importStores()}
                  style={{ marginBottom: 12 }}
                >
                  导入
                </XlbButton>
              </div>
              <XlbBasicForm.Item label={'商品分类'} name={'item_category_ids'}>
                <XlbInputDialog
                  disabled={!hasAuth(['门店管理/经营属性', '编辑'])}
                  style={{ width: 192 }}
                  treeModalConfig={{
                    title: '选择商品分类', // 标题
                    url: '/erp-mdm/hxl.erp.category.find', // 请求地址
                    dataType: 'lists',
                    checkable: true, // 是否多选
                    primaryKey: 'id',
                    data: {
                      enabled: true,
                    },
                    width: 360,
                  }}
                />
              </XlbBasicForm.Item>
              <XlbBasicForm.Item
                rules={[
                  {
                    pattern: Reg,
                    message: '请输入数字类型,预测系数只能大于0和小于等于99.99',
                  },
                ]}
                label={'预测系数'}
                name={'coefficient'}
              >
                <XlbInput
                  style={{ width: 192 }}
                  size="small"
                  placeholder="请输入"
                />
              </XlbBasicForm.Item>
            </div>
          </div>
        </XlbBasicForm>
      </div>
    </XlbModal>
  );
};
export default BatchChange;