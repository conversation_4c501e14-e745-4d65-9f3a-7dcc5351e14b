// import { useKeepAliveRefresh } from '@/hooks';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { hasAuth, LStorage } from '@/utils';
import {
  CopyOutlined,
  DeleteOutlined,
  FileAddOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import type { XlbProPageModalRef, XlbTableColumnProps } from '@xlb/components';
import {
  XlbBasicForm,
  XlbButton,
  XlbDropdownButton,
  XlbForm,
  XlbPageContainer,
  XlbProPageModal,
  XlbTipsModal,
} from '@xlb/components';
import { XlbPageContainerRef } from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import Api, { formList, stateEmail } from '../data';
import Item from '../item/index';
import TableModal from './tableModal';
import { wujieBus } from '@/wujie/utils';
const { ToolBtn, Table, SearchForm } = XlbPageContainer;

const WholesalePriceAdjustment: FC = () => {
  let refresh: Function;
  const { downByProgress } = useDownload();
  const { enable_organization } = useBaseParams((state) => state);
  const [newFormList, setNewFormList] = useState<any>([]);
  const formRef = useRef<any>(null);
  const [formlist, setFormlist] = useState<any>(cloneDeep(formList));
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const pageConatainerRef = useRef<XlbPageContainerRef>(null);
  const [record, setRecord] = useState<any>({});
  const tableColumn: XlbTableColumnProps<any>[] = [
    {
      name: '序号',
      code: '_index',
      width: 70,
      align: 'center',
    },
    {
      name: '组织',
      code: 'org_name',
      width: 120,
      hidden: !enable_organization,
    },
    {
      name: '单据号',
      code: 'fid',
      features: { sortable: true },
      width: 200,
      render: (text: any) => (
        <span
          className="link cursor overwidth"
          onClick={(e) => {
            e.stopPropagation();
            setRecord({ fid: text });
            pageModalRef.current?.setOpen(true);
          }}
        >
          {text}
        </span>
      ),
    },
    {
      name: '应用门店',
      code: 'adjust_stores',
      features: { sortable: true },
      render: (text: any) => {
        return (
          <div style={{ display: 'flex' }}>
            <div
              style={{
                flex: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                wordBreak: 'normal',
              }}
            >
              {text?.length ? text[0].store_name : null}
            </div>
            {text?.length > 1 ? (
              <span
                className="link cursor"
                onClick={(e) => {
                  e.stopPropagation();
                  showStores(text);
                }}
              >
                更多
              </span>
            ) : null}
          </div>
        );
      },
      width: 160,
    },
    {
      name: '单据状态',
      code: 'state',
      features: { sortable: true },
      width: 90,
      render: (text: any) => {
        const obj = stateEmail.find((item) => item.value === text);
        return <span className={`overwidth ${obj?.type}`}>{obj?.label}</span>;
      },
    },
    {
      name: '商品数',
      code: 'item_count',
      align: 'right',
      features: { sortable: true },
      width: 90,
    },
    {
      name: '生效时间',
      code: 'effect_time',
      features: { sortable: true },
      render(text: any) {
        return moment(text).format('YYYY-MM-DD');
      },
      width: 162,
    },
    {
      name: '制单人',
      code: 'create_by',
      features: { sortable: true },
      width: 80,
    },
    {
      name: '审核人',
      code: 'audit_by',
      features: { sortable: true },
      width: 80,
    },
    {
      name: '处理人',
      code: 'handle_by',
      features: { sortable: true },
      width: 80,
    },
    {
      name: '失效人',
      code: 'invalid_by',
      features: { sortable: true },
      width: 80,
    },
    {
      name: '制单时间',
      code: 'create_time',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '审核时间',
      code: 'audit_time',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '处理时间',
      code: 'handle_time',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '失效时间',
      code: 'invalid_time',
      width: 162,
      features: { sortable: true },
    },
    {
      name: '留言备注',
      code: 'memo',
      width: 312,
      features: { sortable: true },
    },
  ];
  const ref = useRef<any>(null);
  const [form] = XlbBasicForm.useForm();

  const showStores = async (item: any) => {
    await XlbTipsModal({
      title: '应用门店',
      width: 900,
      tips: <TableModal data={item} />,
      isCancel: true,
    });
    // const res = await NiceModal.show(XlbNewTipsModal, {
    //   title: '应用门店',
    //   width: 900,
    //   tips: <TableModal data={item} />,
    //   hiddenFooter: true,
    // });
  };
  const setJudgment = () => {
    return (
      !form.getFieldValue('store_ids') &&
      !form.getFieldValue('org_ids') &&
      !form.getFieldValue('item_ids')
    );
  };
  const prevPost = () => {
    if (setJudgment()) {
      message.warning('门店不能为空');
      return false;
    }
    const {
      fid,
      store_ids,
      audit_by,
      item_ids,
      create_by,
      date_type,
      state,
      start_time,
      end_time,
      compactDatePicker,
      org_ids,
    } = form.getFieldsValue(true) || {};
    const params = {
      fid: fid,
      adjust_store_ids: store_ids,
      dates: compactDatePicker,
      audit_by: audit_by,
      item_ids: item_ids,
      create_by: create_by,
      date_type: date_type,
      state: state,
      org_ids: enable_organization ? org_ids : null,
    };
    return params;
  };

  const deleteItem = async (selectRow: any) => {
    await XlbTipsModal({
      tips: (
        <span style={{ wordBreak: 'break-all' }}>
          是否确认删除单据{selectRow.map((v: any) => v?.fid).join(',')}？
        </span>
      ),
      onOkBeforeFunction: async () => {
        const res = await Api.deleteItem({
          fids: selectRow.map((k: any) => k.fid),
        });
        if (res.code == 0) {
          message.success('操作成功');
          ref.current?.fetchData?.();
        }
        return true;
      },
    });
  };

  const copyItem = async (selectRow: any) => {
    if (selectRow?.length > 1) return message.error('只能选择一条数据');
    await XlbTipsModal({
      tips: (
        <span style={{ wordBreak: 'break-all' }}>
          是否确认复制单据{selectRow[0]?.fid}？
        </span>
      ),
      onOkBeforeFunction: async () => {
        const res = await Api.copyItem({ fid: selectRow[0]?.fid });
        if (res.code == 0) {
          message.success('操作成功');
          ref.current?.fetchData?.();
        }
        return true;
      },
    });
  };

  const exportItem = async (e: any) => {
    const values = prevPost();
    const res = await Api.exportItem(values);
    if (res.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success(res.data);
    }
  };
  const exportItemDetail = async (e: any, list: any) => {
    const data = {
      fids: list,
    };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.wholesaleadjustorder.detailbatch.export',
      data,
    );
    if (res.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success(res.data);
    }
  };
  const onValuesChange = (e: any) => {
    if (e?.org_ids) {
      formlist.find(
        (i) => i.name === 'store_ids',
      )!.dialogParams!.data!.org_ids = e?.org_ids ?? [];
      setFormlist([...formlist]);
      if (e?.org_ids?.length) {
        form.setFieldsValue({
          store_ids: [],
        });
      }
    }
  };
  const getOrgList = async () => {
    formlist.find((i) => i.name === 'org_ids')!.hidden = !enable_organization;
    // if (enable_organization) {
    //   const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.find')
    //   if (res.code == 0) {
    //     const org_list = res.data.map((i: any) => ({ value: i.id, label: i.name }))
    //     formlist.find((i) => i.name === 'org_ids')!.options = org_list
    //   }
    // }
    setFormlist([...formlist]);
  };

  useEffect(() => {
    getOrgList();
  }, [enable_organization]);
  useEffect(() => {
    form.setFieldsValue({
      start_time: moment().format('YYYY-MM-DD'),
      end_time: moment().format('YYYY-MM-DD'),
      store_ids_name: enable_organization
        ? null
        : [LStorage.get('userInfo')?.store],
      // store_ids: enable_organization ? [] : [LStorage.get('userInfo')?.store_id],
      compactDatePicker: [
        moment().format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ],
      date_type: 'CREATE',
    });
  }, []);
  // const { go } = useKeepAliveRefresh();

  return (
    <>
      <XlbProPageModal
        ref={pageModalRef}
        Content={({ onClose }) => {
          return (
            <div>
              <Item
                onBack={(back: boolean) => {
                  if (back) {
                    ref?.current?.fetchData?.();
                  }
                  pageModalRef.current?.setOpen(false);
                }}
                record={record}
              ></Item>
            </div>
          );
        }}
      >
        <div className="xlb-table-clickBtn"></div>
      </XlbProPageModal>
      <XlbPageContainer
        url={'/erp/hxl.erp.wholesaleadjustorder.page'}
        prevPost={prevPost}
        tableColumn={tableColumn}
        ref={ref}
      >
        <SearchForm>
          <XlbForm
            form={form}
            formList={formlist}
            isHideDate
            // initialValues={{
            //   start_time: moment().format('YYYY-MM-DD'),
            //   end_time: moment().format('YYYY-MM-DD'),
            //   store_ids_name: enable_organization ? null : [LStorage.get('userInfo')?.store],
            //   store_ids: enable_organization ? [] : [LStorage.get('userInfo')?.store_id],
            //   compactDatePicker: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
            //   date_type: 'CREATE'
            // }}
            getFormRecord={() => refresh()}
            onValuesChange={onValuesChange}
          />
        </SearchForm>
        {/* <XlbProForm
            formList={newFormList}
            initialValues={{
              start_time: moment().format('YYYY-MM-DD'),
              end_time: moment().format('YYYY-MM-DD'),
              store_ids_name: [LStorage.get('userInfo')?.store],
              store_ids: [LStorage.get('userInfo')?.store_id],
              date_type: 'CREATE',
              create_date: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
            }}
            ref={formRef}
          ></XlbProForm> */}
        <ToolBtn>
          {({ loading, fetchData, dataSource, selectRow, selectRowKeys }) => {
            refresh = fetchData;
            return (
              <XlbButton.Group>
                {hasAuth(['批发调价', '查询']) && (
                  <XlbButton
                    type="primary"
                    onClick={() => fetchData()}
                    loading={loading}
                    icon={<SearchOutlined />}
                  >
                    查询
                  </XlbButton>
                )}
                {hasAuth(['批发调价', '编辑']) && (
                  <XlbButton
                    type="primary"
                    onClick={() => {
                      setRecord({});
                      pageModalRef.current?.setOpen(true);
                    }}
                    loading={loading}
                    icon={<FileAddOutlined />}
                  >
                    新增
                  </XlbButton>
                )}
                {hasAuth(['批发调价', '删除']) && (
                  <XlbButton
                    disabled={!selectRow?.length}
                    type="primary"
                    onClick={() => deleteItem(selectRow)}
                    loading={loading}
                    icon={<DeleteOutlined />}
                  >
                    删除
                  </XlbButton>
                )}
                {hasAuth(['批发调价', '导出']) && (
                  // <XlbButton
                  //   disabled={!dataSource?.length}
                  //   type="primary"
                  //   onClick={(e) => exportItem(e)}
                  //   loading={loading}
                  //   icon={<UploadOutlined />}
                  // >
                  //   导出
                  // </XlbButton>
                  <XlbDropdownButton
                    label={'导出'}
                    dropList={[
                      {
                        label: '导出',
                        disabled: !dataSource?.length || loading,
                      },
                      {
                        label: '导出明细',
                        disabled: !selectRowKeys?.length || loading,
                      },
                    ]}
                    dropdownItemClick={(index, item, e) => {
                      if (index === 0) {
                        exportItem(e);
                      } else if (index === 1) {
                        exportItemDetail(e, selectRowKeys);
                      }
                    }}
                  />
                )}
                {hasAuth(['批发调价', '编辑']) && (
                  <XlbButton
                    disabled={!selectRow?.length}
                    type="primary"
                    onClick={() => copyItem(selectRow)}
                    loading={loading}
                    icon={<CopyOutlined />}
                  >
                    复制
                  </XlbButton>
                )}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <Table
          key="fid"
          primaryKey="fid"
          style={{ flex: 1 }}
          selectMode="multiple"
        />
      </XlbPageContainer>
    </>
  );
};

export default WholesalePriceAdjustment;
