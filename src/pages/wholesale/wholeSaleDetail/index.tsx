import PromiseModal from '@/components/promiseModal/PromiseModal';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import type { ContextState } from '@xlb/components';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbPageContainer,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { formList, tableColumn } from './data';
import styles from './index.less';
import { exportAll } from './server';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;
const DeliveryDetails: FC = () => {
  const { downByProgress } = useDownload();
  const [form] = XlbBasicForm.useForm();
  const [formlist, setFormlist] = useState<any>(cloneDeep(formList));
  const [footerDataSource, setFooterDataSource] = useState<any>([]);
  const { enable_organization } = useBaseParams((state) => state);
  const [newFormList, setNewFormList] = useState<any>([]);
  const formRef = useRef<any>(null);

  const checkData = () => {
    const formData = form.getFieldsValue();
    const { panelValue, compactDatePicker } = form.getFieldsValue(true);
    // const startTime = moment(start_time).format('YYYY-MM-DD')
    // const endTime = moment(end_time).format('YYYY-MM-DD')
    const data = {
      ...formData,
      stop_purchase: panelValue?.find((v: any) => v.value == 'stopPurchase')
        ?.itemKey,
      stop_sale: panelValue?.find((v: any) => v.value == 'stopSale')?.itemKey,
      audit_date:
        form.getFieldValue('time_type') === 'audit_date'
          ? compactDatePicker
          : null,
      create_date:
        form.getFieldValue('time_type') === 'create_date'
          ? compactDatePicker
          : null,
      operate_date:
        form.getFieldValue('time_type') === 'operate_date'
          ? compactDatePicker
          : null,
      payment_date:
        form.getFieldValue('time_type') === 'payment_date'
          ? compactDatePicker
          : null,
      // org_ids: enable_organization ? form.getFieldValue('org_ids') : null
    };
    return data;
  };
  // 请求前置处理
  const prevPost = () => {
    const values = checkData();
    return values;
  };

  const afterPost = (data: any) => {
    setFooterDataSource([
      {
        _index: '合计',
        basic_quantity:
          data?.basic_quantity === '****'
            ? '****'
            : Number(data?.basic_quantity)?.toFixed(3) || '0.000',
        quantity:
          data?.quantity === '****'
            ? '****'
            : Number(data?.quantity)?.toFixed(3) || '0.000',
        money:
          data?.money === '****'
            ? '****'
            : Number(data?.money)?.toFixed(2) || '0.00',
        cost_money:
          data?.cost_money === '****'
            ? '****'
            : Number(data?.cost_money)?.toFixed(2) || '0.00',
        present_quantity:
          data?.present_quantity === '****'
            ? '****'
            : Number(data?.present_quantity)?.toFixed(3) || '0.000',
        basic_present_quantity:
          data?.basic_present_quantity === '****'
            ? '****'
            : Number(data?.basic_present_quantity || 0)?.toFixed(3) || '0.000',
        no_tax_money:
          data?.no_tax_money === '****'
            ? '****'
            : Number(data?.no_tax_money)?.toFixed(2) || '0.00',
        present_money:
          data?.present_money === '****'
            ? '****'
            : Number(data?.present_money)?.toFixed(2) || '0.00',
        tax_money:
          data?.tax_money === '****'
            ? '****'
            : Number(data?.tax_money)?.toFixed(2) || '0.00',
        no_tax_purchase_money:
          data?.no_tax_purchase_money === '****'
            ? '****'
            : Number(data?.no_tax_purchase_money || 0)?.toFixed(2) || '0.00',

        purchase_money:
          data?.purchase_money === '****'
            ? '****'
            : Number(data?.purchase_money || 0)?.toFixed(2) || '0.00',
      },
    ]);
  };

  useEffect(() => {
    form.setFieldsValue({
      time_desc: 0,
      time_type: 'audit_date',
      compactDatePicker: [
        moment().format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ],
      // start_time: moment(form.getFieldValue('start_time')).format('YYYY-MM-DD'),
      // end_time: moment(form.getFieldValue('end_time')).format('YYYY-MM-DD'),
      store_ids: [LStorage.get('userInfo').store_id],
      // store_ids_name: [LStorage.get('userInfo').store]
    });
  }, []);

  const onValuesChange = (e: any) => {
    if (e?.org_ids) {
      formlist.find(
        (i) => i.name === 'store_ids',
      )!.dialogParams!.data!.org_ids = e?.org_ids ?? [];
      setFormlist([...formlist]);
      if (e?.org_ids?.length) {
        form.setFieldsValue({
          store_ids: [],
        });
      }
    }
  };

  const getOrgList = async () => {
    formlist.find((i) => i.name === 'org_ids')!.hidden = !enable_organization;
    if (enable_organization) {
      const res = await ErpRequest.post('/erp-mdm/hxl.erp.org.find', {
        level: 2,
      });
      if (res.code == 0) {
        const org_list = res.data.map((i: any) => ({
          value: i.id,
          label: i.name,
        }));
        formlist.find((i) => i.name === 'org_ids')!.options = org_list;
      }
    }
    setFormlist([...formlist]);
  };

  useEffect(() => {
    getOrgList();
  }, [enable_organization]);

  const exportItem = async (e: React.MouseEvent) => {
    const values = checkData();
    const data = { ...values };
    const res = await exportAll({
      ...data,
    });
    wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
    message.success('导出受理成功，请前往下载中心查看');
  };

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div
              className="link overwidth"
              onClick={(e) => {
                e.stopPropagation();
                NiceModal.show(PromiseModal, {
                  order_type: record.order_type, // 根据order_type决定是展示批发销售单还是批发退货单 order_type传销售单类型中文
                  order_fid: record.fid,
                });
              }}
            >
              {value}
            </div>
          );
        };
        break;
      case 'basic_price':
      // 不含税单价、单价、金额、不含税金额、赠品金额、基本单价、批发销售税额---批发价控制
      case 'no_tax_price':
      case 'price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {!hasAuth(['批发商品明细/批发价', '查询'])
                ? '****'
                : (value && parseFloat(value)?.toFixed(4)) || '0.0000'}
            </div>
          );
        };
        break;

      case 'no_tax_purchase_money':
      case 'purchase_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {!hasAuth(['批发商品明细/商品采购金额', '查询'])
                ? '****'
                : (value && parseFloat(value || 0)?.toFixed(4)) || '0.0000'}
            </div>
          );
        };
        break;

      case 'money':
      case 'no_tax_money':
      case 'present_money':
      case 'tax_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {!hasAuth(['批发商品明细/批发价', '查询'])
                ? '****'
                : (value && parseFloat(value)?.toFixed(2)) || '0.00'}
            </div>
          );
        };
        break;
      // 销售成本、赠品成本金额----成本价控制

      case 'present_cost_money':
      case 'cost_money':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {!hasAuth(['批发商品明细/成本价', '查询'])
                ? '****'
                : (value && parseFloat(value)?.toFixed(2)) || '0.00'}
            </div>
          );
        };
        break;

      case 'quantity':
      case 'present_quantity':
      case 'basic_present_quantity':
      case 'basic_quantity':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {value === '****'
                ? '****'
                : (value && parseFloat(value)?.toFixed(3)) || '0.000'}
            </div>
          );
        };
        break;
      default:
        item.render = (value: any) => <div>{value}</div>;
        break;
    }
  };

  tableColumn.map((v) => tableRender(v));

  let refresh: Function;
  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.wholesalereport.itemdetail.find'}
      tableColumn={tableColumn}
      prevPost={prevPost}
      afterPost={afterPost}
    >
      <SearchForm>
        <XlbForm
          className={styles.customPicker}
          formList={formlist}
          isHideDate
          form={form}
          onValuesChange={onValuesChange}
          getFormRecord={() => refresh()}
        />
      </SearchForm>
      <ToolBtn withHLine={false}>
        {({ dataSource, fetchData, loading, selectRow }: ContextState<any>) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['批发商品明细', '查询']) && (
                <XlbButton
                  type="primary"
                  label="查询"
                  loading={loading}
                  onClick={() => fetchData()}
                  icon={<span className="iconfont icon-sousuo" />}
                />
              )}
              {hasAuth(['批发商品明细', '查询']) && (
                <XlbButton
                  type="primary"
                  label="导出"
                  disabled={!dataSource?.length}
                  loading={loading}
                  onClick={(e) => exportItem(e)}
                  icon={<span className="iconfont icon-daochu" />}
                />
              )}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table
        key="id"
        keepDataSource={false}
        footerDataSource={footerDataSource}
        selectMode="single"
        // total={dataSource.length}
      />
    </XlbPageContainer>
  );
};
export default DeliveryDetails;
