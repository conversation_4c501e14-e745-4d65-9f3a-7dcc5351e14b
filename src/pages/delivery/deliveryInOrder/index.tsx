import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { hasAuth } from '@/utils';
import dateManipulation from '@/utils/dateManipulation';
import { formatWithCommas } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import NiceModal from '@ebay/nice-modal-react';
import {
  ContextState,
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbModal,
  XlbPageContainer,
  XlbProPageModal,
  XlbSelect,
  XlbTableColumnProps,
  XlbTipsModal,
  XlbTooltip,
} from '@xlb/components';
import { useNavigation } from '@xlb/max';
import { BatchChangeTimeModal } from './component/BatchModal';
// import { useNavigation } from '@xlb/max';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { message } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import receiveSettingModal from './component/receiveSettingModal/index';

import {
  formList,
  invoiceStateList,
  Options6,
  RECEIVER_TYPE_OPTIONS,
  settlementStateList,
  stateList,
  tableList,
} from './data';
import DetailOrder from './item';
import {
  Approvereturn,
  batchAuditInfo,
  copyInfo,
  deleteItems,
  getreasonListAll,
  orderReceive,
  RedCopy,
  syncInfo,
} from './server';
const Index = () => {
  const { downByProgress } = useDownload();
  const detailRef = useRef(null);
  const [form] = XlbBasicForm.useForm<any>();
  let fetchData = () => {};
  let setLoad = (v = false) => {};
  const userInfo = LStorage.get('userInfo');
  const [refundForm] = XlbBasicForm.useForm<any>();
  const rowData = useRef([]);
  const { enable_organization } = useBaseParams((state) => state);
  const paramsData = useRef<any>({ fid: 1 });
  const [refoundVisible, setRefoundVisible] = useState<boolean>(false); //退款弹框
  const [reasonList, setReasonlist] = useState([]); //退货原因
  const formRef = useRef<any>(null);
  const [itemArr, setItemArr] = useState<XlbTableColumnProps<any>[]>(
    JSON.parse(JSON.stringify(tableList)),
  );
  const chooseList = useRef<any[]>([]);
  const { ToolBtn, SearchForm, Table } = XlbPageContainer;
  const [stocks] = useState<any>({ value: [] }); // 仓库
  const setFormData = () => {
    LStorage.set('deliveryInOrder', {
      ...form.getFieldsValue(),
      // start_time: form.getFieldValue('start_time'),
      // end_time: form.getFieldValue('end_time'),
      // again_time: form.getFieldValue('again_time'),
      // over_time: form.getFieldValue('over_time'),
      // item_ids: form.getFieldValue('item_ids'),
      // store_ids: form.getFieldValue('store_ids'),
      // in_store_ids: form.getFieldValue('in_store_ids'),
      // storehouse_id: form.getFieldValue('storehouse_id'),
      // sortType: sortType,
      // stocks: stocks.value, // ! what
    });
  };
  const batchTime = async (chooseList = []) => {
    await NiceModal.show(BatchChangeTimeModal, {
      idsList: chooseList,
      fetchData: fetchData,
      url: '/erp/hxl.erp.order.date.change',
      idKey: 'fid_list',
      timeKey: 'change_date',
      type: 'delivery_in',
    });
  };
  //#region render table fn
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'fid':
        item.render = (value: any, record: any, index: { index: any }) => {
          return (
            <div className="overwidth cursors">
              <span
                className="link cursors"
                style={{ color: record.reverse_fid ? '#FF0000' : '#3D66FE' }}
                onClick={(e) => {
                  e.stopPropagation();
                  paramsData.current = {
                    fid: record.fid,
                    index: index.index,
                    total: rowData.current.length,
                    allRow: rowData.current,
                  };
                  detailRef.current?.open();
                }}
              >
                {value}
              </span>
            </div>
          );
        };
        break;
      // case 'cost_money':
      // case 'no_tax_cost_money':
      // 税费--成本价
      case 'tax_money':
        item.render = (value: any) => {
          return hasAuth(['调入单/成本价', '查询']) ? (
            <div className="info overwidth">{formatWithCommas(value)}</div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      // 单据金额、单据金额去税---配送价
      case 'money':
      case 'no_tax_money':
        item.render = (value: any) => {
          return hasAuth(['调入单/配送价', '查询']) ? (
            <div className="info overwidth">{formatWithCommas(value)}</div>
          ) : (
            <div className="info overwidth">{'****'}</div>
          );
        };
        break;
      case 'state':
        item.render = (value: any) => {
          const item = stateList.find((v) => v.value === value);
          return (
            <div
              style={{ color: value === 'AUDIT' ? '#ff7d01' : '' }}
              className={`overwidth ${item ? item.type : ''}`}
            >
              {item ? item.label : ''}
            </div>
          );
        };
        break;
      case 'receive_type':
        item.render = (value: any) => {
          const item = RECEIVER_TYPE_OPTIONS.find((v) => v.value === value);
          return <div className={`overwidth `}>{item ? item.label : ''}</div>;
        };
        break;
      case 'license_type':
      case 'out_license_type':
        item.render = (value: any) => {
          const typeOfLicense: any = {
            COMPANY: '企业',
            PERSONAL: '个体',
          };
          return (
            <div className="info overwidth">
              {value ? typeOfLicense[value] : ''}
            </div>
          );
        };
        break;
      case 'check_state':
        item.render = (value: any) => {
          const item = Options6.find((v) => v.value === value);
          return (
            <div className={`overwidth ${item ? item.type : ''}`}>
              {item ? item.label : ''}
            </div>
          );
        };
        break;
      case 'invoice_state':
        item.render = (value: any) => {
          const _item = invoiceStateList.find((v) => v.value === value);
          return (
            <div className={`overwidth ${_item ? _item.type : ''}`}>
              {_item ? _item.label : ''}
            </div>
          );
        };
        break;
      case 'settlement_state':
        item.render = (value: any, record: any) => {
          const item = settlementStateList.find((v) => v.value === value);
          return record.in_center_flag ? (
            <div className={`overwidth ${item ? item.type : ''}`}>
              {item ? item.label : ''}
            </div>
          ) : (
            <div className="info overwidth">{'——'}</div>
          );
        };
        break;
      case 'delivery_out_order_fids_str':
      case 'memo':
        item.render = (value: any) => {
          return (
            <XlbTooltip placement="topLeft" autoAdjustOverflow title={value}>
              <div className="info overwidth"> {value}</div>
            </XlbTooltip>
          );
        };
        break;
      case 'print_count':
        item.render = (value: any) => (
          <div className="info overwidth"> {value || 0}</div>
        );
        break;
      case 'ama_order_fid':
        item.render = (value: any, record: any) =>
          value ? (
            <div className="info overwidth"> {value}</div>
          ) : (
            <div
              className="link cursors"
              onClick={(e) => {
                if (record.state == 'INIT') return;
                e.stopPropagation();
                uploadInfo(record.fid);
              }}
            >
              {record.state == 'INIT' ? null : '上传'}
            </div>
          );
        break;
      case 'last_in_time':
      case 'create_time':
      case 'audit_time':
        item.render = (value: any) => (
          <div className="info overwidth"> {dateManipulation(value)}</div>
        );
        break;
    }
    return;
  };
  //#region render table
  //#region  删除 btn
  const deleteItem = async (data = [], fids = []) => {
    const errFid = data.filter((_) => _?.state !== 'INIT');
    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '只能删除单据状态为制单的单据!',
      });
      return;
    }
    await XlbTipsModal({
      tips: `已选择${fids.length}张单据，是否确认删除!`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await deleteItems({ fids });
        if (res.code === 0) {
          message.success('操作成功');
          fetchData();
          return true;
        }
      },
    });
  };
  //#endregion  删除
  // #region 导出
  const exportItem = async (e: any) => {
    const formData = form.getFieldsValue(true);
    const { time_type, create_date, both_reversed = [{}], ...rest } = formData;
    const item = both_reversed[0];
    const both_reversed_obj = {
      both_reversed: item?.value ? (item?.itemKey ? true : false) : undefined,
    };
    const data = {
      ...both_reversed_obj,
      [time_type]: create_date,
      ...rest,
    };
    const res = await ErpRequest.post('/erp/hxl.erp.deliveryoutorder.export', {
      ...data,
    });
    if (res.code == 0) {
      // downByProgress(e);
      message.success('导出受理成功，请前往下载中心查看');
    }
    // setisLoading(false);
  };
  //#region 批量审核
  // 审核
  const batchAudit = async (data = [], fids = []) => {
    const errFid = data.filter((_) => _?.state !== 'INIT');

    if (errFid.length > 0) {
      XlbTipsModal({
        tips: (
          <>
            <div>下列单据为审核状态，不能再次审核!</div>
            {errFid.map((_) => {
              return <div key={_.fid}>{_.fid}</div>;
            })}
          </>
        ),
      });
      return;
    }

    await XlbTipsModal({
      tips: `已选择${fids.length}张单据，是否确认审核!
    `,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await batchAuditInfo({ fids });
        if (res.code === 0) {
          message.success('操作成功');
          fetchData();
          return true;
        }
      },
    });
  };
  //#region 申请退货
  const approveItem = async (fids = []) => {
    if (fids.length > 1) {
      XlbTipsModal({
        tips: '申请退货不支持批量操作!',
      });
    } else if (fids.length === 1) {
      setRefoundVisible(true);
      const data = {
        pageSize: 200,
        pageNum: 0,
        type: 'RETURN_REQUEST',
      };
      setLoad(true);
      const res = await getreasonListAll(data);
      if (res.code == '0') {
        setReasonlist(res.data);
      }
      setLoad(false);
    }
  };

  //#region 复制
  const copyItem = async (arr = [], fids = []) => {
    // 复制不支持批量操作
    if (fids.length > 1) {
      XlbTipsModal({
        tips: '复制不支持批量操作!',
      });
      return;
    } else {
      await XlbTipsModal({
        tips: `是否确认复制单据"${fids.join(',')}"?`,
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await copyInfo({ fid: fids[0] });
          if (res.code === 0) {
            message.success('操作成功');
            fetchData();
            return true;
          }
        },
      });
    }
  };
  //#region 红冲
  const reverseItem = async (arr = [], fids = []) => {
    const errFid = arr.filter((_) => _?.state !== 'AUDIT');
    if (fids.length > 1) {
      XlbTipsModal({
        tips: '冲红复制不支持批量操作!',
      });
      return;
    }
    if (errFid.length > 0) {
      XlbTipsModal({
        tips: '仅审核单据支持冲红复制!',
      });
      return;
    } else {
      await XlbTipsModal({
        tips: (
          <div>
            <div>
              <div style={{ color: 'red' }}>
                是否确认冲红复制单据,冲红后,系统会自动审核
              </div>
              {fids[0]}
            </div>
          </div>
        ),
        isCancel: true,
        onOkBeforeFunction: async () => {
          const res = await RedCopy({ fid: fids[0] });
          if (res.code === 0) {
            message.success('操作成功');
            fetchData();
            return true;
          }
        },
      });
    }
  };
  const receive = (data = []) => {
    if (data.some((_) => _.state === 'INIT')) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: (
          <>
            {data
              .filter((_) => _.state === 'INIT')
              .map((_) => _.fid)
              .join('、')}
            <br />
            状态为制单，无法一键签收，请重新选择
          </>
        ),
      });
      return;
    }

    if (data.some((_) => _.state === 'ALLCHECK')) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: (
          <>
            {data
              .filter((_) => _.state === 'ALLCHECK')
              .map((_) => _.fid)
              .join('、')}
            <br />
            确认状态为全部确认，无法一键签收，请重新选择
          </>
        ),
      });
      return;
    }

    const reverseList = data
      .filter((item) => item.reverse_fid)
      .map((item) => item.fid);

    if (reverseList.length) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: (
          <>
            {reverseList.map((_) => _.fid).join('、')}
            <br />
            为冲红调入单，不允许操作
          </>
        ),
      });
      return;
    }
    if (data.length > 1) {
      XlbTipsModal({
        title: '提示',
        width: 510,
        tips: <>勾选{data.length}条调入单，是否一键签收,</>,
        onOk: async (e) => {
          const res = orderReceive({ fids: data.map((_) => _.fid) });
          if (res?.code === 0) {
            fetchData();
            message.success('一键签收成功');
          }
        },
      });
      return;
    }
    const { navigate } = useNavigation();
    navigate(
      '/xlb_erp/deliveryConfirm/item',
      {
        fid: data?.[0]?.fid,
        from: 'deliveryInOrder',
        fetchData: () => fetchData(),
      },
      'xlb_erp',
      true,
    );
  };

  // #region 取消退款
  const handleCancelapprove = async () => {
    refundForm.resetFields();
    setRefoundVisible(false);
  };

  //#region 退货
  const handleOkapprove = async () => {
    setLoad(true);
    const res = await Approvereturn({
      fid: chooseList.current[0],
      reason: refundForm.getFieldValue('reason'),
    });
    setLoad(false);
    handleCancelapprove();
    if (res.code == 0) {
      // handleCancelapprove()
      // setStateTips({
      //   tips: `已生成新的单据"${res.data.fid}"!`,
      //   isConfirm: true,
      //   isCancel: false,
      //   showDesc: 'approveItems'
      // })
      // setTipModalVisible(true)
      XlbTipsModal({
        tips: (
          <div>
            <div>已生成门店申请-退货申请单{res.data?.fid}</div>
            {res.data?.messages?.length > 0 ? (
              <>
                <div style={{ color: 'green' }}>
                  以下商品禁止退仓，系统已自动过滤！
                </div>
                <div>{res.data?.messages}</div>
              </>
            ) : null}
          </div>
        ),
      });
      fetchData();
    }
  };

  const uploadInfo = async (fid: any) => {
    setLoad(true);
    const res = await syncInfo({ fid: fid });
    setLoad(false);
    if (res.code == 0) {
      if (res.data) {
        message.error(res.data);
      } else {
        message.success('上传成功！');
        fetchData();
      }
    }
  };
  itemArr.forEach((_) => tableRender(_));
  useEffect(() => {
    const storage = LStorage.get('Index');
    if (storage) formRef?.current?.setFieldsValue({ ...storage });
    form.setFieldsValue({
      category_levels: [1],
      time_type: 'create_date',
      store_ids: LStorage.get('deliveryInOrder')?.store_ids || [],
      create_date: [
        moment().format('YYYY-MM-DD 00:00:00'),
        moment().format('YYYY-MM-DD 23:59:59'),
      ],
    });
  }, []);
  //#region HTML
  return (
    <>
      <XlbModal
        title="请输入"
        open={refoundVisible}
        width={308}
        isCancel={true}
        onOk={handleOkapprove}
        maskClosable={false}
        centered
        onCancel={() => {
          handleCancelapprove();
        }}
      >
        <div>
          <XlbBasicForm autoComplete="off" layout="inline" form={refundForm}>
            <span style={{ color: 'red' }}>是否确认申请退货？</span>
            <XlbBasicForm.Item label="退货申请原因：" name="reason">
              <XlbSelect style={{ width: 140 }}>
                {reasonList?.map((v: any, i: number) => {
                  return (
                    <XlbSelect.Option key={i} value={v.name}>
                      {v.name}
                    </XlbSelect.Option>
                  );
                })}
              </XlbSelect>
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      </XlbModal>
      <XlbProPageModal
        ref={detailRef}
        Content={({ onClose }) => {
          return (
            <>
              <section>
                <DetailOrder
                  parentRef={detailRef}
                  data={paramsData}
                ></DetailOrder>
              </section>
            </>
          );
        }}
      >
        <XlbPageContainer
          url={'/erp/hxl.erp.deliveryinorder.page'}
          tableColumn={itemArr}
          footerDataSource={(data) => {
            const footerData = [
              {
                _index: '合计',

                money: hasAuth(['调出单/配送价', '查询'])
                  ? data?.money?.toFixed(2) || '0.00'
                  : '****',
                quantity: data.quantity.toFixed(3) || '0.000',
                item_count: data.item_count || 0,
                tax_money: hasAuth(['调出单/成本价', '查询'])
                  ? data.tax_money.toFixed(2) || '0.00'
                  : '****',
                no_tax_money: hasAuth(['调出单/配送价', '查询'])
                  ? data.no_tax_money.toFixed(2) || '0.00'
                  : '****',
              },
            ];
            return footerData;
          }}
          prevPost={(pagin) => {
            setFormData();
            const formData = form.getFieldsValue(true);
            const both_reversed = formData.both_reversed || [{}];
            const { time_type, create_date, ...rest } = formData;
            const item = both_reversed[0];
            const both_reversed_obj = {
              both_reversed: item?.value
                ? item?.itemKey
                  ? true
                  : false
                : null,
            };
            const time_obj: any = {};
            time_obj[time_type] = create_date;
            return {
              ...pagin,
              ...rest,
              ...both_reversed_obj,
              ...time_obj,
            };
          }}
          immediatePost={false}
        >
          <ToolBtn>
            {(context: ContextState) => {
              const {
                loading,
                setLoading,
                fetchData: _fetchData,
                dataSource,
              } = context;
              fetchData = _fetchData;
              setLoad = setLoading;
              rowData.current = dataSource;
              const selectRow: any[] | undefined = context.selectRow;
              const selectRowKeys: string[] = (context.selectRow || []).map(
                (_) => _.fid,
              );
              chooseList.current = selectRowKeys;

              return (
                <XlbButton.Group>
                  <XlbButton
                    label="查询"
                    type="primary"
                    loading={loading}
                    onClick={() => _fetchData()}
                    icon={<XlbIcon name="sousuo" />}
                  />
                  {hasAuth(['调出单', '编辑']) ? (
                    <XlbButton
                      label="新增"
                      type="primary"
                      loading={loading}
                      // onClick={() =>
                      //   go('/xlb_erp/deliveryOrder/item', { fid: 1 })
                      // }
                      onClick={() => {
                        detailRef.current?.open();
                        console.log(detailRef.current);
                        paramsData.current = {
                          fid: 1,
                        };
                      }}
                      icon={<XlbIcon name="jia" />}
                    />
                  ) : null}

                  {hasAuth(['调出单', '编辑']) ? (
                    <XlbButton
                      label="批量审核"
                      type="primary"
                      disabled={!selectRowKeys.length || loading}
                      onClick={() =>
                        batchAudit(selectRow as [], selectRowKeys as [])
                      }
                      icon={<XlbIcon name="shenhe" />}
                    />
                  ) : null}
                  {hasAuth(['调出单', '删除']) ? (
                    <XlbButton
                      label="删除"
                      type="primary"
                      disabled={!selectRowKeys?.length}
                      loading={loading}
                      onClick={() =>
                        deleteItem(selectRow as [], selectRowKeys as [])
                      }
                      icon={<XlbIcon name="shanchu" />}
                    />
                  ) : null}
                  {hasAuth(['调出单', '导出']) ? (
                    <XlbButton
                      label="导出"
                      type="primary"
                      loading={loading}
                      onClick={(_) => {
                        console.log(_);
                        exportItem(_);
                      }}
                      icon={<XlbIcon name="daochu" />}
                    />
                  ) : null}

                  {hasAuth(['调出单', '编辑']) ? (
                    <XlbButton
                      label="复制"
                      type="primary"
                      disabled={!selectRowKeys.length || loading}
                      onClick={() =>
                        copyItem(selectRow as [], selectRowKeys as [])
                      }
                      icon={<XlbIcon name="fuzhi" />}
                    />
                  ) : null}
                  {hasAuth(['调出单', '编辑']) ? (
                    <XlbButton
                      label="冲红"
                      type="primary"
                      disabled={!selectRowKeys.length || loading}
                      onClick={() =>
                        reverseItem(selectRow as [], selectRowKeys as [])
                      }
                      icon={<XlbIcon name="fuzhi" />}
                    />
                  ) : null}

                  {hasAuth(['调入单', '编辑']) ? (
                    <XlbButton
                      label="申请退货"
                      type="primary"
                      disabled={!selectRowKeys.length || loading}
                      // onClick={() => approveItem(selectRowKeys)}
                      icon={<XlbIcon name="shenqingtuihuo" />}
                    />
                  ) : null}
                  {hasAuth(['调入单/日期修改', '编辑']) ? (
                    <XlbButton
                      label="日期修改"
                      type="primary"
                      disabled={!selectRowKeys.length || loading}
                      onClick={() => batchTime(selectRowKeys)}
                      preIcon={<span className="iconfont icon-xiugai1" />}
                    />
                  ) : null}
                  {hasAuth(['调入单/一键签收', '查询']) ? (
                    <XlbButton
                      label="一键签收"
                      type="primary"
                      disabled={!selectRow.length}
                      onClick={() => receive(selectRow)}
                      preIcon={
                        <span className="iconfont icon-shengchengXXdan" />
                      }
                    />
                  ) : null}
                  {hasAuth(['调入单/一键签收', '查询']) ? (
                    <XlbButton
                      label="配置"
                      type="primary"
                      onClick={() =>
                        NiceModal.show(receiveSettingModal, {
                          title: '调入单自动签收配置',
                          type: 'DELIVERY_IN',
                        })
                      }
                      preIcon={<span className="iconfont icon-jichupeizhi" />}
                    />
                  ) : null}
                </XlbButton.Group>
              );
            }}
          </ToolBtn>

          <SearchForm>
            <XlbForm
              formList={formList}
              form={form}
              isHideDate={true}
              // getFormRecord={() => refresh()}
              // onValuesChange={onValuesChange}
            />
          </SearchForm>
          <Table selectMode="multiple"></Table>
        </XlbPageContainer>
      </XlbProPageModal>
    </>
  );
};

export default Index;
