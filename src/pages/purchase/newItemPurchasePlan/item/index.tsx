import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { default as XlbFetch } from '@/utils/XlbFetch';
import { useIRouter } from '@/wujie/utils';
import { history } from '@umijs/max';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbPageContainer,
} from '@xlb/components';
import { message } from 'antd';
import { type FC } from 'react';
import { formList, tableColumn } from './data';

const { ToolBtn, Table, SearchForm } = XlbPageContainer;
const PurchaseLatestPrice: FC = () => {
  const [form] = XlbBasicForm.useForm();
  let refresh = () => {};

  // 导出
  const exportItem = async (
    requestForm: any,
    setLoading: (v: boolean) => void,
  ) => {
    setLoading(true);
    const res = await XlbFetch(
      '/erp/hxl.erp.requestorder.forcedelivery.item.export',
      requestForm,
    );
    if (res?.code === 0) {
      message.success(res?.data);
    }
    setLoading(false);
  };

  //处理查询参数
  const prevPost = () => {
    const record = history.location.state as any;
    const data = {
      store_ids: form.getFieldValue('store_ids') || [],
      item_id: record.item_id,
      out_store_id: record.store_id,
      company_id: LStorage.get('userInfo').company_id,
      operator_store_id: LStorage.get('userInfo').store_id,
      is_force: form.getFieldValue('is_force'),
      has_force: form.getFieldValue('has_force'),
    };
    return { ...data };
  };

  const { navigate } = useIRouter();
  const goback = () => {
    navigate('/xlb_erp/newItemPurchasePlan/index');
  };

  return (
    <XlbPageContainer
      url={'/erp/hxl.erp.requestorder.forcedelivery.item.find'}
      tableColumn={tableColumn}
      prevPost={prevPost}
    >
      <SearchForm>
        <XlbForm
          isHideDate
          formList={formList}
          form={form}
          getFormRecord={refresh}
        />
      </SearchForm>
      <ToolBtn>
        {({
          fetchData,
          dataSource,
          requestForm,
          loading,
          setLoading,
        }: // setCurrentIndex,
        any) => {
          refresh = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['新品采购计划', '查询']) && (
                <XlbButton
                  loading={loading}
                  label="查询"
                  type="primary"
                  onClick={() => {
                    fetchData();
                  }}
                  icon={
                    <XlbIcon name="sousuo" color="currentColor" size={16} />
                  }
                />
              )}

              {hasAuth(['新品采购计划', '导出']) && (
                <XlbButton
                  label="导出"
                  type="primary"
                  loading={loading}
                  disabled={loading || !dataSource?.length}
                  onClick={() => exportItem(requestForm, setLoading)}
                  icon={<XlbIcon size={16} name="daochu" />}
                />
              )}

              <XlbButton
                label="返回"
                type="primary"
                onClick={() => goback()}
                icon={<XlbIcon size={16} name="fanhui" />}
              />
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table primaryKey="fid" key="fid" />
    </XlbPageContainer>
  );
};

export default PurchaseLatestPrice;
