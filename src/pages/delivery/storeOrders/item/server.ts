import { XlbFetch as ErpRequest } from '@xlb/utils';

//获取数据
export const getDeliveryOutOrder = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.page', data);
};

//新增
export const addInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.save', data);
};

//删除
export const deleteInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.batchdelete', data);
};
//批量作废
export const batchinvalidInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.batchinvalid', data);
};
//合并
export const mergeInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.merge', data);
};
//复制
export const copyInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.copy', data);
};

//读取
export const readInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.read', data);
};

//更新
export const updateInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.update', data);
};

//审核
export const auditInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.audit', data);
};

//审核前校验
export const checkAuditInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.checkandadjust', data);
};

// 审核前统配校验
export const forcedeliveryItemCheck = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.storeorder.forcedelivery.item.check',
    data,
  );
};

//查询psd
export const getPsd = async (data: any) => {
  return await ErpRequest.post('/bi/hxl.bi.store.category.forecast.find', data);
};

//确认支付
export const payInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.submit', data);
};
//审核检查
export const repeatdeliverydateitem = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.check', data);
};

//批量获取门店psd
export const getBatchStorePsd = async (data: any) => {
  return await ErpRequest.post(
    '/bi/hxl.bi.store.category.forecast.find.all',
    data,
  );
};

//批量审核
export const batchAuditInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.batchaudit', data);
};

//  账号管理仓库查询
export const getStock = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.storehouse.store.find', data);
};
//查询配送中心门店
export const getCenterStores = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliverycenterstore.page', data);
};
//查询配送中心门店
export const getCenterStoresList = async () => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.store.center.find');
};
//查询门店（走默认规则）
export const getStores = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.store.short.page', data);
};
export const getPreType = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.preparetype.find', data);
};
//查询门店 （无规则）
export const getAllStores = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.store.all.shortfind', data);
};
// 发货门店
export const getAwayStores = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.store.allcenter.find', data);
};
//  查询门店额度
export const getStoreAmout = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.store.balance.read', data);
};

export const getItemInfo = async (data: any) => {
  return await ErpRequest.post('/erp-mdm/hxl.erp.item.read', data);
};
// 生成调出单
export const outorderCreate = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.storeorder.outorder.batchcreate',
    data,
  );
};
// 生成缺货补货单
export const replenishCreate = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.replenish', data);
};

// 配送参数查询
export const deliveryparamRead = async () => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryparam.read', {});
};

export const getItem = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.item.page', data);
};

// 打印
export const print = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.print', data);
};
// 判断是否弹出注册商户
export const isIncoming = async (data: any) => {
  return await ErpRequest.post('/bms/bms.merchant.citic.isincoming', data);
};
// 保存注册商户
export const incoming = async (data: any) => {
  return await ErpRequest.post('/bms/bms.merchant.citic.incoming', data);
};
// 自动调整数量
export const autoadjustItem = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.autoadjust', data);
};

//获取中心库存数据是否开启
export const getCenterAble = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.deliveryparam.read', data);
};
//锁定
export const lockDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockcheckorder.lock', data);
};
//解锁
export const unlockDetail = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.stockcheckorder.unlock', data);
};

//批量制单保存
export const batchOrderSave = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.storeorder.batchsave', data);
};

//批量制单商品
export const batchOrderGoods = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.storeorder.batchcreate.item.page',
    data,
  );
};

//获取统配商品
export const forcedelivery = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.forcedelivery.item.find', data);
};

//获取统配商品
export const getFidListsData = async (data: any) => {
  return await ErpRequest.post(
    '/erp/hxl.erp.deliverycenterstore.item.page',
    data,
  );
};
