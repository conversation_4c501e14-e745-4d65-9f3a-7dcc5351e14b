
export interface UserDeptFindReqDTO {

    /**
     * @name 关键字(名称)
     */
    keyword?: string;

}


export interface UserDeptFindResDTO {

    /**
     * @name 创建人
     */
    create_by?: string;

    /**
     * @name 创建时间
     */
    create_time?: string;

    /**
     * @name 主键id
     */
    id?: number;

    /**
     * @name 部门负责人
     */
    leader?: string;

    /**
     * @name 用户部门名称
     */
    name?: string;

    /**
     * @name 更新人
     */
    update_by?: string;

    /**
     * @name 更新时间
     */
    update_time?: string;

}


export interface UserDeptUpdateReqDTO {

    /**
     * @name id
     */
    id: number;

    /**
     * @name 部门负责人
     */
    leader?: string;

    /**
     * @name 部门名称
     */
    name: string;

}



export interface UserDeptSaveReqDTO {

    /**
     * @name 部门负责人
     */
    leader?: string;

    /**
     * @name 部门名称
     */
    name: string;

}

