import toFixed from '@/utils/toFixed';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbBlueBar,
  XlbInputNumber,
  XlbModal,
  XlbSelect,
} from '@xlb/components';
import { Alert, message } from 'antd';
import { useEffect, useState } from 'react';
import { supplierrebatepresent } from '../server';
import style from './itemDetailModal.less';

const ItemDetailModal = NiceModal.create((props: any) => {
  const { record, formatTableColumn, purchasere } = props;
  const { visible, hide } = NiceModal.useModal();
  const [form] = XlbBasicForm.useForm();
  const [info, setInfo] = useState<any>({});

  //赠品数据计算
  const getSupplierrebatepresent = async (e: any) => {
    const { ratio, unit, item_id } = form.getFieldsValue(true);
    const res = await supplierrebatepresent({
      quantity: e,
      ratio,
      item_id,
      org_id: record?.org_id,
      supplier_id: form.getFieldValue('supplier_id') || record?.supplier_id,
      unit,
    });
    if (res.code === 0 && res.data?.present_quantity >= 0) {
      record.present_quantity = res.data.present_quantity || 0;
      form.setFieldValue('present_quantity', res.data.present_quantity);
      if (res.data?.present_quantity) {
        message.success(`自动搭赠数量为${res.data.present_quantity}`);
      }
    }
  };

  const handleOk = () => {
    const chooseSupplier = record?.supplier_details.find(
      (item: any) => item.supplier_id === form.getFieldValue('supplier_id'),
    );
    const data = {
      quantity: form.getFieldValue('quantity'),
      supplier_id: form.getFieldValue('supplier_id'),
      supplier_name: chooseSupplier?.supplier_name,
      present_quantity: form.getFieldValue('present_quantity'),
      purchase_period: chooseSupplier?.purchase_period,
      supplier_prepare: form.getFieldValue('supplier_prepare'),
      safe_days: chooseSupplier?.safe_days,
    };
    Object.assign(record, data);
    formatTableColumn();
    hide();
  };

  const init = () => {
    const data = {
      ...record,
      present_quantity: toFixed(record?.['present_quantity'] ?? 0, 'QUANTITY'),
      ...purchasere,
    };
    form.setFieldsValue(data);
    setInfo(form.getFieldsValue(true));
  };

  useEffect(() => {
    if (visible) {
      init();
    }
  }, [visible]);

  return (
    <XlbModal
      title={
        <span>
          {form.getFieldValue('item_name')}
          <span className={style.subtitle}>
            ({form.getFieldValue('item_code')})
          </span>
        </span>
      }
      centered
      open={visible}
      maskClosable={false}
      wrapClassName="xlbDialog"
      onOk={handleOk}
      isCancel
      onCancel={() => {
        form.resetFields();
        hide();
      }}
      width={700}
    >
      <div className={style.container}>
        <div
          style={{
            maxHeight: 'calc(100vh - 380px)',
            overflowY: 'auto',
            marginTop: 16,
          }}
        >
          <XlbBlueBar.SubTitle title={'基本信息'} />
          <div className={style.baseContainer}>
            <span>补货仓库：{info.storehouse_name}</span>
            <span>
              统配数量：{info.force_transfer_quantity}
              {info.unit}
            </span>
            <span>交货周期：{info.purchase_period}</span>
            <span>最近收货日期：{info.latest_in_date}</span>
            <span>最近生产日期：{info.latest_producing_date}</span>
            <span>经营门店数：{info.business_store_count}</span>
            <span>经营范围：{info.business_scopes}</span>
            <span>采购规格：{info.purchase_spec}</span>
          </div>
          <XlbBlueBar.SubTitle title={`仓库出库(${info.unit})`} />
          <div className={style.unitContainer}>
            <div className={`${style.unitItem} flex-column flex-1`}>
              上期
              <span>{info.last_sale_quantity}</span>
            </div>
            <div className={`${style.unitItem} flex-column flex-1`}>
              日均
              <span style={{ color: '#f53f3f' }}>{info.avg_sale_quantity}</span>
            </div>
            <div className={`${style.unitItem} flex-column flex-1`}>
              前7天
              <span>{info.seven_day_ago_sale_quantity}</span>
            </div>
            <div className={`${style.unitItem} flex-column flex-1`}>
              前1月
              <span>{info.month_ago_sale_quantity}</span>
            </div>
            <div className={`${style.unitItem} flex-column flex-1`}>
              前2月
              <span>{info.two_month_ago_sale_quantity}</span>
            </div>
            <div className={`${style.unitItem} flex-column flex-1`}>
              前3月
              <span>{info.three_month_ago_sale_quantity}</span>
            </div>
          </div>
          <XlbBlueBar.SubTitle title={`门店销售(${info.unit})`} />
          <div className={style.unitContainer}>
            <div className={`${style.unitItem} flex-column flex-1`}>
              <span>上期前台</span>
              <span> {info?.last_store_sale_quantity || 0}</span>
            </div>
            <div className={`${style.unitItem} flex-column flex-1`}>
              日均前台
              <span style={{ color: '#f53f3f' }}>
                {info?.avg_store_sale_quantity || 0}
              </span>
            </div>
            <div className={`${style.unitItem} flex-column flex-1`}>
              前7天前台
              <span>{info?.seven_day_ago_store_sale_quantity || 0}</span>
            </div>
          </div>
        </div>
        <XlbBlueBar.SubTitle title={'补货信息'} style={{ marginTop: 10 }} />
        <Alert
          message={
            <div className="v-flex">
              {`建议订购量： `}
              <span className={style.messageTitle}>
                {`${info.suggest_quantity}${info.unit} `}
              </span>
              {` 在订量： `}
              <span className={style.messageTitle}>
                {`${info.purchase_quantity}${info.unit} `}
              </span>
              {` 库存数量： `}
              <span className={style.messageTitle}>
                {`${info.stock_quantity}${info.unit} `}
              </span>
              {` 预计可用天数： `}
              <span
                className={style.messageTitle}
              >{`${info.valid_days}天 `}</span>
            </div>
          }
          type="error"
          showIcon
          icon={
            <i
              className={'iconfont icon-jinggao'}
              style={{ color: '#f53f3f' }}
            />
          }
        />
        <div className={style.formContainer}>
          <XlbBasicForm form={form} autoComplete="off">
            <XlbBasicForm.Item name="supplier_id" label={'供应商'}>
              <XlbSelect style={{ width: 190 }} size="small">
                {record?.supplier_details?.map((v: any) => (
                  <XlbSelect.Option key={v.supplier_id} value={v.supplier_id}>
                    {v.supplier_name}
                  </XlbSelect.Option>
                ))}
              </XlbSelect>
            </XlbBasicForm.Item>
            <XlbBasicForm.Item name="quantity" label={'实际订购量'}>
              <XlbInputNumber
                controls={false}
                step={0.001}
                min={0}
                max={999999999.999}
                style={{ width: 190 }}
                onFocus={(e) => e.target.select()}
                onBlur={(e) => {
                  getSupplierrebatepresent(e.target.value);
                }}
                suffix={info.purchase_unit}
              />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item name="present_quantity" label={'搭赠数量'}>
              <XlbInputNumber
                controls={false}
                step={0.001}
                min={0}
                max={999999999.999}
                style={{ width: 190 }}
                onFocus={(e) => e.target.select()}
                suffix={info.purchase_unit}
              />
            </XlbBasicForm.Item>
            <XlbBasicForm.Item label="供应商备货">
              {info?.supplier_prepare ? '充裕' : '-'}
            </XlbBasicForm.Item>
          </XlbBasicForm>
        </div>
      </div>
    </XlbModal>
  );
});
export default ItemDetailModal;
