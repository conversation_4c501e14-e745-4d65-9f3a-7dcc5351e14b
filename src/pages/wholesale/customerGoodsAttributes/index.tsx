import { hasAuth } from '@/utils/kit';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbBasicForm,
  XlbButton,
  XlbForm,
  XlbIcon,
  XlbMessage,
  XlbSelect,
} from '@xlb/components';
import XlbPageContainer, {
  XlbPageContainerRef,
} from '@xlb/components/dist/lowcodes/XlbPageContainer';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { useRef, useState } from 'react';
import { goodsType, searchFormList, tableList, types } from './data';
import { updateInfo } from './server';
const { Table, ToolBtn, SearchForm } = XlbPageContainer;

import { wujieBus } from '@/wujie/utils';
import CustomerGoodsBatchChange from './component/batchChange/batchChange';
import CustomerGoodsAttributesCopy from './component/copy/copy';

const CustomerGoodsAttributes = () => {
  const pageRef = useRef<XlbPageContainerRef>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // searchFormList
  const [form] = XlbBasicForm.useForm();
  const [formList, setFormList] = useState<any[]>(
    JSON.parse(JSON.stringify(searchFormList)),
  );
  // table
  const [itemArr, setItemArr] = useState<any[]>(
    JSON.parse(JSON.stringify(tableList)),
  );
  const rowData = useRef<any[]>([]);

  const tableRender = (item: any) => {
    switch (item.code) {
      case 'item_type':
        item.render = (value: any) => {
          return (
            <div className="cursors">
              {goodsType.find((e) => e.value === value)?.label}
            </div>
          );
        };
        break;
      case 'stop_wholesale':
        item.render = (value: any, record: any, index: any) => {
          return record._click && hasAuth(['客户商品属性', '编辑']) ? (
            <div onClick={(e) => e.stopPropagation()}>
              <XlbSelect
                style={{ width: '100%' }}
                defaultValue={value}
                onChange={(e) => selectChange(e, record, index)}
                options={types}
              ></XlbSelect>
            </div>
          ) : (
            <div className="info overwidth">
              {types.find((e) => e.value === value)?.label}
            </div>
          );
        };
        break;
    }
    return item;
  };

  //表格更新
  const selectChange = async (event: any, record: any, index: any) => {
    rowData.current[index.index].stop_wholesale = event;
    const res = await updateInfo({
      item_id: record.item_id,
      client_id: record.client_id,
      stop_wholesale: event,
    });
    if (res?.code === 0) {
      XlbMessage.success('操作成功');
      pageRef?.current?.fetchData();
      // pageRef?.current?.setDataSource([...rowData.current]);
    }
  };

  // 处理数据
  const prevPost = () => {
    return {
      client_ids: form.getFieldValue('client_ids') || null,
      item_ids: form.getFieldValue('item_ids') || null,
      stop_wholesale: form.getFieldValue('stop_wholesale')
        ? JSON.parse(form.getFieldValue('stop_wholesale'))
        : '',
    };
  };
  const getData = async () => {
    const values = await form.validateFields();
    if (!values) return false;
    if (!form.getFieldValue('client_ids')) {
      rowData.current = [];
      XlbMessage.error('请选择批发客户');
      return false;
    }
    setIsLoading(true);
    pageRef?.current?.fetchData();
    setIsLoading(false);
  };
  const afterPost = (data: any) => {
    rowData.current = data?.content;
  };

  //导出
  const exportItem = async (e: any) => {
    const values = await form.validateFields();
    if (!values) return false;
    if (!form.getFieldValue('client_ids')) {
      rowData.current = [];
      XlbMessage.error('请选择批发客户');
      return false;
    }
    const data = prevPost();
    setIsLoading(true);
    const res = await ErpRequest.post(
      '/erp/hxl.erp.clientitemattr.export',
      data,
    );
    if (res?.code == 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      XlbMessage.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };

  // 弹窗控制
  const handleOpenCopy = () => {
    NiceModal.show(CustomerGoodsAttributesCopy, {
      getData: getData,
      client_ids: form.getFieldValue('client_ids'),
    });
  };

  const handleOpenBatch = () => {
    NiceModal.show(CustomerGoodsBatchChange, {
      getData: getData,
      client_ids: form.getFieldValue('client_ids'),
    });
  };

  return (
    <XlbPageContainer
      ref={pageRef}
      url={'/erp/hxl.erp.clientitemattr.page'}
      tableColumn={itemArr.map((v) => tableRender(v))}
      immediatePost={false}
      prevPost={() => prevPost()}
      afterPost={afterPost}
    >
      <SearchForm>
        <XlbForm form={form} formList={formList} isHideDate />
      </SearchForm>
      <ToolBtn showColumnsSetting>
        {(context) => {
          const { dataSource } = context;
          return (
            <XlbButton.Group>
              {hasAuth(['客户商品属性', '查询']) ? (
                <XlbButton
                  label="查询"
                  type={'primary'}
                  disabled={isLoading}
                  onClick={() => getData()}
                  icon={<XlbIcon name="sousuo" />}
                />
              ) : null}
              {hasAuth(['客户商品属性', '导出']) ? (
                <XlbButton
                  label="导出"
                  type={'primary'}
                  disabled={!dataSource?.length || isLoading}
                  onClick={(e: any) => exportItem(e)}
                  icon={<XlbIcon name="daochu" />}
                />
              ) : null}
              {hasAuth(['客户商品属性', '编辑']) ? (
                <XlbButton
                  label="批量设置"
                  type={'primary'}
                  disabled={isLoading}
                  onClick={() => handleOpenBatch()}
                  icon={<XlbIcon size={16} name="piliang" />}
                />
              ) : null}
              {hasAuth(['客户商品属性', '编辑']) ? (
                <XlbButton
                  label="复制"
                  type={'primary'}
                  disabled={isLoading}
                  onClick={() => handleOpenCopy()}
                  icon={<XlbIcon name="fuzhi" />}
                />
              ) : null}
            </XlbButton.Group>
          );
        }}
      </ToolBtn>
      <Table primaryKey="_index" selectMode="single" isLoading={isLoading} />
    </XlbPageContainer>
  );
};

export default CustomerGoodsAttributes;
