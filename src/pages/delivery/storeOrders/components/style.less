.spanss {
  display: inline-block;
  width: 15px;
  height: 15px;
  padding-left: 4px;
  color: white;
  font-size: 12px;
  line-height: 15px;
  text-align: center;
  background-color: #000;
  border-radius: 50%;
  cursor: default;
}

.line {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 32px;
  margin-bottom: 12px;
  .before {
    width: 234px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px 0px 0px 4px;
    background: #f7f8fa;
    font-size: 14px;
    color: #3d3d3d;
  }
  .center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #ffffff;
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    color: #3d3d3d;
  }
  .right {
    width: 234px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border-radius: 4px 0px 0px 4px;
    background: #f3f8ff;
    font-size: 14px;
    color: #3d3d3d;
  }
}
.titleline {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  cursor: pointer;
  .titlelineChild {
    display: flex;
    align-items: center;
  }
  .title {
    color: #86909c;
    margin-left: 4px;
  }
}
.group_box {
  max-height: 340px;
  overflow-y: auto;
}

:global .wrapper-left .xlbUploadCardContainer {
  background: #f7f8fa !important;
}

:global .wrapper-right .xlbUploadCardContainer {
  background: #e8f1ff !important;
}

:global .xlb_dialog .ant-modal-body {
  overflow-y: hidden;
}

/* 遮罩样式 */
:global .custom-preview-modal .ant-modal-content {
  background: rgba(0, 0, 0, 0.5); /* 半透明黑色背景 */
  box-shadow: none;
  z-index: 99;
}

:global .custom-preview-modal .ant-modal-body {
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 90vh; /* 弹窗高度 */
  z-index: 99;
}

/* 图片容器样式 */
:global .custom-preview-content {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 16px; /* 图片之间的间距 */
  justify-content: center;
  align-items: center;
}

/* 图片样式 */
:global .custom-preview-image {
  max-width: 45%; /* 每张图片的宽度 */
  max-height: 80vh; /* 图片的最大高度 */
  object-fit: contain;
  border-radius: 8px; /* 圆角 */
}

:global .xlb_pictureItem .xlb_pictureItem_container .ant-image img {
  width: 100%;
  height: auto;
}
