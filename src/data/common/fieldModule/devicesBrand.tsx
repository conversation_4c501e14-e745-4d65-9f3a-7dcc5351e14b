export const DevicesBrandKeyMap = {
  device_id: 'device_id'
}

export const devicesBrandConfig: any[] = [
  // 设备分类
  {
    tag: 'ERP',
    label: '关联分类',
    id: 'device_id',
    name: 'category_id',
    componentType: 'select',
    options: [],
    request: async (formValues: any, anybaseURL: any, globalFetch: any) => {
      const res = await globalFetch.post(
        anybaseURL + '/erp/hxl.erp.storehardware.category.find',
        {}
      )
      if (res.code == 0) {
        return res.data
          ?.filter((v: any) => v?.id)
          ?.map((item: any) => {
            return {
              label: item.name,
              value: item.id
            }
          })
      }
      return []
    }
  }
]
