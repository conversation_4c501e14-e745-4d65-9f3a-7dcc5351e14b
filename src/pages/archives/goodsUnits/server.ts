import {XlbFetch as ErpRequest } from '@xlb/utils';
import { ItemDeptResDTO } from './type';
const POST = ErpRequest.post;
const urlHeader = '/erp/hxl.erp.itemunit.';

export default {
  getDataList: (data: ItemDeptResDTO) => POST(urlHeader + 'find', { data }),
  save: (data: ItemDeptResDTO) => POST(urlHeader + 'save', { data }),
  update: (data: ItemDeptResDTO) => POST(urlHeader + 'update', { data }),
  delete: (data: ItemDeptResDTO) => POST(urlHeader + 'delete', { data }),
};
