
const CONST = {
    ENVIRONMENT: {
        DEV: 'development',
        PRODUCTION: 'production',
    },
    // 范围
    RANGE: {
        // 最大值
        MAX: {
            MONEY:  999999999.99,
            PRICE: 999999999.9999,
            QUANTITY: 999999999.999,
            // 备注最大长度
            MEMO: 200
        },
        // 最小值
        MIN: {
            MONEY: -999999999.99,
            PRICE: 0,
            QUANTITY: -999999999.999
        }
    },
    ZERO: {
        // 固定的位数
        FIXED: {
            QUANTITY: 3,
            MONEY: 2,
            PRICE: 4,
        },
        // 格式化 数量 金额 价格
        FORMAT: {
            QUANTITY: '0.000',
            MONEY: '0.00',
            PRICE: '0.0000',
        }
    },
    // 通用正则常量
    REGEX: {
        // 数字校验
        NUMBER: /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/,
    },
};

export default CONST;
