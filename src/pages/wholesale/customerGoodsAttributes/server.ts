import { XlbFetch as ErpRequest } from '@xlb/utils';

// 获取数据
export const getAllData = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.clientitemattr.page', data);
};
//更新
export const updateInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.clientitemattr.update', data);
};
//批量设置
export const batchUpdate = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.clientitemattr.batchupdate', data);
};
//复制
export const copyInfo = async (data: any) => {
  return await ErpRequest.post('/erp/hxl.erp.clientitemattr.copy', data);
};
