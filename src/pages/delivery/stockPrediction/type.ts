
export interface TikTokCouponPageReqDTO {

    /**
     * @name 消费券券码
     */
    certificate_id?: string;

    /**
     * @name 消费券名称
     */
    coupon_names?: [];

    /**
     * @name 消费券来源
     */
    coupon_source?: string;

    /**
     * @name 是否分账
     */
    divided?: boolean;

    /**
     * @name 排序
     */
    orders?: OrderParam[];

    /**
     * @name 页号
     */
    page_number?: number;

    /**
     * @name 每页条数
     */
    page_size?: number;

    /**
     * @name 核销门店
     */
    store_ids?: [];

    /**
     * @name 核销单号
     */
    use_order_fid?: string;

    /**
     * @name 核销时间 yyyy-MM-dd格式
     */
    verify_time?: Array<string | number>;

}

export interface OrderParam {

    /**
     * @name 方向
     */
    direction?: string;

    /**
     * @name 属性
     */
    property?: string;

}


export interface resDTO {
    content: CouponVerifyResDTO[],
    coupon_pay_amount: number,
    market_price: number,
    original_amount: number,
    platform_discount_amount: number,
    share_money: number,
    total_elements: number,
    total_pages: number
}

export interface CouponVerifyResDTO {

    /**
     * @name 券id
     */
    coupon_id?: number;

    /**
     * @name 券名称
     */
    coupon_name?: string;

    /**
     * @name 券号
     */
    coupon_num?: string;

    /**
     * @name 订单实收金额
     */
    coupon_pay_amount?: number;

    /**
     * @name 商家补贴金额
     */
    market_price?: number;

    /**
     * @name 券售卖金额
     */
    original_amount?: number;

    /**
     * @name 平台优惠金额
     */
    platform_discount_amount?: number;

    /**
     * @name 是否分账
     */
    share?: boolean;

    /**
     * @name 分账金额
     */
    share_money?: number;

    /**
     * @name 门店id
     */
    store_id?: number;

    /**
     * @name 门店名称
     */
    store_name?: string;

    /**
     * @name 券来源
     */
    type?: string;

    /**
     * @name 券来源
     */
    type_str?: string;

    /**
     * @name 核销时间
     */
    verify_time?: string;

}
