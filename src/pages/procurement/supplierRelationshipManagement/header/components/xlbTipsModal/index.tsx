import { Modal, ModalProps, Button } from 'antd'
import { useEffect } from 'react'

type TipModalProps = {
  setTipModal: any
  handConfirm: any
  handCancel: any
  state: {
    tips: any
    isConfirm: boolean
    isCancel: boolean
    showDesc: string
  }
  setTipState: any
} & ModalProps
export const XlbTipsModal = (props: TipModalProps) => {
  const { visible, setTipModal, handConfirm, state, setTipState, title, handCancel } = props

  useEffect(() => {
    if (!visible) {
      setTipState({
        tips: '',
        isConfirm: true,
        isCancel: true,
        showDesc: ''
      })
    }
  }, [visible])

  return (
    <Modal
      title={title}
      visible={visible}
      width="450px"
      wrapClassName="xlbDialog"
      onCancel={()=>handCancel(true)}
      footer={[
        state.isCancel ? (
          <Button key={0} type="default" onClick={handCancel}>
            取消
          </Button>
        ) : null,
        state.isConfirm ? (
          <Button type="primary" onClick={handConfirm} key={1}>
            确定
          </Button>
        ) : null
      ]}
      centered
    >
      <div style={{ width: 'auto', height: 'auto', fontSize: '16px' }} className="v-flex">
        {state.tips}
      </div>
    </Modal>
  )
}