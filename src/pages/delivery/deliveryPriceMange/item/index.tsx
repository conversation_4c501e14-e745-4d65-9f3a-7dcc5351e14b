import { orderStatusIcons } from '@/components/common/data';
import { useBaseParams } from '@/hooks/useBaseParams';
import useDownload from '@/hooks/useDownload';
import { hasAuth } from '@/utils/kit';
import { LStorage } from '@/utils/storage';
import { SearchOutlined } from '@ant-design/icons';
import {
  XlbBasicData,
  XlbBasicForm,
  XlbButton,
  XlbCheckbox,
  XlbDatePicker,
  XlbDropdownButton,
  XlbIcon,
  XlbImportModal,
  XlbInput,
  XlbInputDialog,
  XlbInputNumber,
  XlbModal,
  XlbSelect,
  XlbShortTable,
  XlbTableColumnProps,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { Form, Input, message, Select, Tabs } from 'antd';
import type { EditorState } from 'braft-editor';
import BraftEditor from 'braft-editor';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { goodsType, itemTableListDetail, priceType } from '../data';
import Api from '../server';
import PreView from './components/preView';
import Template from './components/Template';
import XlbBaseGoods from './components/xlbBaseGoods';
import styles from './index.less';
const { TabPane } = Tabs;
const { Option } = Select;

const DeliveryPriceMangeItem = (props) => {
  const { record, onBack } = props;
  const userInfo = LStorage.get('userInfo');
  const [form] = XlbBasicForm.useForm();
  const [rowData, setRowData] = useState<any[]>([]);
  const [chooseList, setChooseList] = useState<any>(null);
  const [batchType, setBatchType] = useState<string>('RATIO');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [itemArrdetail, setdetailItemArr] = useState<
    XlbTableColumnProps<any>[]
  >(JSON.parse(JSON.stringify(itemTableListDetail)));
  const [pagin, setPagin] = useState({ pageSize: 200, pageNum: 1, total: 0 });
  const { downByProgress } = useDownload();
  const formBox = useRef<HTMLDivElement | null>(null);
  const [ErrorState, setErrorState] = useState<any>({
    isCancel: false,
    isConfirm: true,
    msg: '',
    fail_list: [],
  });
  const [batchVisible, setBatchVisible] = useState<boolean>(false);
  const [fid, setFid] = useState<any>();
  const [info, setInfo] = useState<any>({ state: 'INIT', value: null });
  const [allRow, setAllRow] = useState<any>({}); //主页查询数据
  const [nextLoding, setNextLoding] = useState<boolean>(false); //下一单
  const [lastLoding, setLastLoding] = useState<boolean>(false); //上一单
  const [goodsVisible, setgoodsVisible] = useState<boolean>(false);
  const [edit, setEdit] = useState<boolean>(false); //触发表格编辑
  const [storeModal, setStoreModal] = useState<any>({
    modalVisible: false, // 弹窗是否展示
    storeItem: {}, // 接收弹窗选中的值
    modalState: {
      isMultiple: true, //（默认false单选，true多选）是否多选
      modalType: 'store', //弹窗类型 （必传）'item'商品 'store'门店  'supplier'供应商
      value: '',
    },
  });
  const [backFresh, setBackFresh] = useState<any>(false);
  const [noticeTemplateList, setNoticeTemplateList] = useState<any>([]);
  const [visible, setVisible] = useState(false);
  const [templateVisible, setTemplateVisible] = useState<boolean>(false);
  const [editorState, setEditorState] = useState<EditorState>(
    BraftEditor.createEditorState(''),
  );
  const [details, setDetails] = useState<any>({});
  const { enable_organization } = useBaseParams((state) => state);
  const [activeKey, setActiveKey] = useState('baseInfo');
  const [pageSize, setPageSize] = useState<number>(200);
  const [, forceUpdate] = React.useState({});
  const tableRender = (item: any) => {
    switch (item.code) {
      case 'basic_price':
        item.render = (value: any) => {
          return (
            <div className="info">
              {hasAuth(['配送价调整/档案采购价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'basic_unit_value':
        item.render = (value: any, record: any, index: any) => {
          return record?._click &&
            record?.item_name &&
            hasAuth(['配送价调整/配送价', '编辑']) ? (
            <XlbInputNumber
              className="full-box"
              id={item.code + '-' + index['index'].toString()}
              value={value}
              // onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index['index'], item.code, record)
              }
              // onBlur={(e) => inputBlur(e, index['index'], item.code, record)}
              // onClick={(e) => {
              //   e.stopPropagation();
              // }}
              // onPressEnter={(e) => {
              //   onPressEnter(item.code, index['index']);
              // }}
            />
          ) : (
            <div className="info">
              {value !== null && hasAuth(['配送价调整/配送价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'value':
        item.render = (value: any, record: any, index: any) => {
          return record?._click &&
            record?.item_name &&
            hasAuth(['配送价调整/配送价', '编辑']) ? (
            <XlbInputNumber
              className="full-box"
              id={item.code + '-' + index['index'].toString()}
              value={value}
              // onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index['index'], item.code, record)
              }
              // onBlur={(e) => inputBlur(e, index['index'], item.code, record)}
              // onClick={(e) => {
              //   e.stopPropagation();
              // }}
              // onPressEnter={(e) => {
              //   onPressEnter(item.code, index['index']);
              // }}
            />
          ) : (
            <div className="info">
              {value !== null && hasAuth(['配送价调整/配送价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;
      case 'basic_unit_price':
        item.render = (value: any, record: any, index: any) => {
          return (
            <div className="info">
              {record?.price !== null && hasAuth(['配送价调整/配送价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
      case 'price':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info">
              {value !== null && hasAuth(['配送价调整/配送价', '查询'])
                ? Number(value || 0)?.toFixed(4)
                : '****'}
            </div>
          );
        };
        break;

      case 'type':
        item.render = (value: any, record: any, index: any) => {
          return record?._click &&
            record?.item_name &&
            hasAuth(['配送价调整/配送价', '编辑']) ? (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <XlbSelect
                className="full-box"
                value={record[item?.code]}
                onChange={(e) =>
                  inputChange(e, index['index'], item.code, record)
                }
              >
                <XlbSelect.Option key={0} value={'NULL'}>
                  {'　'}
                </XlbSelect.Option>
                <XlbSelect.Option key={1} value={'RATIO'}>
                  按比例
                </XlbSelect.Option>
                <XlbSelect.Option key={2} value={'MONEY'}>
                  按金额
                </XlbSelect.Option>
                <XlbSelect.Option key={3} value={'FIXED_MONEY'}>
                  固定金额
                </XlbSelect.Option>
              </XlbSelect>
            </div>
          ) : (
            <div className="info">
              {priceType.find((v) => v.value === value)?.label}
            </div>
          );
        };
        break;
      case 'unit':
        item.render = (value: any, record: any) => {
          return (
            <div className="info">
              {form.getFieldValue('unit_type') === 'DELIVERY'
                ? record.delivery_unit
                : record.basic_unit}
            </div>
          );
        };
        break;
      case 'memo':
        item.render = (value: any, record: any, index: any) => {
          return record?._click && record?.item_name ? (
            <Input
              className="full-box"
              id={item.code + '-' + index['index'].toString()}
              defaultValue={record[item?.code]}
              onFocus={(e) => e.target.select()}
              onChange={(e) =>
                inputChange(e, index['index'], item.code, record)
              }
              // onBlur={(e) => inputBlur(e, index['index'], item.code, record)}
              // onClick={(e) => {
              //   e.stopPropagation();
              // }}
              // onPressEnter={(e) => {
              //   onPressEnter(item.code, index);
              // }}
            />
          ) : (
            <div className="info">{value}</div>
          );
        };
        break;
      case 'item_type':
        item.render = (value: any, record: any, index: number) => {
          return (
            <div className="info overwidth">
              {goodsType.find((v) => v.value === value)?.label}
            </div>
          );
        };
        break;
    }
    return item;
  };
  //回车事件
  const onPressEnter = (code: any, index: any) => {
    Promise.resolve()
      .then(() => {
        rowData[index].edit = false;
        index + 1 == rowData.length
          ? (rowData[0].edit = true)
          : (rowData[index + 1].edit = true);
        setRowData(JSON.parse(JSON.stringify(rowData)));
      })
      .then(() => {
        const inputBox =
          index + 1 == rowData.length
            ? document.getElementById(code + '-' + (0).toString())
            : document.getElementById(code + '-' + (index + 1).toString());
        inputBox?.focus();
      });
  };
  //输入框改变
  const inputChange = (e: any, index: any, key: any, record: any) => {
    console.log('🚀 ~ inputChange ~ e:', e);
    setEdit(true);
    switch (key) {
      case 'basic_unit_value':
        record[key] = record['type'] == 'NULL' ? null : Number(e).toFixed(4);
        record['old_basic_unit_value'] =
          record['type'] == 'NULL' ? null : Number(e).toFixed(4);
        record['value'] = e * record['delivery_ratio'];
        record['old_value'] = e * record['delivery_ratio'];
        record['price'] = countMoney(
          record['type'],
          record['value'],
          record['basic_price'],
        );
        record['basic_unit_price'] =
          countMoney(record['type'], record['value'], record['basic_price']) /
          record['delivery_ratio'];

        break;
      case 'value':
        record[key] = record['type'] == 'NULL' ? null : Number(e).toFixed(4);
        record['price'] = countMoney(record['type'], e, record['basic_price']);
        record['old_value'] =
          record['type'] == 'NULL' ? null : Number(e).toFixed(4);
        record['basic_unit_value'] = record[key] / record['delivery_ratio'];
        record['old_basic_unit_value'] = record[key] / record['delivery_ratio'];
        record['basic_unit_price'] = record['price'] / record['delivery_ratio'];
        break;
      case 'type':
        record[key] = e;
        e === 'NULL' && (record['value'] = null);
        record['price'] = countMoney(e, record['value'], record['basic_price']);
        record['old_type'] = e;
        record['basic_unit_price'] =
          countMoney(e, record['value'], record['basic_price']) /
          record['delivery_ratio'];
        record['basic_unit_value'] =
          e == 'RATIO'
            ? record['value']
            : record['value'] / record['delivery_ratio'];
        break;
      default:
        record[key] = e;
        break;
    }
    setRowData([...rowData]);
  };
  //失去焦点
  const inputBlur = (e: any, index: any, key: any, record) => {
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (
      key === 'value' &&
      (!regPos.test(e.target.value) || e.target.value < 0)
    ) {
      XlbTipsModal({
        tips: `请输入>=0的数字`,
      });
      record[key] = 0;
      return false;
    }
    if (key == 'memo' && e.target.value.length > 200) {
      XlbTipsModal({
        tips: `请输入>=0的数字`,
      });
      record[key] = e.target.value.substring(0, 200);
      return false;
    }
  };
  //读取信息
  const readinfo = async (fid: any) => {
    setIsLoading(true);
    const res = await Api.readInfo({ fid });
    if (res.code === 0) {
      form.setFieldsValue({
        ...res.data,
        org_id: [res.data.two_level_org_id],
        org_name: res.data.two_level_org_name,
        notice_send_store_ids: res.data.send_stores.map((v: any) => v.id),
        notice_send_store_names: res.data.send_stores
          .map((v: any) => v.store_name)
          .join(','),
        create_time: res.data.create_time?.slice(0, 10),
        audit_time: res.data.audit_time?.slice(0, 10),
        updata_time: res.data.updata_time?.slice(0, 10),
        store_id: res.data.store_id,
        store_name: res.data.store_name,
        store_names: res.data.stores.map((v) => v.store_name).join(','),
        store_ids: res.data.stores.map((v) => v.id),
        supply_date: moment(res.data.supply_date),
        invalid_time: res.data?.invalid_time
          ? moment(res.data?.invalid_time)
          : null,
        invalid_type: res.data?.invalid_type || 0,
        unit_type: res.data.unit_type,
        memo: res.data.memo,
        send_time: res.data?.send_time && moment(res.data.send_time),
      });
      if (res.data?.notice_flag) {
        onValuesChange({
          notice_flag: res.data?.notice_flag,
          notice_template_id: res.data?.notice_template_id,
        });
      }
      itemArrdetail.find((v) => v.code === 'unit')!.name =
        res.data?.unit_type === 'DELIVERY' ? '配送单位' : '基本单位';
      setFid(res.data.fid);
      setdetailItemArr([...itemArrdetail]);
      setInfo({ ...res.data, state: res.data?.state });
      setRowData(
        res.data.details.map((v) => ({
          ...v,
          basic_price:
            res.data.unit_type === 'DELIVERY'
              ? Number(v.basic_price) * Number(v.delivery_ratio)
              : v.basic_price,
          basic_unit_value:
            form.getFieldValue('unit_type') === 'DELIVERY' && v.type !== 'RATIO'
              ? (Number(v.value) * Number(v.delivery_ratio)) / v.delivery_ratio
              : v.value,
          basic_unit_price:
            countMoney(
              v.type,
              form.getFieldValue('unit_type') === 'DELIVERY' &&
                v.type !== 'RATIO'
                ? Number(v.value) * Number(v.delivery_ratio)
                : v.value,
              form.getFieldValue('unit_type') === 'DELIVERY'
                ? Number(v.basic_price) * Number(v.delivery_ratio)
                : v.basic_price,
            ) / v.delivery_ratio,
          value:
            res.data.unit_type === 'DELIVERY' && v.type !== 'RATIO'
              ? Number(v.value) * Number(v.delivery_ratio)
              : v.value,
          price: countMoney(
            v.type,
            res.data.unit_type === 'DELIVERY' && v.type !== 'RATIO'
              ? Number(v.value) * Number(v.delivery_ratio)
              : v.value,
            res.data.unit_type === 'DELIVERY'
              ? Number(v.basic_price) * Number(v.delivery_ratio)
              : v.basic_price,
          ),
        })),
      );
      setPagin({ ...pagin, total: res.data.details.length });
    }
    setIsLoading(false);
  };
  const getNotice = async () => {
    const res = await Api.getNotice({ order_type: 'DELIVERY', state: 'AUDIT' });
    if (res.code === 0) {
      return res.data?.map((v: any) => ({ ...v, value: v.id, label: v.title }));
    }
    return false;
  };
  const onValuesChange = async (values: any) => {
    if (values?.notice_flag && !noticeTemplateList?.length) {
      const data = await getNotice();
      if (data?.length) {
        setNoticeTemplateList(data);
      }
      if (typeof data !== 'boolean' && !data?.length) {
        message.error('配送调价公告模版不存在，请先去配置');
      }
    }
    if (values?.notice_template_id) {
      const res = await Api.getNoticeContent({ id: values.notice_template_id });
      if (res.code === 0) {
        setDetails({ ...res.data, title: form.getFieldValue('notice_title') });
        setEditorState(BraftEditor.createEditorState(res.data?.content));
      }
    }
  };
  useEffect(() => {
    if (record.fid !== 1 && record?.allRow?.length) {
      const item = record?.allRow?.find((v: any) => v.fid === record.fid);
      onValuesChange(item);
    }
    setFid(record.fid);
    setAllRow(record);
    if (record.index === 0) setLastLoding(true);
    if (record.index === record.total - 1) setNextLoding(true);
    if (record.fid === 1) {
      form.setFieldsValue({
        store_name: userInfo.store_name,
        store_id: userInfo.store_id,
        supply_date: moment(),
      });
    } else {
      readinfo(record.fid);
    }
    itemArrdetail.forEach((item) => {
      if (item.code === 'basic_price') {
        item.name = enable_organization ? '组织采购价' : '档案采购价';
      }
    });
    setdetailItemArr([...itemArrdetail]);
  }, []);

  // 保存 审核 反审核t:string
  const operateOrder = async (t: string) => {
    const { invalid_time, supply_date, send_time } = form.getFieldsValue();
    if (
      invalid_time &&
      moment(invalid_time, 'YYYY-MM-DD HH:mm:ss').format('x') <
        moment(supply_date, 'YYYY-MM-DD HH:mm:ss').format('x')
    ) {
      XlbTipsModal({
        tips: '失效时间不能小于应用日期！',
      });
      return false;
    }
    if (
      send_time &&
      moment(send_time, 'YYYY-MM-DD HH:mm:ss').isBefore(moment())
    ) {
      XlbTipsModal({
        tips: '公告推送时间不能小于当前时间！',
      });
      return;
    }
    const notice_flag = form.getFieldValue('notice_flag');
    const notice_template_id = form.getFieldValue('notice_template_id');
    const notice_send_store_ids = form.getFieldValue('notice_send_store_ids');
    const notice_title = form.getFieldValue('notice_title');
    if (notice_flag && !notice_template_id) {
      XlbTipsModal({
        tips: '配送调价公告模版不能为空',
      });
      return;
    }
    if (notice_flag && !notice_title) {
      XlbTipsModal({
        tips: '零售调价公告模版标题不能为空',
      });
      return false;
    }
    if (notice_flag && !notice_send_store_ids) {
      XlbTipsModal({
        tips: '零售调价公告模版发送门店不能为空',
      });
      return false;
    }

    rowData[rowData.length - 1]?.newRow &&
      rowData.splice(rowData.length - 1, 1);
    if (rowData[0]?.newRow || rowData.length == 0) {
      XlbTipsModal({
        tips: '请先添加商品！',
      });
      return false;
    }
    const data = {
      org_id: enable_organization ? form.getFieldValue('org_id') : undefined,
      store_ids: form.getFieldValue('store_ids') || '',
      store_id: form.getFieldValue('store_id') || '',
      notice_template_id: notice_flag ? notice_template_id : undefined,
      notice_title: notice_flag ? notice_title : undefined,
      notice_flag: notice_flag ? notice_flag : undefined,
      notice_send_store_ids: notice_flag ? notice_send_store_ids : undefined,
      memo: form.getFieldValue('memo'),
      unit_type: form.getFieldValue('unit_type'),
      supply_date:
        moment(form.getFieldValue('supply_date')).format('YYYY-MM-DD HH:mm:00') ||
        undefined,
      fid: form.getFieldValue('fid'),
      invalid_time: invalid_time
        ? moment(form.getFieldValue('invalid_time')).format(
            'YYYY-MM-DD HH:mm:59',
          )
        : undefined,
      send_time: send_time
        ? moment(send_time).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      invalid_type: form.getFieldValue('invalid_type'),
      details: rowData.map((v) => ({
        ...v,
        basic_unit_value:
          form.getFieldValue('unit_type') === 'DELIVERY' && v.type !== 'RATIO'
            ? v.basic_unit_value / v.delivery_ratio
            : v.basic_unit_value,
        basic_unit_price:
          form.getFieldValue('unit_type') === 'DELIVERY'
            ? v.basic_unit_price / v.delivery_ratio
            : v.basic_unit_price,
        value:
          form.getFieldValue('unit_type') === 'DELIVERY' && v.type !== 'RATIO'
            ? v.value / v.delivery_ratio
            : v.value,
        basic_price:
          form.getFieldValue('unit_type') === 'DELIVERY'
            ? v.basic_price / v.delivery_ratio
            : v.basic_price,
        price:
          form.getFieldValue('unit_type') === 'DELIVERY'
            ? v.price / v.delivery_ratio
            : v.price,
      })),
    };
    let res: any = null;
    setIsLoading(true);
    t === '保存' && fid === 1 && (res = await Api.addInfo(data));
    t === '保存' && fid !== 1 && (res = await Api.updateInfo(data));
    t === '审核' && (res = await Api.auditInfo(data));
    t === '反审核' &&
      (res = await Api.unAuditInfo({
        ...data,
        fids: [form.getFieldValue('fid')],
      }));
    setIsLoading(false);
    if (res.code == 0) {
      setEdit(false);
      message.success('操作成功');
      readinfo(res.data?.fid || form.getFieldValue('fid'));
      setBackFresh(true);
    }
  };

  //返回前判断保存状态
  const goBack = async () => {
    if (edit) {
      await XlbTipsModal({
        tips: '单据未保存，是否确认返回？',
        onOkBeforeFunction: async () => {
          onBack();
          return true;
        },
      });
    }
    onBack();
    // history.push('/xlb_erp/deliveryPriceMange/index', { refreshPage: true })
  };
  //弹窗取消事件
  const handleCancel = () => {
    setStoreModal({
      ...storeModal,
      modalVisible: false,
    });
  };
  // 批量添加
  const confirmAdd = (list: any, add_type = 'item') => {
    let filterArr = [...list];
    const ids = rowData.map((v) => v.item_id);
    const repeatArr = list.filter((v) =>
      ids.includes(add_type === 'import' ? v.item_id : v.id),
    );
    const rName = [
      repeatArr
        .map((v) => `【${add_type === 'import' ? v.item_name : v.name}】`)
        .join('、'),
    ];
    if (repeatArr.length) {
      XlbTipsModal({
        tips: '以下商品已存在，不允许重复添加，系统已自动过滤！',
        tipsList: rName,
      });
    }
    filterArr = list.filter(
      (item: any) =>
        !ids.includes(add_type === 'import' ? item?.item_id : item.id),
    );
    const newList = filterArr?.map((v) => {
      return {
        ...v,
        item_id: add_type === 'import' ? v.item_id : v.id,
        item_code: add_type === 'import' ? v.item_code : v.code,
        item_bar_code: add_type === 'import' ? v.item_bar_code : v.bar_code,
        item_name: add_type === 'import' ? v.item_name : v.name,
        item_spec: add_type === 'import' ? v.item_spec : v.purchase_spec,
        unit:
          form.getFieldValue('unit_type') === 'DELIVERY'
            ? v.delivery_unit
            : v.basic_unit,
        basic_unit: v.basic_unit,
        basic_unit_value:
          form.getFieldValue('unit_type') === 'DELIVERY' && v.type !== 'RATIO'
            ? (Number(v.value) * Number(v.delivery_ratio)) / v.delivery_ratio
            : v.value,
        basic_unit_price:
          countMoney(
            v.type,
            form.getFieldValue('unit_type') === 'DELIVERY' && v.type !== 'RATIO'
              ? Number(v.value) * Number(v.delivery_ratio)
              : v.value,
            form.getFieldValue('unit_type') === 'DELIVERY'
              ? Number(v.basic_price) * Number(v.delivery_ratio)
              : v.basic_price,
          ) / v.delivery_ratio,
        memo: null,
        basic_price:
          form.getFieldValue('unit_type') === 'DELIVERY'
            ? Number(v.basic_price) * Number(v.delivery_ratio)
            : v.basic_price,
        value:
          form.getFieldValue('unit_type') === 'DELIVERY' && v.type !== 'RATIO'
            ? Number(v.value) * Number(v.delivery_ratio)
            : v.value,
        price: countMoney(
          v.type,
          form.getFieldValue('unit_type') === 'DELIVERY' && v.type !== 'RATIO'
            ? Number(v.value) * Number(v.delivery_ratio)
            : v.value,
          form.getFieldValue('unit_type') === 'DELIVERY'
            ? Number(v.basic_price) * Number(v.delivery_ratio)
            : v.basic_price,
        ),
        _click: false,
        _edit: false,
      };
    });
    newList.length > 0 ? setEdit(true) : setEdit(false);
    const mergeArr = [...rowData, ...newList]?.filter((v) => v?.item_id);
    mergeArr.map((item, index) => {
      if (item.newRow) {
        mergeArr.splice(index, 1);
      }
    });
    setPagin({
      ...pagin,
      pageSize: mergeArr.length,
      total: mergeArr.length,
    });
    setRowData([...mergeArr]);
  };
  const countMoney = (
    type: string,
    value: number,
    basic_price: number,
  ): any => {
    switch (type) {
      case 'RATIO':
        return (Number(basic_price) * (Number(value) * 0.01 + 1)).toFixed(4);
      case 'MONEY':
        return (Number(basic_price) + Number(value)).toFixed(4);
      case 'FIXED_MONEY':
        return Number(value).toFixed(4);
      case 'NULL':
        return null;
      default:
        break;
    }
  };
  // 批量修改
  const batchChange = () => {
    const b_value = form.getFieldValue('money_value');
    const regPos = /^[+-0]?(0|([1-9]\d*))(\.\d+)?$/;
    if (
      form.getFieldValue('money_type') !== 'NULL' &&
      (!regPos.test(b_value) || b_value < 0)
    ) {
      XlbTipsModal({
        tips: '请输入>=0的数字',
      });
      form.setFieldValue('money_value', 0);
      return false;
    }
    const arr = rowData
      .filter((e) => !e.newRow)
      .map((v) => ({
        ...v,
        type: form.getFieldValue('money_type'),
        value: form.getFieldValue('money_type') === 'NULL' ? null : b_value,
        price: countMoney(
          form.getFieldValue('money_type'),
          b_value,
          v.basic_price,
        ),
        _edit: false,
        _click: false,
      }));
    setRowData([...arr]);
    onCancel();
  };
  const onCancel = () => {
    setBatchVisible(false);
    form.setFieldsValue({
      money_value: '0.0000',
      money_type: 'RATIO',
    });

    setBatchType('RATIO');
  };
  const exportItem = async (e: any) => {
    setIsLoading(true);
    const data = { fid: form.getFieldValue('fid') };
    const res = await ErpRequest.post(
      '/erp/hxl.erp.deliverypriceadjust.detail.export',
      { data },
    );

    if (res.code == 0) {
      downByProgress(e);
      message.success('导出受理成功，请前往下载中心查看');
    }
    setIsLoading(false);
  };

  const handleTemplateDate = (values: any) => {
    onValuesChange({ notice_template_id: values?.notice_template_id });
    form.setFieldsValue(values);
    setTemplateVisible(false);
  };
  return (
    <>
      <div
        style={{
          height: 'calc(100vh - 120px)',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Template
          fid={fid}
          disabled={info.state !== 'INIT'}
          visible={templateVisible}
          handleCancel={(values: any) => {
            form.setFieldsValue(values);
            setTemplateVisible(false);
          }}
          handleOk={handleTemplateDate}
          noticeTemplateList={noticeTemplateList}
          infoForm={form}
        />
        <PreView
          key={[visible, form.getFieldValue('unit_type')].join('_')}
          visible={visible}
          type="DELIVERY"
          item_id={info?.fid}
          item_time={info?.create_time}
          order_unit_type={form.getFieldValue('unit_type')}
          dataSource={rowData.filter((i: any) => !!i.fid || !!i.id)}
          onOk={() => setVisible(false)}
          content={editorState?.toHTML()}
          details={details}
        />
        <XlbModal
          title="批量设置"
          visible={batchVisible}
          onOk={batchChange}
          isCancel={true}
          onCancel={onCancel}
          centered
        >
          <div
            style={{ width: 'auto', fontSize: '16px', margin: 10 }}
            className="v-flex"
          >
            <Form colon form={form} autoComplete="off" layout="inline">
              <Form.Item
                name="money_type"
                label={'配送价类型'}
                initialValue={'RATIO'}
                style={{ display: 'inline-block' }}
              >
                <XlbSelect style={{ width: 100 }} onChange={setBatchType}>
                  <XlbSelect.Option key={0} value={'NULL'}>
                    置空
                  </XlbSelect.Option>
                  <XlbSelect.Option key={1} value={'RATIO'}>
                    按比例
                  </XlbSelect.Option>
                  <XlbSelect.Option key={2} value={'MONEY'}>
                    按金额
                  </XlbSelect.Option>
                  <Option key={3} value={'FIXED_MONEY'}>
                    固定金额
                  </Option>
                </XlbSelect>
              </Form.Item>
              <Form.Item
                name="money_value"
                initialValue={'0.0000'}
                style={{ display: 'inline-block' }}
              >
                <XlbInput
                  style={{ width: 80 }}
                  onFocus={(e) => e.target.select()}
                />
              </Form.Item>
              <span style={{ lineHeight: '30px' }}>
                {' '}
                {batchType === 'RATIO' && '%'}
              </span>
            </Form>
          </div>
        </XlbModal>
        <div className={'button_box row-flex'}>
          <div
            style={{
              width: '90%',
              height: '100%',
              paddingLeft: 12,
              paddingTop: 12,
            }}
            className="row-flex v-flex"
          >
            <XlbButton.Group>
              {hasAuth(['配送价调整', '编辑']) && (
                <XlbButton
                  label="保存"
                  disabled={info.state !== 'INIT' || isLoading}
                  onClick={() => operateOrder('保存')}
                  type="primary"
                  icon={<XlbIcon name="baocun" />}
                />
              )}
              {hasAuth(['配送价调整', '审核']) && (
                <XlbButton
                  label="审核"
                  type="primary"
                  disabled={info.state !== 'INIT' || fid === 1 || isLoading}
                  onClick={() => operateOrder('审核')}
                  icon={<XlbIcon name="shenhe" />}
                />
              )}
              {hasAuth(['配送价调整', '反审核']) && (
                <XlbButton
                  label="反审核"
                  type="primary"
                  onClick={() => operateOrder('反审核')}
                  disabled={info.state !== 'AUDIT' || isLoading}
                  icon={<XlbIcon name="fanshenhe" />}
                />
              )}
              {hasAuth(['配送价调整', '处理']) && (
                <XlbDropdownButton
                  label="处理"
                  dropList={[
                    {
                      label: '拒绝',
                      disabled:
                        info.state !== 'AUDIT' ||
                        rowData[0]?.newRow ||
                        !hasAuth(['配送价调整', '处理']) ||
                        isLoading,
                    },
                    {
                      label: '通过',
                      disabled:
                        info.state !== 'AUDIT' ||
                        rowData[0]?.newRow ||
                        !hasAuth(['配送价调整', '处理']) ||
                        isLoading,
                    },
                    {
                      label: '中止',
                      disabled:
                        info.state !== 'HANDLE' ||
                        rowData[0]?.newRow ||
                        !hasAuth(['配送价调整', '处理']) ||
                        isLoading,
                    },
                  ]}
                  dropdownItemClick={async (value: number, item: any) => {
                    let res: any = null;
                    setIsLoading(true);
                    switch (item?.label) {
                      case '拒绝':
                        res = await Api.refuseInfo({
                          fid: form.getFieldValue('fid'),
                        });
                        break;
                      case '通过':
                        res = await Api.passInfo({
                          fid: form.getFieldValue('fid'),
                        });
                        break;
                      case '中止':
                        res = await Api.abortInfo({
                          fid: form.getFieldValue('fid'),
                        });
                        break;
                      default:
                        break;
                    }
                    if (res.code === 0) {
                      message.success('处理成功');
                      readinfo(form.getFieldValue('fid'));
                      setBackFresh(true);
                    }
                  }}
                />
              )}
              {!hasAuth(['配送价调整', '导出']) ||
              !hasAuth(['配送价调整', '打印']) ? null : (
                <XlbDropdownButton
                  label="业务操作"
                  dropList={[
                    {
                      label: '导出',
                      disabled: rowData[0]?.newRow || isLoading,
                      isNoAuth: !hasAuth(['配送价调整', '导出']),
                    },
                    {
                      label: '打印',
                      disabled:
                        rowData[0]?.newRow ||
                        !hasAuth(['配送价调整', '打印']) ||
                        isLoading,
                      isNoAuth: !hasAuth(['配送价调整', '打印']),
                    },
                  ]}
                  dropdownItemClick={(value: number, item: any, e: any) => {
                    switch (item?.label) {
                      case '导出':
                        exportItem(e);
                        break;
                      case '打印':
                        // printItem().then()
                        break;
                    }
                  }}
                />
              )}
              <XlbButton
                label="返回"
                type="primary"
                onClick={goBack}
                icon={<XlbIcon name="fanhui" />}
              />
            </XlbButton.Group>
          </div>
        </div>
        <div ref={formBox}>
          <XlbBasicForm
            colon
            form={form}
            autoComplete="off"
            layout="inline"
            className={styles.contractTab}
            onValuesChange={onValuesChange}
            style={{ paddingLeft: '12px' }}
          >
            <Tabs
              onChange={(e) => setActiveKey(e)}
              activeKey={activeKey}
              defaultActiveKey="baseInfo"
            >
              <TabPane tab="基本信息" key="baseInfo">
                <div className="row-flex">
                  <div style={{ flex: 1 }}>
                    <div className="row-flex" style={{ flexWrap: 'wrap' }}>
                      <XlbBasicForm.Item label="应用门店" name="store_ids">
                        <XlbInputDialog
                          dialogParams={{
                            type: 'store',
                            dataType: 'lists',
                            isMultiple: true,
                            data: {
                              filter_org_levels: [1, 3],
                            },
                            onOkBeforeFunction: (_val: any, data: any[]) => {
                              let flag = true;
                              data?.reduce((pre, cur) => {
                                if (
                                  Object.keys(pre)?.length &&
                                  pre?.org_id !== cur?.org_id
                                ) {
                                  flag = false;
                                }
                                return cur;
                              }, {});
                              if (!flag) {
                                XlbTipsModal({
                                  tips: '请选择同一组织下的门店',
                                  zIndex: 2111,
                                });
                                forceUpdate({});
                              } else {
                                form.setFieldsValue({
                                  org_name: data[0]?.enable_organization
                                    ? data[0].org_name
                                    : data[0]?.parent_org_name,
                                  org_id: [
                                    data[0]?.enable_organization
                                      ? data[0].org_id
                                      : data[0]?.org_parent_id,
                                  ],
                                  store_ids: data.map((v: any) => v.id),
                                  store_names: data
                                    .map((v: any) => v.store_name)
                                    .join(','),
                                });
                                forceUpdate({});
                              }
                              return flag;
                            },
                          }}
                          onChange={async (e: any, options: any) => {
                            let flag = true;
                            options?.reduce((pre, cur) => {
                              if (
                                Object.keys(pre)?.length &&
                                pre?.org_id !== cur?.org_id
                              ) {
                                flag = false;
                              }
                              return cur;
                            }, {});
                            if (!flag) {
                              await XlbTipsModal({
                                tips: '请选择同一组织下的门店',
                                zIndex: 2111,
                                onOkBeforeFunction: () => {
                                  form.setFieldsValue({
                                    org_id: null,
                                    org_name: null,
                                    store_ids: [],
                                  });
                                  forceUpdate({});

                                  return true;
                                },
                              });
                            } else {
                              form.setFieldsValue({
                                org_name: options[0]?.enable_organization
                                  ? options[0].org_name
                                  : options[0]?.parent_org_name,
                                org_id: [
                                  options[0]?.enable_organization
                                    ? options[0].org_id
                                    : options[0]?.org_parent_id,
                                ],
                                store_ids: options.map((v: any) => v.id),
                                store_names: options
                                  .map((v: any) => v.store_name)
                                  .join(','),
                              });
                              forceUpdate({});
                            }
                          }}
                          fieldNames={{
                            idKey: 'id',
                            nameKey: 'store_name',
                          }}
                          width={180}
                        ></XlbInputDialog>
                      </XlbBasicForm.Item>
                      {enable_organization ? (
                        <XlbBasicForm.Item label="应用组织" name="org_name">
                          <XlbInput disabled style={{ width: '180px' }} />
                        </XlbBasicForm.Item>
                      ) : null}
                      <XlbBasicForm.Item
                        label="单位"
                        name="unit_type"
                        initialValue={'DELIVERY'}
                      >
                        <XlbSelect
                          style={{ width: 180 }}
                          onChange={(e) => {
                            itemArrdetail.find((v) => v.code === 'unit')!.name =
                              e === 'DELIVERY' ? '配送单位' : '基本单位';
                            const arr = rowData.map((v) => {
                              return {
                                ...v,
                                basic_price:
                                  e === 'DELIVERY'
                                    ? v.basic_price * v.delivery_ratio
                                    : v.basic_price / v.delivery_ratio,
                                value: v.value,
                                price: countMoney(
                                  v.type,
                                  v.value,
                                  e === 'DELIVERY'
                                    ? v.basic_price * v.delivery_ratio
                                    : v.basic_price / v.delivery_ratio,
                                ),
                              };
                            });
                            setRowData(arr);
                            setdetailItemArr([...itemArrdetail]);
                          }}
                          disabled={
                            !hasAuth(['配送价调整', '编辑']) ||
                            info.state !== 'INIT'
                          }
                        >
                          <XlbSelect.Option key={0} value={'DELIVERY'}>
                            {' '}
                            配送单位
                          </XlbSelect.Option>
                          <XlbSelect.Option key={1} value={'BASIC'}>
                            {' '}
                            基本单位
                          </XlbSelect.Option>
                        </XlbSelect>
                      </XlbBasicForm.Item>
                      <XlbBasicForm.Item label="制单门店" name="store_name">
                        <XlbInput
                          size="small"
                          style={{ width: '180px' }}
                          disabled
                          suffix={
                            <SearchOutlined style={{ color: '#F5F5F5' }} />
                          }
                        />
                      </XlbBasicForm.Item>
                      <XlbBasicForm.Item label="单据号" name="fid">
                        <XlbInput
                          size="small"
                          style={{ width: '180px' }}
                          disabled
                          // suffix={}
                        />
                      </XlbBasicForm.Item>
                      {/* <Form.Item label="单据状态">
                      <Input
                        disabled
                        size="small"
                        suffix={<SearchOutlined style={{ color: '#F5F5F5' }} />}
                        value={Options1.find((s) => s.value === info.state)?.label}
                        style={{ width: '180px' }}
                      />
                    </Form.Item> */}
                      <XlbBasicForm.Item
                        label="商品部门"
                        name="item_dept_names"
                      >
                        <XlbInput
                          disabled
                          size="small"
                          suffix={
                            <SearchOutlined style={{ color: '#F5F5F5' }} />
                          }
                          style={{ width: '180px' }}
                        />
                      </XlbBasicForm.Item>
                      <XlbBasicForm.Item label="应用日期" name="supply_date">
                        <XlbDatePicker
                          showTime={true}
                          format={'YYYY-MM-DD HH:mm'}
                          style={{ width: 180 }}
                          disabled={
                            info.state !== 'INIT' ||
                            !hasAuth(['配送价调整', '编辑'])
                          }
                        />
                      </XlbBasicForm.Item>
                      <XlbBasicForm.Item label="失效日期" name="invalid_time">
                        <XlbDatePicker
                          style={{ width: 180 }}
                          showTime={true}
                          format={'YYYY-MM-DD HH:mm'}
                          // resultFormat={'YYYY-MM-DD HH:mm:59'}
                          disabled={
                            info.state !== 'INIT' ||
                            !hasAuth(['配送价调整', '编辑'])
                          }
                        />
                      </XlbBasicForm.Item>
                      <XlbBasicForm.Item
                        label="失效后价格"
                        name="invalid_type"
                        initialValue={0}
                      >
                        <XlbSelect
                          style={{ width: 180 }}
                          disabled={
                            info.state !== 'INIT' ||
                            !hasAuth(['配送价调整', '编辑'])
                          }
                        >
                          <Option key={0} value={0}>
                            按调整前价格
                          </Option>
                          <Option key={1} value={1}>
                            按档案配送价
                          </Option>
                        </XlbSelect>
                      </XlbBasicForm.Item>
                      <XlbBasicForm.Item label="留言备注" name="memo">
                        <XlbInput
                          style={{ width: 780 }}
                          disabled={
                            !hasAuth(['配送价调整', '编辑']) ||
                            info.state !== 'INIT'
                          }
                        />
                      </XlbBasicForm.Item>
                      {hasAuth(['调价公告模板', '编辑']) ? (
                        <>
                          <XlbBasicForm.Item
                            label=" "
                            colon={false}
                            name="notice_flag"
                            valuePropName="checked"
                          >
                            <XlbCheckbox
                              style={{ width: 180 }}
                              disabled={
                                info.state !== 'INIT' ||
                                !hasAuth(['配送价调整', '编辑'])
                              }
                            >
                              发送公告
                            </XlbCheckbox>
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="公告模版"
                            name="notice_template_name"
                          >
                            <XlbInput
                              style={{ width: 180 }}
                              readOnly
                              disabled={
                                info.state !== 'INIT' ||
                                !hasAuth(['配送价调整', '编辑'])
                              }
                            />
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            noStyle
                            dependencies={['notice_flag']}
                          >
                            {({ getFieldValue }) => (
                              <XlbButton
                                style={{ marginLeft: 10 }}
                                label="添加"
                                disabled={!getFieldValue('notice_flag')}
                                icon={<XlbIcon name="jia" />}
                                type="primary"
                                onClick={() => setTemplateVisible(true)}
                              />
                            )}
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            noStyle
                            dependencies={['notice_template_id']}
                          >
                            {({ getFieldValue }) => (
                              <XlbButton
                                style={{ marginLeft: 10 }}
                                label="预览"
                                type="primary"
                                disabled={!getFieldValue('notice_template_id')}
                                icon={<XlbIcon name="xianshimima" />}
                                onClick={() => setVisible(true)}
                              />
                            )}
                          </XlbBasicForm.Item>
                          <XlbBasicForm.Item
                            label="公告推送时间"
                            name="send_time"
                          >
                            <XlbDatePicker
                              disabled={info.state === 'HANDLE'}
                              style={{ width: '180px' }}
                              format={'YYYY-MM-DD HH:mm:ss'}
                              showTime={true}
                            />
                          </XlbBasicForm.Item>
                        </>
                      ) : null}
                    </div>
                  </div>
                  {info?.state && (
                    <div
                      style={{
                        width: '150px',
                        flexBasis: '150px',
                        display: 'flex',
                        justifyContent: 'center',
                      }}
                    >
                      <img
                        src={orderStatusIcons[info?.state]}
                        width={86}
                        height={78}
                      />
                    </div>
                  )}
                </div>
              </TabPane>
              <TabPane tab="其他信息" key="otherInfo">
                <div className="row-flex" style={{ marginTop: '2px' }}>
                  <XlbBasicForm.Item label="制单人" name="create_by">
                    <XlbInput style={{ width: '180px' }} disabled />
                  </XlbBasicForm.Item>
                  <XlbBasicForm.Item label="制单时间" name="create_time">
                    <XlbInput style={{ width: '180px' }} disabled />
                  </XlbBasicForm.Item>
                  <XlbBasicForm.Item label="审核人" name="audit_by">
                    <XlbInput style={{ width: '180px' }} disabled />
                  </XlbBasicForm.Item>
                  <XlbBasicForm.Item label="审核时间" name="audit_time">
                    <XlbInput style={{ width: '180px' }} disabled />
                  </XlbBasicForm.Item>
                </div>
                <div className="row-flex">
                  <XlbBasicForm.Item label={'修改人'} name={'update_by'}>
                    <XlbInput style={{ width: '180px' }} disabled />
                  </XlbBasicForm.Item>
                  <XlbBasicForm.Item label={'修改时间'} name={'update_time'}>
                    <XlbInput style={{ width: '180px' }} disabled />
                  </XlbBasicForm.Item>
                </div>
              </TabPane>
            </Tabs>
          </XlbBasicForm>
        </div>
        <div
          style={{
            marginBottom: 12,
            paddingLeft: '16px',
            paddingRight: '16px',
          }}
        >
          <XlbButton.Group>
            {hasAuth(['配送价调整', '编辑']) && (
              <XlbButton
                label="批量添加"
                type="primary"
                disabled={
                  info.state !== 'INIT' ||
                  form.getFieldValue('store_ids')?.length == 0 ||
                  !form.getFieldValue('store_ids')
                }
                onClick={async () => {
                  const list = await XlbBasicData({
                    type: 'goods',
                    url: '/erp/hxl.erp.deliverypriceadjust.item.page',
                    isMultiple: true,
                    dataType: 'lists',
                    primaryKey: 'id',
                    resetForm: true,
                    nullable: false,
                    data: {
                      store_id: form.getFieldValue('store_id'),
                      two_level_org_id: form.getFieldValue('org_id')?.[0],
                      storeStatus: true,
                      store_ids: form.getFieldValue('store_ids'),
                    },
                  });
                  if (!list) return;
                  if (Array.isArray(list)) {
                    confirmAdd(list, 'item');
                  }
                }}
                icon={<XlbIcon name="jia" />}
              />
            )}
            {hasAuth(['配送价调整', '导入']) && (
              <XlbButton
                label="导入"
                type="primary"
                disabled={
                  info.state !== 'INIT' ||
                  form.getFieldValue('store_ids')?.length == 0 ||
                  !form.getFieldValue('store_ids')
                }
                onClick={async () => {
                  const res = await XlbImportModal({
                    templateUrl:
                      process.env.BASE_URL +
                      '/erp/hxl.erp.deliverypriceadjust.template.download',
                    importUrl:
                      process.env.BASE_URL +
                      `/erp/hxl.erp.deliverypriceadjust.import?supply_store_ids=${[
                        form.getFieldValue('store_ids'),
                      ]}&unit_type=${form.getFieldValue('unit_type')}&two_level_org_id=${
                        form.getFieldValue('org_id')?.[0]
                      }`,
                    title: '配送价调整',
                  });
                  if (res.code !== 0) return;
                  if (res?.data?.details && res?.data?.details?.length) {
                    confirmAdd(res?.data?.details, 'import');
                  }
                }}
                icon={<XlbIcon name="daoru" />}
              />
            )}
            {hasAuth(['配送价调整', '编辑']) && (
              <XlbButton
                label="批量设置"
                type="primary"
                disabled={info.state !== 'INIT' || !rowData?.some((v) => v?.item_id)}
                onClick={() => setBatchVisible(true)}
                icon={<XlbIcon size={16} name="piliang" />}
              />
            )}
            {hasAuth(['配送价调整', '编辑']) && (
              <XlbButton
                label="查看配送价"
                type="primary"
                disabled={!chooseList}
                onClick={() => setgoodsVisible(true)}
                icon={<XlbIcon name="sousuo" />}
              />
            )}
          </XlbButton.Group>
        </div>
        <XlbShortTable
          onSelectRow={(_, e: any) => {
            setChooseList(e?.[0]?.item_id);
          }}
          disabledAdd={
            info.state !== 'INIT' ||
            form.getFieldValue('store_ids')?.length == 0 ||
            !form.getFieldValue('store_ids')
          }
          isLoading={isLoading}
          showSearch
          key={activeKey}
          style={{ flex: 1, margin: '0 16px' }}
          url={'/erp/hxl.erp.deliverypriceadjust.item.page'}
          data={{
            store_id: info?.store_id || userInfo?.store_id,
            two_level_org_id: form.getFieldValue('org_id')?.[0],
            store_ids: form.getFieldValue('store_ids'),
          }}
          onChangeData={(data, type) => {
            setRowData((prev) => {
              const formUnitType = form.getFieldValue('unit_type');
              const prevMap = new Map(prev.map((v) => [v.item_id, v]));

              const getStandardItem = (v, index = 0) => {
                const item_id = v?.id || v?.item_id;
                const oldItem = prevMap.get(item_id);
                return {
                  ...oldItem,
                  ...v,
                  key: `${index}_${item_id ?? 'new'}`,
                  item_id,
                  item_code: v?.code || v?.item_code,
                  item_bar_code: v?.bar_code || v?.item_bar_code,
                  item_name: v?.name || v?.item_name,
                  item_spec: v?.purchase_spec || v?.item_spec,
                  basic_price:
                    formUnitType === 'DELIVERY'
                      ? Number(v.basic_price) * Number(v.delivery_ratio)
                      : v.basic_price,
                  value: v.old_value ?? v.value ?? oldItem?.value ?? 0,
                  basic_unit_value:
                    form.getFieldValue('unit_type') === 'DELIVERY' &&
                    v.type !== 'RATIO'
                      ? (Number(v.old_value || v.value) *
                          Number(v.delivery_ratio)) /
                        v.delivery_ratio
                      : v.old_value || v.value,
                  basic_unit_price:
                    countMoney(
                      v.type,
                      formUnitType === 'DELIVERY' && v.type !== 'RATIO'
                        ? Number(v.old_value || v.value) *
                            Number(v.delivery_ratio)
                        : v.old_value || v.value,
                      formUnitType === 'DELIVERY'
                        ? Number(v.basic_price) * Number(v.delivery_ratio)
                        : v.basic_price,
                    ) / v.delivery_ratio,
                  price: countMoney(
                    v.type,
                    formUnitType === 'DELIVERY' && v.type !== 'RATIO'
                      ? Number(v.old_value || v.value) *
                          Number(v.delivery_ratio)
                      : v.old_value || v.value,
                    formUnitType === 'DELIVERY'
                      ? Number(v.basic_price) * Number(v.delivery_ratio)
                      : v.basic_price,
                  ),
                  type: v.old_type || v.type,
                  unit:
                    formUnitType === 'DELIVERY'
                      ? v.delivery_unit
                      : v.basic_unit,
                  memo: oldItem?.memo ?? null,
                };
              };
              if (type === 'onDelete') {
                const cleanData = data.filter((v) => !!(v?.item_id || v?.id));
                return cleanData.map((v) => prevMap.get(v.item_id) || v);
              }
              if (type === 'onChangeSorts') {
                return data.map((v) => {
                  const item_id = v.item_id || v.id;
                  return prevMap.get(item_id) || getStandardItem(v);
                });
              }
              const prevIds = new Set(prev.map((v) => v.item_id));
              const newItems = data.filter((v) => {
                const hasValidId =
                  v?.item_id || v?.id || Object.keys(v).length > 0;
                return hasValidId && !prevIds.has(v.item_id || v.id);
              });

              if (newItems.length === 0) return prev;

              const nextIndex = prev.length;
              const [newItemRaw] = newItems;

              const newItem = {
                ...getStandardItem(newItemRaw, nextIndex),
                memo: null,
              };

              let mergeArr = [...prev, newItem];
              if (type === 'onAddSelectData') {
                mergeArr = mergeArr.filter((v) => v.item_id || v.id);
              }

              return mergeArr;
            });
          }}
          disabled={
            !hasAuth(['配送价调整', '编辑']) ||
            !form.getFieldValue('store_ids') ||
            info.state !== 'INIT'
          }
          columns={itemArrdetail?.map((v) => tableRender(v))}
          dataSource={rowData}
          total={rowData?.length}
          pageSize={pageSize}
          onPaginChange={(page, pageSize) => {
            setPageSize(pageSize);
          }}
          selectMode="single"
          primaryKey="item_id"
          // popoverPrimaryKey="id"
        />
        <XlbBaseGoods
          visible={goodsVisible}
          params={{
            item_id: chooseList,
            store_ids: form.getFieldValue('store_ids'),
          }}
          handCancel={() => setgoodsVisible(false)}
        />
      </div>
    </>
  );
};

export default DeliveryPriceMangeItem;
