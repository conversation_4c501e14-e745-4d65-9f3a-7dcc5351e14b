// 示例方法，没有实际意义
export function trim(str: string) {
  return str.trim();
}

// 转换查询的state
export const transferState = (values: any, handleKey: string) => {
  const state = values[handleKey];
  if (state?.length > 1 || !state?.length) {
    values[handleKey] = null;
  } else if (state?.length === 1) {
    values[handleKey] = values[handleKey][0];
  }

  return values;
};
/**
 * 判断时间跨度是否超过限制
 * @param dates - 长度为2的日期字符串数组，格式为 'YYYY-MM-DD'
 * @param options - 配置项
 * @param options.mode - 判断模式，'month' 为自然月，'day' 为按天数，默认为 'month'
 * @param options.limit - 限制跨度数，比如 3 表示 3个月或90天，默认为 3
 * @returns 是否超过指定时间跨度
 */
export const isDateRangeExceeded = (
  dates: [string, string],
  options: {
    mode?: 'month' | 'day';
    limit?: number;
  } = {},
): boolean => {
  const { mode = 'month', limit = 3 } = options;

  const [startStr, endStr] = dates;
  const startDate = new Date(startStr);
  const endDate = new Date(endStr);

  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    throw new Error('无效的日期格式');
  }

  if (mode === 'month') {
    const monthDiff =
      (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      (endDate.getMonth() - startDate.getMonth());

    return (
      monthDiff > limit ||
      (monthDiff === limit && endDate.getDate() > startDate.getDate())
    );
  }

  if (mode === 'day') {
    const diffTime = endDate.getTime() - startDate.getTime();
    const diffDays = diffTime / (1000 * 60 * 60 * 24);
    return diffDays > limit;
  }

  throw new Error("mode 参数只支持 'month' 或 'day'");
};
