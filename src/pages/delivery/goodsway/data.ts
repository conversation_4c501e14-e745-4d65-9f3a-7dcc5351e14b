import { columnWidthEnum } from '@/constants/index';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const formList: SearchFormType[] = [
  {
    width: 392,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
    // disabled: true,
    // format: "YYYY-MM-DD HH:mm:ss",
    // @ts-ignore
  },
  {
    label: '调出组织',
    name: 'out_org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    check: true,
    options: [],
    selectRequestParams: (params: Record<string, any>) => {
      return {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '调出门店',
    name: 'out_store_ids',
    type: 'inputDialog',
    dependencies: ['out_org_ids'],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        url: '/erp-mdm/hxl.erp.store.all.shortfind',
        isMultiple: true,
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '调入组织',
    name: 'in_org_ids',
    type: 'select',
    multiple: true,
    clear: true,
    check: true,
    options: [],
    selectRequestParams: (params: Record<string, any>) => {
      return {
        url: '/erp-mdm/hxl.erp.org.find',
        responseTrans(data) {
          const options = data.map((item: any) => {
            const obj = {
              label: item.name,
              value: item.id,
            };
            return obj;
          });
          return options;
        },
      };
    },
  },
  {
    label: '调入门店',
    name: 'in_store_ids',
    type: 'inputDialog',
    dependencies: ['in_org_ids'],
    dialogParams: (params: any) => {
      return {
        type: 'store',
        dataType: 'lists',
        isLeftColumn: true,
        isMultiple: true,
        url: '/erp-mdm/hxl.erp.store.all.shortfind',
        primaryKey: 'id',
        data: {
          enabled: true,
        },
      };
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'store_name',
    } as any,
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
      resetForm: true,
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
  },
  {
    label: '调出单号',
    name: 'delivery_out_fid',
    type: 'input',
    clear: true,
    check: true,
    tooltip: '单据号不受其他查询条件限制',
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {},
    },
  },
  {
    label: '过滤',
    name: 'checkValue',
    colon: true,
    type: 'checkbox',
    options: [
      {
        label: '仅查询在途数量≠0的数据',
        value: 'query_item_on_way_not_equal_zero',
      },
    ],
  },
];

export const checkOptions = [
  {
    label: '仅查询在途数量>0的数据',
    value: 'normal',
  },
];

export const goodsTotalArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '商品规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '配送单位',
    code: 'delivery_unit',
    width: 110,
    features: { sortable: true },
  },

  {
    name: '调出数量',
    code: 'out_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调出基本数量',
    code: 'out_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调出金额',
    code: 'out_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '调入数量',
    code: 'in_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入基本数量',
    code: 'in_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入金额',
    code: 'in_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在途数量',
    code: 'on_way_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途基本数量',
    code: 'on_way_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途金额',
    code: 'on_way_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '',
  },
];

export const storeTotalArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出数量',
    code: 'out_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调出基本数量',
    code: 'out_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调出金额',
    code: 'out_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '调入数量',
    code: 'in_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入基本数量',
    code: 'in_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入金额',
    code: 'in_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在途数量',
    code: 'on_way_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途基本数量',
    code: 'on_way_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途金额',
    code: 'on_way_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '',
  },
];

export const storegoodsTotalArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '商品规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '配送单位',
    code: 'delivery_unit',
    width: 110,
    features: { sortable: true },
  },
  {
    name: '调出数量',
    code: 'out_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调出基本数量',
    code: 'out_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调出金额',
    code: 'out_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '调入数量',
    code: 'in_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入基本数量',
    code: 'in_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入金额',
    code: 'in_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在途数量',
    code: 'on_way_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途基本数量',
    code: 'on_way_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途金额',
    code: 'on_way_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '',
  },
];

export const djTotalArr: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '调出组织',
    code: 'out_org_name',
    hiddenInXlbColumns: true,
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出单号',
    code: 'delivery_out_fid',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '调出门店',
    code: 'out_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调入门店',
    code: 'in_store_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '调出数量',
    code: 'out_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true , format: 'QUANTITY'},
  },
  {
    name: '调出基本数量',
    code: 'out_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调出金额',
    code: 'out_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '调入数量',
    code: 'in_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入基本数量',
    code: 'in_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '调入金额',
    code: 'in_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '在途数量',
    code: 'on_way_quantity',
    width: 110,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途基本数量',
    code: 'on_way_basic_quantity',
    width: 140,
    align: 'right',
    features: { sortable: true, format: 'QUANTITY' },
  },
  {
    name: '在途金额',
    code: 'on_way_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单据备注',
    code: 'memo',
    width: columnWidthEnum.MEMO,
    features: { sortable: true },
  },
  {
    name: '',
  },
];
