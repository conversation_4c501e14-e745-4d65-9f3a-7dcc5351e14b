import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import { hasAuth } from '@/utils/kit';
import { wujieBus } from '@/wujie/utils';
import NiceModal from '@ebay/nice-modal-react';
import {
  XlbButton,
  XlbIcon,
  XlbInput,
  XlbModal,
  XlbProPageContainer,
  XlbRadio,
  XlbSelect,
} from '@xlb/components';
import { XlbFetch } from '@xlb/utils';
import { Form, message } from 'antd';
import type { FC } from 'react';
import { useEffect, useRef, useState } from 'react';
import { tableColumn } from '../data';
import { addstockadjustmentreason, editstockadjustmentreason } from '../server';
import { BatchChangeModal } from './component/batchChange';
import styles from './index.less';
const InventoryReason: FC = () => {
  let refresh = () => {};
  const dataSourceRef = useRef<any>(null);
  const fetchDataRef = useRef<any>(null);
  const requestFormRef = useRef<any>(null);
  const [form] = Form.useForm();

  enum titleType {
    'add' = '新增库存调整原因',
    'update' = '库存调整原因',
  }
  const exportItem = async (e, requestForm: any) => {
    console.log('🚀 ~ exportItem ~ e:', e);
    const data = await XlbFetch.post(
      process.env.BASE_URL + '/erp/hxl.erp.stockadjustmentreason.export',
      { ...requestForm },
    );
    if (data.code === 0) {
      wujieBus?.$emit('xlb_erp-event', { code: 'downloadEnd', target: e });
      message.success(data?.data);
    }
  };
  // <Option value={0}>不限</Option>
  const applicabilityMap = new Map();
  applicabilityMap.set(0, '不限');
  applicabilityMap.set(1, '配送中心');
  applicabilityMap.set(2, '非配送中心');
  const [preId, setId] = useState(-1); // -1 新增
  const [visible, setVisible] = useState<boolean>(false);
  const [title, setTitle] = useState<titleType>(titleType.add);
  const render = (item: any) => {
    switch (item.code) {
      case 'name':
        item.render = (value: string, record: any, index) => {
          return (
            <div className="">
              <span
                className="link cursors"
                onClick={(e) => {
                  setTitle(titleType.update);
                  setVisible(true);
                  setId(record.id);
                  form.setFieldValue('flag', 1);
                  if (record.id > 0) {
                    form.setFieldsValue({
                      id: record.id,
                      name: record.name,
                      flag: record.flag ? 1 : 0,
                      applicability: record.applicability || 0,
                      code: record.code,
                      enabled: record.enabled,
                      keyword: '',
                    });
                  }
                  e.stopPropagation();
                  // go('/xlb_erp/inventoryReason/item', {...record, refresh})
                }}
              >
                {value}
              </span>
            </div>
          );
        };
        break;
      case 'flag':
        item.render = (value: any, record: any, index: number) => {
          return <div className="">{value ? '出库' : '入库'}</div>;
        };
        break;
      case 'applicability':
        item.render = (value: any, record: any, index: number) => {
          return <div className="">{applicabilityMap.get(value)}</div>;
        };
        break;
      case 'enabled':
        item.render = (value: any, record: any, index: number) => {
          const showColor = record?.enabled ? 'success' : 'danger';
          return (
            <div className={`${showColor}`}>
              {record?.enabled ? '启用' : '禁用'}
            </div>
          );
        };
        break;
    }
  };
  tableColumn.map((v) => render(v));
  useEffect(() => {
    setTimeout(refresh, 100);
  }, []);
  const handleBatchModal = () => {
    return NiceModal.show(NiceModal.create(BatchChangeModal), {
      requestForm: requestFormRef?.current,
      fetchData: fetchDataRef?.current,
      nameList: dataSourceRef?.current,
    });
  };
  const resetData = () => {
    form.setFieldValue('name', '');
    form.setFieldValue('flag', 1);
    form.setFieldValue('applicability', 0);
  };
  const saveOrder = async () => {
    const stockadjustmentreason_name = form.getFieldValue('name')?.trim();
    if (!stockadjustmentreason_name || stockadjustmentreason_name == '') {
      message.error('请输入库存调整原因名称');
      return;
    }
    if (stockadjustmentreason_name.length > 20) {
      message.error('库存调整原因名称长度≤20');
      return;
    }
    if (preId == -1) {
      // 新增
      const res = await addstockadjustmentreason({
        name: stockadjustmentreason_name.replace(/\s*/g, ''),
        flag: form.getFieldValue('flag') == 1,
        applicability: form.getFieldValue('applicability'),
        enabled: form.getFieldValue('enabled'),
        code: form.getFieldValue('code'),
      });
      // setisLoading(false)
      if (res.code == 0) {
        message.success('操作成功');
        setId(res.data?.id);
        resetData();
        setVisible(false);
        refresh();
        // history.push('/xlb_erp/inventoryReason/index')
      }
    } else {
      const res = await editstockadjustmentreason({
        id: preId,
        name: stockadjustmentreason_name.replace(/\s*/g, ''),
        flag: form.getFieldValue('flag') == 1 ? true : false,
        applicability: form.getFieldValue('applicability'),
        enabled: form.getFieldValue('enabled'),
        code: form.getFieldValue('code'),
      });
      if (res.code == '0') {
        form.resetFields();
        resetData();
        message.success('操作成功');
        refresh();
        setVisible(false);
      }
    }
  };
  // TODO总计
  return (
    <>
      <XlbModal
        isCancel={false}
        footerExtroContent={() => {
          return (
            <XlbButton
              onClick={() => {
                resetData();
                setVisible(false);
              }}
              type="line"
            >
              关闭
            </XlbButton>
          );
        }}
        title={title}
        open={visible}
        centered
        onOk={() => saveOrder()}
        onCancel={() => {
          resetData();
          setVisible(false);
        }}
      >
        <div style={{ padding: 15 }}>
          <div className={'baseInfo'}>
            <Form
              autoComplete="off"
              layout="horizontal"
              form={form}
              className={styles.form_container}
            >
              <Form.Item label="调整原因代码" name="code">
                <XlbInput
                  disabled={preId > 0}
                  style={{ width: 180 }}
                  size="small"
                  allowClear
                />
              </Form.Item>
              <Form.Item label="库存调整原因名称：" name="name">
                <XlbInput
                  disabled={preId > 0}
                  style={{ width: 180 }}
                  size="small"
                  allowClear
                />
              </Form.Item>
              <Form.Item
                label="出入库标记："
                name="flag"
                initialValue={form.getFieldValue('flag') || 1}
              >
                <XlbRadio.Group>
                  <XlbRadio value={1} style={{ width: 100 }}>
                    出库
                  </XlbRadio>
                  <XlbRadio value={0}>入库</XlbRadio>
                </XlbRadio.Group>
              </Form.Item>
              {/* {form.getFieldValue('applicability')} */}
              <Form.Item
                label="应用范围："
                name="applicability"
                initialValue={form.getFieldValue('applicability') || 0}
              >
                <XlbSelect style={{ width: 180 }}>
                  <XlbSelect.Option value={0}>不限</XlbSelect.Option>
                  <XlbSelect.Option value={1}>配送中心</XlbSelect.Option>
                  <XlbSelect.Option value={2}>非配送中心</XlbSelect.Option>
                </XlbSelect>
              </Form.Item>
              <Form.Item label="状态：" name="enabled">
                <XlbRadio.Group>
                  <XlbRadio value={true} style={{ width: 100 }}>
                    启用
                  </XlbRadio>
                  <XlbRadio value={false}>禁用</XlbRadio>
                </XlbRadio.Group>
              </Form.Item>
            </Form>
          </div>
        </div>
      </XlbModal>
      <XlbProPageContainer
        searchFieldProps={{
          formList: () => [
            { id: 'keyword', label: '调整原因' },
            {
              id: ErpFieldKeyMap.erpBillState,
              name: 'flag',
              label: '出入库标记',
              fieldProps: {
                options: [
                  {
                    label: '出库',
                    value: 1,
                  },
                  {
                    label: '入库',
                    value: 0,
                  },
                ],
              },
            },
            {
              id: ErpFieldKeyMap.erpBillState,
              label: '状态',
              name: 'enabled',
              fieldProps: {
                options: [
                  {
                    label: '启用',
                    value: true,
                  },
                  {
                    label: '禁用',
                    value: false,
                  },
                ],
              },
            },
            {
              id: ErpFieldKeyMap.erpBillState,
              label: '应用范围',
              name: 'applicabilities',
              fieldProps: {
                mode: 'multiple',
                options: [
                  {
                    label: '不限',
                    value: 0,
                  },
                  {
                    label: '配送中心',
                    value: 1,
                  },
                  {
                    label: '非配送中心',
                    value: 2,
                  },
                ],
              },
            },
            {
              id: ErpFieldKeyMap.erpBillFid,
              label: '创建人',
              name: 'create_by',
            },
            {
              id: 'dateCommonGoodsFiles',
              label: '创建时间',
              name: 'time',
              fieldProps: {
                allowClear: true,
              },
            },
            {
              id: ErpFieldKeyMap.erpKingdeeCode1,
              name: 'start_time',
              hidden: true,
            },
            {
              id: ErpFieldKeyMap.erpKingdeeCode2,
              name: 'end_time',
              hidden: true,
            },
          ],
        }}
        extra={(context) => {
          const {
            selectRowKeys,
            loading,
            requestForm,
            setLoading,
            fetchData,
            dataSource,
          } = context;
          refresh = fetchData;
          dataSourceRef.current = dataSource;
          requestFormRef.current = requestForm;
          fetchDataRef.current = fetchData;
          return (
            <XlbButton.Group>
              {hasAuth(['库存调整原因', '编辑']) && (
                <XlbButton
                  type="primary"
                  onClick={() => {
                    setTitle(titleType.add);
                    setId(-1);
                    form.resetFields();
                    setVisible(true);
                  }}
                  disabled={loading}
                  icon={<XlbIcon size={16} name="jia" />}
                >
                  新增
                </XlbButton>
              )}
              {hasAuth(['库存调整原因', '导出']) && (
                <XlbButton
                  type="primary"
                  disabled={!dataSource?.length || loading}
                  onClick={(e) => exportItem(e, requestForm)}
                  icon={<XlbIcon size={16} name="daochu" />}
                >
                  导出
                </XlbButton>
              )}
              {hasAuth(['库存调整原因', '编辑']) && (
                <XlbButton
                  type="primary"
                  disabled={!dataSource?.length || loading}
                  onClick={handleBatchModal}
                  icon={<XlbIcon size={16} name="shezhi" />}
                >
                  批量修改
                </XlbButton>
              )}
            </XlbButton.Group>
          );
        }}
        tableFieldProps={{
          url: '/erp/hxl.erp.stockadjustmentreason.find',
          primaryKey: 'id',
          tableColumn: tableColumn,
          selectMode: 'single',
          showColumnsSetting: true,
          immediatePost: true,
          keepDataSource: false,
        }}
      />
    </>
  );
};
export default InventoryReason;
