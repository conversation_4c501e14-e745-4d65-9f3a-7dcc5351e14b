.inputNumber {
  width: 100%;
  height: 26px;
  padding: 0 7px;
  border: 1px solid #d9d9d9;
  border-radius: 5px;
}
.inputNumber::-webkit-outer-spin-button,
.inputNumber::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
.inputNumber[type='number'] {
  -moz-appearance: textfield;
}
.inputNumber:hover {
  border-color: #3d66fe !important;
  border-right-width: 1px;
}
.inputNumber:focus {
  border-color: #3d66fe !important;
  border-right-width: 1px;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.storeGroupContainer{
  :global {
    .xlb-pageContainer-toolBtn{
      padding-top: 12px;
    }
  }
}