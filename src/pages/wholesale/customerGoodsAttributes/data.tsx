import { columnWidthEnum } from '@/data/common/constant';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const types1 = [
  {
    label: 'true',
    value: true,
  },
  {
    label: 'false',
    value: false,
  },
];
export const types = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
  {
    label: '　',
    value: null,
  },
];
//商品类型
export const goodsType = [
  {
    label: '主规格商品',
    value: 'MAINSPEC',
  },
  {
    label: '多规格商品',
    value: 'MULTIPLESPEC',
  },
  {
    label: '标准商品',
    value: 'STANDARD',
  },
  {
    label: '组合商品',
    value: 'COMBINATION',
  },
  {
    label: '成分商品',
    value: 'COMPONENT',
  },
  {
    label: '制单组合',
    value: 'MAKEBILL',
  },
  {
    label: '分级商品',
    value: 'GRADING',
  },
];

// 批量修改
export const batch = [
  {
    label: '停止批发',
    value: 'stop_wholesale',
  },
];

export const searchFormList: SearchFormType[] = [
  {
    label: '批发客户',
    name: 'client_ids',
    type: 'inputDialog',
    check: true,
    dialogParams: {
      type: 'wholesaler',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    rules: [{ required: true, message: '请选择批发客户' }],
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
    width: 200,
  },
  {
    label: '停止批发',
    name: 'stop_wholesale',
    type: 'select',
    clear: true,
    check: true,
    options: [
      { label: '是', value: '2' },
      { label: '否', value: '1' },
      { label: '空', value: '0' },
    ],
  },
];

export const tableList: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
    lock: true,
  },
  {
    name: '批发客户',
    code: 'client_name',
    width: columnWidthEnum.STORE_NAME,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: 260,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品类型',
    code: 'item_type',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '商品类别',
    code: 'item_category_name',
    width: 140,
    features: { sortable: true },
  },
  {
    name: '停止批发',
    code: 'stop_wholesale',
    width: 140,
    features: { sortable: true },
  },
];
