import { request } from 'umi'

export const GoodsFilesKeyMap = {
  goodsFilesItem: 'goodsFilesItem',
  goodsFilesBrand:'goodsFilesBrand',
  goodsFilesFinance:'goodsFilesFinance'
}

export const goodsFilesConfig: any[] = [
  {
    tag: 'ERP',
    label: '商品部门',
    id: GoodsFilesKeyMap?.goodsFilesItem,
    name: 'item_dept_id',
    componentType: 'select',
    request: async (formValues, anybaseURL, globalFetch) => {
      console.log("🚀 ~ request: ~ formValues:", formValues)
      const res = await globalFetch.post(anybaseURL + '/erp-mdm/hxl.erp.dept.find', {})
      if (res.code == 0 && Array.isArray(res?.data)) {
        return res.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
      return []
    },
    fieldProps: {
      mode: 'single',
      allowClear: true
    }
  },
  {
    tag: 'ERP',
    label: '商品品牌',
    id: GoodsFilesKeyMap?.goodsFilesBrand,
    name: 'item_dept_id',
    componentType: 'select',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(anybaseURL + '/erp-mdm/hxl.erp.brand.find', {})
      if (res.code == 0 && Array.isArray(res?.data)) {
        return res.data?.map((item: any) => {
          return {
            label: item.name,
            value: item.id
          }
        })
      }
      return []
    },
    fieldProps: {
      mode: 'single',
      allowClear: true
    }
  },
  {
    tag: 'ERP',
    label: '业财分类',
    id: GoodsFilesKeyMap?.goodsFilesFinance,
    name: 'finance_codes',
    componentType: 'select',
    request: async (formValues, anybaseURL, globalFetch) => {
      const res = await globalFetch.post(anybaseURL + '/erp-mdm/hxl.erp.settlementcategory.center.find', {})
      if (res.code == 0 && Array.isArray(res?.data)) {
        return res.data?.map((item: any) => {
          return {
            label: `${item.code}/${item.category_name}`,
            value: item.code
          }
        })
      }
      return []
    },
    fieldProps: {
      mode: 'multiple',
      allowClear: true
    }
  }
]