import type { FormInstance } from '@ant-design/pro-components';
import type { SearchFormType, XlbTableColumnProps } from '@xlb/components';
import { getHouseList } from './server';
//日期类型
export const dateTypeOptions = [
  {
    label: '制单日期',
    value: 'create_date',
  },
  {
    label: '审核日期',
    value: 'audit_date',
  },
  {
    label: '销售日期',
    value: 'operate_date',
  },
  {
    label: '付款日期',
    value: 'payment_date',
  },
];
//单据状态
export const stateType = [
  {
    label: '制单',
    value: 'INIT',
    type: 'deful',
  },
  {
    label: '审核',
    value: 'AUDIT',
    type: 'warning',
  },
  // {
  //   label: '处理通过',
  //   value: 'HANDLE',
  //   type: 'success'
  // },
  // {
  //   label: '处理拒绝',
  //   value: 'HANDLE_REFUSE',
  //   type: 'danger'
  // },
];
//查询门店下仓库
const getStockData = async (id: any) => {
  console.log('getStockData', id);
  if (id?.length > 1) {
    return [];
  } else {
    let tempId = id;
    if (Array.isArray(id)) {
      tempId = id && id[0];
    }
    let houseArrs: any[] = [];
    const res = await getHouseList({ store_id: tempId });
    if (res.code == 0) {
      const defaultHouseList = res?.data; //?.filter((item: any) => item.default_flag === true)
      houseArrs = await defaultHouseList.map((item: any) => {
        return { label: item.name, value: item.id };
      });
      return houseArrs;
    }
  }
};
export const formList: SearchFormType[] = [
  {
    label: '日期类型',
    name: 'time_type',
    type: 'select',
    clear: false,
    check: true,
    options: dateTypeOptions,
  },
  {
    width: 388,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
    // disabled: true,
    // format: "YYYY-MM-DD HH:mm:ss",
    // @ts-ignore
  },
  {
    label: '组织',
    name: 'org_ids',
    type: 'select',
    multiple: true,
    allowClear: true,
    hidden: true,
    options: [],
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    allowClear: true,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    onChange: async (e, _, form: FormInstance) => {
      form.setFieldValue(
        'storehouse',
        await getStockData(form.getFieldValue('store_ids')),
      );
      // console.log('onChange',e,_,form,form.getFieldValue('store_ids'))
    },
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      // primaryKey: 'id',
      data: {
        status: true,
        // center_flag:true,
      },
    },
  },
  {
    label: '批发客户',
    name: 'client_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'wholesaler',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    allowClear: true,
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    },
  },
  {
    label: '单据状态',
    name: 'state',
    type: 'select',
    clear: true,
    check: true,
    options: stateType,
  },
  {
    label: '出货仓库',
    name: 'storehouse_ids',
    type: 'select',
    clear: true,
    check: true,
    multiple: true,
    dependencies: ['store_ids'],
    linkId: 'storehouse',
  },
  {
    label: '制单人',
    name: 'create_by',
    // width: columnWidthEnum.BY,
    features: { sortable: true },
  },
  {
    label: '审核人',
    name: 'audit_by',
    // width: 100,
    features: { sortable: true },
  },
];
export const tableColumn: XlbTableColumnProps<any>[] = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
    lock: true,
  },
  {
    name: '单据号',
    code: 'fid',
    width: 186,
    align: 'left',
    features: { sortable: true },
    lock: true,
  },
  {
    name: '单据类型',
    code: 'order_type',
    width: 100,
    align: 'left',
  },
  {
    name: '销售时间',
    code: 'operate_date',
    width: 180,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '审核时间',
    code: 'audit_time',
    width: 180,
    align: 'left',
    features: { sortable: true },
  },
  // {
  //   name: '销售员',
  //   code: 'create_by',
  //   width: 140,
  //   align: 'left',
  //   features: { sortable: true }
  // },
  {
    name: '制单人',
    code: 'create_by',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '审核人',
    code: 'audit_by',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '出货仓库',
    code: 'storehouse_name',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '客户代码',
    code: 'client_code',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '客户名称',
    code: 'client_name',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  // {
  //   name: '客户区域',
  //   code: 'in_store_name',
  //   width: 140,
  //   align: 'left',
  //   features: { sortable: true }
  // },
  {
    name: '商品分类',
    code: 'item_category_name',
    width: 140,
    align: 'left',
    features: { sortable: true },
  },
  // {
  //   name: '系统编码',
  //   code: 'item_name',
  //   width: 140,
  //   align: 'left',
  //   features: { sortable: true }
  // },
  {
    name: '商品代码',
    code: 'item_code',
    width: 100,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_barcode',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 280,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '规格',
    code: 'item_spec',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单位',
    code: 'item_unit',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售数量',
    code: 'quantity',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '不含税单价',
    code: 'no_tax_price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '单价',
    code: 'price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额',
    code: 'money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '不含税金额',
    code: 'no_tax_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '商品采购金额',
    code: 'purchase_money',
    width: 134,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '商品采购金额(去税)',
    code: 'no_tax_purchase_money',
    width: 164,
    features: { sortable: true },
    align: 'right',
  },
  {
    name: '税率(%)',
    code: 'tax_rate',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '销售成本',
    code: 'cost_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '赠品基本数量',
    code: 'basic_present_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '赠品金额',
    code: 'present_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '赠品成本金额',
    code: 'present_cost_money',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本单位',
    code: 'basic_unit',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '基本数量',
    code: 'basic_quantity',
    width: 120,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '基本单价',
    code: 'basic_price',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '批发销售税额',
    code: 'tax_money',
    width: 140,
    align: 'right',
    features: { sortable: true },
  },
  // {
  //   name: '批发销售金额(去税)',
  //   code: 'cost_money',
  //   width: 160,
  //   align: 'right',
  //   features: { sortable: true }
  // },
  {
    name: '部门',
    code: 'item_dept_name',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  {
    name: '备注',
    code: 'memo',
    width: 120,
    align: 'left',
    features: { sortable: true },
  },
  // {
  //   name: '附加备注',
  //   code: 'profit',
  //   width: 140,
  //   align: 'right',
  //   features: { sortable: true }
  // }
];
