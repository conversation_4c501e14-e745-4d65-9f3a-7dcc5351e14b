import { ErpFieldKeyMap } from '@/data/common/fieldListConfig';
import useDownload from '@/hooks/useDownload';
import { hasAuth } from '@/utils';
import {
  XlbButton,
  XlbIcon,
  XlbMessage,
  XlbPrintModal,
  XlbProDetail,
  XlbTipsModal,
} from '@xlb/components';
import { XlbFetch as ErpRequest } from '@xlb/utils';
import { FormInstance } from 'antd';
import Decimal from 'decimal.js';
import { useEffect, useRef, useState } from 'react';
import UploadPhotoGroup from '../components/uploadPhotoGroup';
import { renderProductDetailsTableList } from '../data';
import { auditInfo, confirm, invalid, postPrint } from '../server';

const StoreOrdersItem = (props) => {
  const { record, onBack } = props;
  // const { dropScope } = useAliveController();
  // dropScope('/xlb_erp/storeOrders/storeItem');
  const fidRef = useRef<any | null>(null);
  const { downByProgress } = useDownload();
  const inVoiceForm = useRef<any>();
  const formInstance = useRef<FormInstance<any>>();
  const [isLoading, setIsLoading] = useState(false);
  const [isReadOnly, setIsReadOnly] = useState(false);
  const proDetailRef = useRef<any>(null);
  // 确认
  const requestConfirm = async (obj: any) => {
    await XlbTipsModal({
      tips: `是否确认确认${obj.fid}数据?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const data = {
          ...obj,
          details: obj.details.map((item: any) => {
            return {
              ...item,
              basic_receive_quantity: new Decimal(item.receive_quantity ?? 0)
                .mul(new Decimal(item.ratio ?? 0))
                .toNumber(),
            };
          }),
        };
        const res = await confirm(data);
        if (res.code === 0) {
          XlbMessage.success('确认成功');
          onBack(true);
        }
        return true;
      },
    });
  };
  // 打印
  const printCallBack = async () => {
    const data = {
      fid: fidRef.current,
    };
    const res = await postPrint(data);
    if (res.code === 0) {
      XlbPrintModal({
        data: res?.data,
        title: '门店订单打印详情',
      });
    }
  };
  const audit = async (formValue: any) => {
    try {
      await proDetailRef.current?.form?.validateFields();
    } catch (err: any) {
      throw err;
    }
    const bool = await XlbTipsModal({
      tips: `是否确认审核`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        setIsLoading(true);
        const { code, data } = await auditInfo({ ...formValue });
        if (code === 0) {
          XlbMessage.success('审核成功！');
          onBack(true);
        }
        setIsLoading(false);
        return true;
      },
    });
  };
  const exportItem = async (e: any = null, requestForm: any) => {
    const data = {
      fid: fidRef.current,
    };
    const res = await ErpRequest.post('/erp/hxl.erp.storeorder.export', data);
    if (res.code === 0) {
      await downByProgress(e);
      XlbMessage.success(res?.data);
    }
  };
  const handleChangePhotoGruop = (photoGroup: any) => {
    formInstance.current?.setFieldValue('photo_groups', photoGroup);
  };
  const requestInvalid = async (obj: any) => {
    await XlbTipsModal({
      tips: `是否确认作废${obj.fid}数据?`,
      isCancel: true,
      onOkBeforeFunction: async () => {
        const res = await invalid({ fid: obj.fid });
        if (res.code === 0) {
          XlbMessage.success('作废成功');
          onBack(true);
        }
        return true;
      },
    });
  };
  const deleteItem = (dataSource: any, selectRow: any, form: any) => {
    // 提取 selectRow 中的 id，存储为 Set
    const selectRowIds = new Set(selectRow.map((item: any) => item.id));

    // 过滤掉 dataSource 中与 selectRow 中 id 相同的数据
    const afterDeleteDataSource =
      dataSource.filter((dataItem: any) => !selectRowIds.has(dataItem.id)) ||
      [];

    XlbMessage.success('删除成功');

    form?.setFieldValue('details', afterDeleteDataSource);
  };
  const getData = async () => {
    setIsLoading(true);
    const data = {
      fid: fidRef.current,
    };
    const res = await ErpRequest.post('/erp/hxl.erp.storeorder.read', data);
    if (res.code === 0) {
      const params = {
        store_ids: [res?.data?.store_id],
        supplier_id: res?.data?.supplier_id,
      };
      const addres = await ErpRequest.post(
        '/erp/hxl.erp.directsupplypoint.config.stores.exist',
        params,
      );
      const data = {
        ...res.data,
        details: res.data?.details?.map((item: any) => ({
          ...item,
          temperature: item?.temperature_info?.temperature || 0,
          image_urls: item?.temperature_info?.image_urls,
          id: item?.item_id, // 详情查出的商品没有id，导致新增弹窗里没有勾上
        })),
        ref_order_type: res.data?.details?.[0]?.ref_order_type || '',
        ref_order_fid: res.data?.details?.[0]?.ref_order_fid || '',
        isAdd: addres?.data?.[res?.data?.store_id],
      };
      setIsReadOnly(
        data.state !== 'INIT' || data.type !== 'DIRECT_SUPPLY_RESERVE',
      );
      setTimeout(() => {
        formInstance.current?.setFieldsValue(data);
      }, 5);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (record.fid) {
      fidRef.current = record.fid;
      getData().then();
    }
  }, []);

  return (
    <XlbProDetail
      ref={proDetailRef}
      title="门店订单详情"
      initialValues={{
        state: 'INIT',
        type: 'DIRECT_SUPPLY_RESERVE',
      }}
      refreshAndClose={true}
      saveFieldProps={{
        url: hasAuth(['门店订单/直供', '编辑'])
          ? '/erp/hxl.erp.storeorder.save'
          : '',
        beforePost: (data: any) => {
          if (!data.details?.length) {
            XlbMessage.error('请选择商品');
            return false;
          }
          const invalidItem = data.details.find((item: any) => {
            return (
              (!item.price && item.price != 0) ||
              (!item.quantity && item.quantity != 0)
            );
          });
          if (invalidItem) {
            XlbMessage.error('请完善商品数据');
            return false;
          }
          return {
            ...data,
            type: 'DIRECT_SUPPLY_RESERVE',
            details: data.details.map((item: any) => {
              return {
                ...item,
                temperature_info: {
                  temperature: item.temperature,
                  image_urls: item.image_urls,
                },
              };
            }),
          };
        },
        afterPost(data, oldFormValues) {
          fidRef.current = data?.fid;
          getData();
          return null;
        },
        hidden(formValues) {
          return (
            formValues.state !== 'INIT' ||
            formValues.type !== 'DIRECT_SUPPLY_RESERVE'
          );
        },
      }}
      updateFieldProps={{
        url: hasAuth(['门店订单/直供', '编辑'])
          ? '/erp/hxl.erp.storeorder.update'
          : '',
        beforePost: (data) => {
          if (!data.details?.length) {
            XlbMessage.error('请选择商品');
            return false;
          }
          const invalidItem = data.details.find((item: any) => {
            return (
              (!item.price && item.price != 0) ||
              (!item.quantity && item.quantity != 0)
            );
          });
          if (invalidItem) {
            XlbMessage.error('请完善商品数据');
            return false;
          }
          const newData = data.details.map((item: any) => {
            return {
              ...item,
              temperature_info: {
                temperature: item.temperature,
                image_urls: item.image_urls,
              },
            };
          });
          return {
            ...data,
            details: newData,
          };
        },
        afterPost(data, oldFormValues) {
          getData();
          return null;
        },
        hidden(formValues) {
          return formValues.state === 'AUDIT';
        },
      }}
      primaryKey="fid"
      key={isReadOnly.toString()}
      extra={({ values, form, ...args }: any) => {
        inVoiceForm.current = values;
        formInstance.current = form;
        return (
          <XlbButton.Group>
            {hasAuth(['门店订单', '审核']) && (
              <XlbButton
                label="审核"
                type="primary"
                disabled={
                  values?.state === 'AUDIT' ||
                  !values?.fid ||
                  values?.state === 'INVALID' ||
                  isLoading
                }
                onClick={(e) => audit(values)}
                icon={<XlbIcon name="shenhe" />}
              />
            )}
            {hasAuth(['门店订单', '导出']) && (
              <XlbButton
                label="导出"
                type="primary"
                disabled={!values?.fid || isLoading}
                onClick={(e) => exportItem(e, values)}
                icon={<XlbIcon name="daochu" />}
              />
            )}
            <UploadPhotoGroup
              onChangePhotoGruop={handleChangePhotoGruop}
              details={values}
            />
            {hasAuth(['门店订单', '作废']) && (
              <XlbButton
                label="作废"
                type="primary"
                disabled={
                  isLoading ||
                  values?.state !== 'AUDIT' ||
                  !hasAuth(['门店订单/直供', '作废']) ||
                  (values?.confirm_state !== 'INIT' &&
                  values?.confirm_state !== 'SUPPLIER_CONFIRM'
                    ? true
                    : false)
                }
                onClick={() => requestInvalid(values)}
                icon={<XlbIcon name="shenqingtuihuo" />}
              />
            )}
            {hasAuth(['门店订单', '确认']) && (
              <XlbButton
                label="确认"
                type="primary"
                disabled={
                  isLoading ||
                  (values?.confirm_state === 'SUPPLIER_CONFIRM' &&
                  values?.state === 'AUDIT'
                    ? false
                    : true)
                }
                onClick={() => requestConfirm(values)}
                icon={<XlbIcon name="shengchengXXdan" />}
              />
            )}
            {hasAuth(['门店订单', '打印']) && (
              <XlbButton
                label="打印"
                disabled={isLoading}
                type="primary"
                onClick={() => printCallBack()}
              />
            )}
            <XlbButton
              label="返回"
              type="primary"
              icon={<XlbIcon name={'fanhui'} />}
              onClick={() => onBack(false)}
            />
          </XlbButton.Group>
        );
      }}
      readOnly={isReadOnly}
      formList={[
        {
          componentType: 'tabs',
          name: 'tabs',
          initialValue: '3',
          fieldProps: {
            style: {
              marginTop: '-12px',
            },
            items: [
              {
                key: '3',
                label: '基本信息',
                children: (obj: any) => [
                  {
                    componentType: 'form',
                    fieldProps: {
                      itemSpan: 6,
                      width: '100%',
                      formList: [
                        obj?.state === 'INIT' &&
                        obj?.type === 'DIRECT_SUPPLY_RESERVE'
                          ? {
                              id: ErpFieldKeyMap?.erpSupplierId,
                              label: '供应商',
                              dependencies: ['details'],
                              disabled(formValues: any) {
                                return formValues.details?.length > 0
                                  ? true
                                  : false;
                              },
                              fieldProps: {
                                handleValueChange: () => {
                                  formInstance?.current?.setFieldValue(
                                    'store_id',
                                    '',
                                  );
                                  formInstance?.current?.setFieldValue(
                                    'storehouse_id',
                                    '',
                                  );
                                },
                                style: {
                                  width: '100%',
                                },
                              },
                            }
                          : {
                              id: 'commonInput',
                              label: '供应商',
                              name: 'supplier_name',
                            },
                        obj?.state === 'INIT' &&
                        obj?.type === 'DIRECT_SUPPLY_RESERVE'
                          ? {
                              id: ErpFieldKeyMap?.erpReceivingStores,
                              label: '收货门店',
                              name: 'store_id',
                              dependencies: ['supplier_id', 'details'],
                              rules: [
                                {
                                  required: true,
                                  message: '请选择收货门店',
                                },
                              ],
                              fieldProps(formValue: any) {
                                return {
                                  dialogParams: {
                                    type: 'store',
                                    dataType: 'lists',
                                    isMultiple: false,
                                    url: '/erp-mdm/hxl.erp.store.short.page',
                                    data: {
                                      supplier_id:
                                        formValue.getFieldValue(
                                          'supplier_id',
                                        ) || '',
                                      query_supply: true,
                                      status: true,
                                      center_flag: false,
                                    },
                                  },
                                  handleValueChange: async (e: any) => {
                                    formValue.setFieldValue(
                                      'storehouse_id',
                                      '',
                                    );
                                    if (
                                      formValue.getFieldValue('supplier_id') &&
                                      e?.length
                                    ) {
                                      setIsLoading(true);
                                      // 直供加点逻辑
                                      const data = {
                                        store_ids: [e?.[0]],
                                        supplier_id:
                                          formValue.getFieldValue(
                                            'supplier_id',
                                          ),
                                      };
                                      const addres = await ErpRequest.post(
                                        '/erp/hxl.erp.directsupplypoint.config.stores.exist',
                                        data,
                                      );
                                      if (addres?.code !== 0) return;
                                      formValue.setFieldValue(
                                        'isAdd',
                                        addres?.data?.[e?.[0]],
                                      );
                                      const params = {
                                        store_id: e?.[0],
                                        supplier_id:
                                          formValue.getFieldValue(
                                            'supplier_id',
                                          ),
                                      };
                                      const res = await ErpRequest.post(
                                        '/erp/hxl.erp.contract.storeorder.effectiveness',
                                        params,
                                      );
                                      setIsLoading(false);
                                      if (res?.code !== 0) return;
                                      const {
                                        is_all_signed,
                                        need_sign_info,
                                        contract_name,
                                        error_msg,
                                      } = res.data || {};
                                      if (is_all_signed === false) {
                                        const tipsContent = error_msg || (
                                          <div>
                                            <div
                                              style={{
                                                fontWeight: 'bold',
                                                fontSize: 15,
                                                marginBottom: 10,
                                              }}
                                            >
                                              {need_sign_info} 当前不能下单
                                            </div>
                                            <div>
                                              签署《{contract_name}
                                              》后可以下单
                                            </div>
                                            <div>
                                              如果您已签署，请耐心等待平台维护签署状态，或联系平台处理
                                            </div>
                                          </div>
                                        );
                                        await XlbTipsModal({
                                          isConfirm: true,
                                          isCancel: false,
                                          tips: tipsContent,
                                        });
                                        onBack(true);
                                      }
                                    }
                                  },
                                  fieldNames: {
                                    idKey: 'id',
                                    nameKey: 'store_name',
                                  },
                                };
                              },
                              disabled(formValues: any) {
                                return (
                                  !formValues.supplier_id ||
                                  (formValues.details?.length > 0
                                    ? true
                                    : false)
                                );
                              },
                            }
                          : {
                              id: 'commonInput',
                              label: '收货门店',
                              name: 'store_name',
                            },
                        obj?.state === 'INIT' &&
                        obj?.type === 'DIRECT_SUPPLY_RESERVE'
                          ? {
                              id: ErpFieldKeyMap?.erpStorehouseId,
                              dependencies: ['store_id'],
                              label: '收货仓库',
                              name: 'storehouse_id',
                              disabled: true,
                            }
                          : {
                              id: 'commonInput',
                              label: '收货仓库',
                              name: 'storehouse_name',
                            },
                        {
                          id: ErpFieldKeyMap?.erpDocumentNumber,
                          label: '单据号',
                          disabled: true,
                          fieldProps: {
                            style: {
                              width: '100%',
                            },
                          },
                          name: 'fid',
                        },
                        {
                          id: ErpFieldKeyMap?.deliveryDate,
                          label: '收货日期',
                          disabled: true,
                          name: 'date',
                        },
                        {
                          id: ErpFieldKeyMap?.erpDownstreamDocuments,
                          label: '下游单据',
                          name: 'ref_order_type',
                          disabled: true,
                        },
                        {
                          id: 'commonInput',
                          label: '下游单据号',
                          name: 'ref_order_fid',
                          disabled: true,
                          fieldProps: {
                            style: {
                              width: '100%',
                            },
                          },
                        },
                        {
                          id: ErpFieldKeyMap?.erpCommonUpload,
                          label: '到货单',
                          name: 'receive_order_image_urls',
                          fieldProps: {
                            mode: 'textButton',
                            multiple: true,
                            maxCount: 10,
                            disabled: true,
                            listType: 'picture',
                            action: '/erp/hxl.erp.storeorder.file.upload',
                            accept: ['image'],
                            data: {
                              fid: inVoiceForm.current?.fid || '',
                              type: 'RECEIVE',
                            },
                          },
                        },
                        {
                          id: ErpFieldKeyMap?.estimatedArrivalDate,
                          label: '预计到货日期',
                          disabled: true,
                          name: 'estimated_arrival_date_str',
                          fieldProps: {
                            style: {
                              width: '100%',
                            },
                          },
                        },
                        {
                          id: ErpFieldKeyMap?.erpMemoInput,
                          label: '留言备注',
                          itemSpan: '12',
                          name: 'memo',
                        },
                      ],
                    },
                  },
                ],
              },
              {
                key: '4',
                label: '其他信息',
                children: [
                  {
                    componentType: 'form',
                    fieldProps: {
                      itemSpan: 6,
                      readOnly: true,
                      width: '100%',
                      formList: [
                        {
                          id: ErpFieldKeyMap?.erpCreateBy,
                          label: '制单人',
                          name: 'create_by',
                        },
                        {
                          id: ErpFieldKeyMap?.deliveryDate,
                          label: '制单时间',
                          name: 'create_time',
                          fieldProps: {
                            format: 'YYYY-MM-DD HH:mm:ss',
                          },
                        },
                        {
                          id: ErpFieldKeyMap?.erpAuditBy,
                          label: '审核人',
                          name: 'audit_by',
                        },
                        {
                          id: ErpFieldKeyMap?.deliveryDate,
                          label: '审核时间',
                          name: 'update_time',
                          fieldProps: {
                            format: 'YYYY-MM-DD HH:mm:ss',
                          },
                        },
                        {
                          id: ErpFieldKeyMap?.erpAuditBy,
                          label: '作废人',
                          name: 'invalid_by',
                        },
                        {
                          id: ErpFieldKeyMap?.deliveryDate,
                          label: '作废时间',
                          name: 'invalid_time',
                          fieldProps: {
                            format: 'YYYY-MM-DD HH:mm:ss',
                          },
                        },
                      ],
                    },
                  },
                ],
              },
            ],
          },
        },
        {
          componentType: 'blueBar',
          fieldProps: {
            title: '商品明细',
          },
          dependencies: ['tabs', 'store_id', 'supplier_id', 'details'],
          children: (obj: any) => {
            const footerDataSource =
              obj?.details?.length > 0
                ? [
                    {
                      _index: '合计',
                      quantity: obj?.details?.reduce(
                        (acc: any, curr: any) => acc + (curr?.quantity || 0),
                        0,
                      ),
                      actual_delivered_quantity: obj?.details?.reduce(
                        (acc: any, curr: any) =>
                          acc + (curr?.actual_delivered_quantity || 0),
                        0,
                      ),
                      receive_quantity: obj?.details?.reduce(
                        (acc: any, curr: any) =>
                          acc + (curr?.receive_quantity || 0),
                        0,
                      ),
                      money: hasAuth(['门店订单/采购价', '查询'])
                        ? obj?.details
                            ?.reduce(
                              (acc: any, curr: any) => acc + (curr?.money || 0),
                              0,
                            )
                            ?.toFixed(2)
                        : '****',
                      // 如果存在receive_quantity为null，则直接展示-
                      receive_money: hasAuth(['门店订单/采购价', '查询'])
                        ? obj?.details?.some(
                            (item: any) =>
                              item?.receive_quantity == null ||
                              item?.receive_quantity == undefined ||
                              item?.receive_quantity == '',
                          )
                          ? '-'
                          : obj?.details
                              ?.reduce(
                                (acc: any, curr: any) =>
                                  acc + (curr?.receive_money || 0),
                                0,
                              )
                              ?.toFixed(2)
                        : '****',
                      no_tax_money: hasAuth(['门店订单/采购价', '查询'])
                        ? obj?.details
                            ?.reduce(
                              (acc: any, curr: any) =>
                                acc +
                                  new Decimal(curr?.money ?? 0)
                                    .div(
                                      new Decimal(curr?.input_tax_rate ?? 0)
                                        .div(new Decimal(100))
                                        .add(new Decimal(1)),
                                    )
                                    .toNumber() || 0,
                              0,
                            )
                            ?.toFixed(2)
                        : '****',
                      basic_quantity: obj?.details
                        ?.reduce(
                          (acc: any, curr: any) =>
                            acc + (curr?.basic_quantity || 0),
                          0,
                        )
                        ?.toFixed(3),
                    },
                  ]
                : [];
            const auth =
              !hasAuth(['门店订单/直供', '编辑']) ||
              obj.state !== 'INIT' ||
              obj.type !== 'DIRECT_SUPPLY_RESERVE';
            return [
              {
                componentType: 'editTable',
                disabled: !obj.store_id,
                isLoading: isLoading,
                name: 'details',
                key: obj?.tabs,
                fieldProps: {
                  columns: renderProductDetailsTableList(obj),
                  footerDataSource: footerDataSource,
                  style: {
                    height:
                      obj?.tabs == '4'
                        ? `calc(100vh - ${auth ? '354px' : '382px'})`
                        : `calc(100vh - ${auth ? '434px' : '462px'})`,
                  },
                  dialogParams: {
                    type: 'goods',
                    url: '/erp/hxl.erp.storeorder.item.page?sort=1',
                    dataType: 'lists',
                    immediatePost: false,
                    isLeftColumn: true,
                    isMultiple: true,
                    primaryKey: 'id',
                    data: {
                      type: 'DIRECT_SUPPLY_RESERVE',
                      enabled: true,
                      store_id: obj.store_id,
                      supplier_id: obj.supplier_id,
                      storehouse_id: obj.storehouse_id,
                      isShowStoreOrders: true,
                    },
                  },
                  afterPost: (newData: any, oldData: any) => {
                    // 初始化选中数据的价格
                    const newDataInitPriceList = newData.map((item: any) => {
                      return {
                        ...item,
                        price: new Decimal(item.basic_price ?? 0)
                          .mul(
                            new Decimal(
                              (item.store_item_supplier_default_unit == 'BASIC'
                                ? 1
                                : item.purchase_ratio) ?? 0,
                            ),
                          )
                          .toNumber(),
                        item_id: item.id,
                        item_code: item.code,
                        item_bar_code: item.bar_code,
                        item_name: item.name,
                        ratio:
                          item.store_item_supplier_default_unit == 'BASIC'
                            ? 1
                            : item.purchase_ratio,
                        basic_unit: item.unit,
                        unit:
                          item.store_item_supplier_default_unit == 'BASIC'
                            ? item.unit
                            : item.purchase_unit,
                      };
                    });

                    // 已选中列的数据不能被替换
                    const updatedData = newDataInitPriceList.map(
                      (newItem: any) => {
                        const oldItem = oldData.find(
                          (oldItem: any) => oldItem.id === newItem.id,
                        );
                        return oldItem ? oldItem : newItem;
                      },
                    );

                    return updatedData;
                  },
                  addConfig: {
                    label: '批量新增',
                    dependencies: ['store_id'],
                    hidden: auth,
                  },
                  delConfig: {
                    hidden: true,
                  },
                  extra: (context: any) => {
                    const { dataSource, selectRow, form } = context;

                    // 更新表格后dataSource是最新的，selectRow没有重置，导致disabled不起作用，所以要过滤
                    const isExist = selectRow.some((selectItem: any) =>
                      dataSource.some(
                        (dataItem: any) => dataItem.id === selectItem.id,
                      ),
                    );

                    return auth ? (
                      ''
                    ) : (
                      <XlbButton
                        label="删除"
                        type="primary"
                        disabled={selectRow?.length === 0 || !isExist}
                        onClick={() => deleteItem(dataSource, selectRow, form)}
                        icon={<XlbIcon name="shanchu" />}
                      />
                    );
                  },
                },
              },
            ];
          },
        },
      ]}
    />
  );
};

export default StoreOrdersItem;
