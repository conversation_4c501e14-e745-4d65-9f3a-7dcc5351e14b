import { LStorage } from '@/utils/storage';
import { cloneDeep } from 'lodash';
import reduce from 'lodash/reduce';
import React from 'react';
export type ITreeData<T> = T & { children?: Array<T>; deep?: number };
type Options<T> = {
  idKey: keyof T;
  pidKey: keyof T;
  pidValue?: T[keyof T];
};

// 模块按钮权限判断
export const hasAuth = (keys: string[] | number) => {
  const user = LStorage.get('userInfo');
  if (!keys || !user) return false;
  let auths = user.authorities;
  if (!auths || auths.length === 0) return false;
  /* 对于重复菜单的权限控制通过权限 id */
  if (typeof keys === 'number' && auths.some((auth) => auth.id === keys)) {
    return true;
  }
  if (auths.some((v) => v.path == keys[0] && v.action == keys[1])) {
    return true;
  }
  return false;
};
/**
 * @name 打平树为list
 * @param tree
 * @returns
 */
export function flattenTree<T extends unknown = any>(
  tree: Array<T>,
  childrenKey = 'children',
) {
  return reduce<any, T[]>(
    tree,
    (result, node) => {
      result.push(node);
      // 如果节点有子节点，则递归调用 flattenTree 函数
      if (node[childrenKey] && node[childrenKey].length > 0) {
        result.push(...flattenTree(node[childrenKey]));
      }
      return result;
    },
    [],
  );
}
export const getTree = (list: any) => {
  const result: Array<number> = [];
  if (!Array.isArray(list)) {
    return result;
  }
  list.forEach((item) => {
    delete item.children;
  });
  const obj: any = {};
  list.forEach((item) => {
    obj[item.id] = item;
  });
  list.forEach((item) => {
    const parent = obj[item.parent_id];
    if (parent) {
      (parent.children || (parent.children = [])).push(item);
    } else {
      result.push(item);
    }
  });
  return result;
};

// 老系统菜单权限判断
export const hasOldAuth = (keys: any[]) => {
  if (keys.length === 0 && keys[0]) return true;
  let isAuth = LStorage.get('old_userInfo')?.permission;
  if (isAuth) {
    keys.map((v) => (isAuth = isAuth ? isAuth[v] : false));
  } else {
    isAuth = false;
  }
  return !!isAuth;
};

export const getParams = (url: string) => {
  if (!url.includes('?')) {
    return {};
  }
  let str = url.split('?')[1]; //?号后面的就是我们需要的name=quan&age=21&sex=1
  let arr = str.split('&'); //["name=quan", "age=21", "sex=1"]
  let obj = {};
  for (let i = 0; i < arr.length; i++) {
    let key = arr[i].split('='); //["name", "quan"] ["age", "21"] ["sex", "1"]
    obj[key[0]] = key[1];
  }
  return obj;
};

/**
 * @name 遍历vnode 找到文本为止
 *
 */
export const findVnodeText = (vnode: React.ReactNode): string | number => {
  let text: string | number;
  if (React.isValidElement(vnode)) {
    const children = vnode.props.children;
    if (React.isValidElement(children)) {
      text = findVnodeText(children);
    } else {
      text = children;
    }
  } else if (typeof vnode === 'string') {
    text = vnode;
  } else if (typeof vnode === 'number') {
    text = vnode;
  } else {
    text = '';
  }
  return text;
};
// 转变成千分位的方法
export const formatWithCommas = (value) => {
  if (!value) return 0;
  try {
    if (typeof value !== 'number' && typeof value !== 'string') {
      throw new Error('Invalid input: value must be a number or a string');
    }

    const formattedValue = value
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return formattedValue;
  } catch (error) {
    console.error('Error formatting number with commas:', error);
    return value;
  }
};
/**
 * 折叠list为树形结构
 * @param arr { Array<T> }  待处理的list
 * @param options { Options<T> } 配置参数
 * @param cb { Function } 针对每一项的数据结构变更
 * @return { Array<ITreeComment<T>> } 树形数据
 */
export const coverListToTree = <T extends Object>(
  arr?: Array<T>,
  options?: Options<T>,
  cb: (data: T) => T = (data) => data,
): Array<ITreeData<T>> => {
  if (!Array.isArray(arr)) return [];
  if (!options) return [];
  const { idKey, pidKey, pidValue } = options;
  const list = cloneDeep(arr).map(cb);

  type TMapper = {
    [key in string | number]?: ITreeData<T>;
  };
  const mapper: TMapper = {};
  const result: Array<ITreeData<T>> = [];

  list.forEach((item) => {
    const itemData = item as ITreeData<T>;
    const value: any = item[idKey];
    mapper[value] = itemData;
  });

  list.forEach((item) => {
    const itemData = item as ITreeData<T>;
    const linkPid: any = item[pidKey];
    if (linkPid === pidValue || !linkPid) {
      itemData.deep = 0;
      result.push(itemData);
    }
    const parentItem = mapper[linkPid];
    if (parentItem) {
      itemData.deep = Number(parentItem.deep) + 1;
      parentItem.children = parentItem?.children || [];
      parentItem.children.push(itemData);
    }
  });

  return result;
};

// 处理单据号
export const normalizeToCommaSeparated = (str: string) => {
  if (!str || typeof str !== 'string') return '';
  return str
    .replace(/，/g, ',') // 显式替换中文逗号为英文逗号
    .replace(/[^a-zA-Z0-9]+/g, ',') // 其他非字母数字字符统一转逗号
    .replace(/^,|,$/g, '') // 去除首尾逗号
    .replace(/,+/g, ','); // 合并连续逗号
};
