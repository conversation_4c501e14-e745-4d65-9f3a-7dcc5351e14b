export const AdvancePositionKeyMap = {
  /**
   * 门店多选，过滤管理中心门店
   */
  erpCenterStoreIdsMultipleEnable: 'erpCenterStoreIdsMultipleEnable'
}

export const advancePositionConfig: any[] = [
  {
    tag: 'ERP',
    label: '配送中心门店',
    id: AdvancePositionKeyMap?.erpCenterStoreIdsMultipleEnable,
    dependencies: ['org_ids'],
    name: 'store_ids',
    fieldProps: (form) => {
      const data = {
        ...form.getFieldsValue(['org_ids'])
      }
      if (!form?.getFieldsValue(true).org_ids) {
        delete data?.org_ids
      }
      return {
        dialogParams: {
          type: 'store',
          dataType: 'lists',
          isMultiple: true,
          data: {
            ...data,
            center_flag: true,
            enable_organization: false
          }
        },
        fieldNames: {
          idKey: 'id',
          nameKey: 'store_name'
        }
      }
    },
    formItemProps: {
      label: '门店'
    },
    componentType: 'inputDialog'
  }
]
