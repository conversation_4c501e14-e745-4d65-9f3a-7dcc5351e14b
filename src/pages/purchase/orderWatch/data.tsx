import { columnWidthEnum } from '@/data/common/constant';
import { SearchFormType, XlbTableColumnProps } from '@xlb/components';

export const queryUnit = [
  {
    label: '采购单位',
    value: 'PURCHASE',
  },
  {
    label: '基本单位',
    value: 'BASIC',
  },
];

export const receive_Type = [
  {
    label: '未收货',
    value: 'UNRECEIVE',
  },
  {
    label: '部分收货',
    value: 'PARTRECEIVE',
  },
  {
    label: '全部收货',
    value: 'ALLRECEIVE',
  },
];
export const ORDER_FILTER_LIST = [
  {
    label: '仅显示未到货商品',
    value: 'only_show_un_receive_item',
  },
];
export const ORDER_STATE_FILTER_LIST = [
  {
    label: '仅显示未到货商品',
    value: 'only_show_un_receive_item',
  },
  {
    label: '过滤作废日期=审核日期',
    value: 'invalid_equal_audit_date',
  },
];
export const ORDER_STATE_LIST = [
  {
    label: '审核',
    value: 'AUDIT',
  },
  {
    label: '作废',
    value: 'INVALID',
  },
];
export const ALL_ORDER_STATE_LIST = [
  ...ORDER_STATE_LIST,
  {
    label: '制单',
    value: 'INIT',
  },
];

export const formListData: SearchFormType[] = [
  {
    label: '时间类型',
    name: 'time_type',
    type: 'select',
    options: [
      { label: '创建日期', value: 'create_date' },
      { label: '审核日期', value: 'audit_date' },
    ],
    hidden: false,
  },
  {
    width: 372,
    type: 'compactDatePicker',
    label: '日期选择',
    name: 'compactDatePicker',
    allowClear: false,
  },
  {
    label: '订单状态',
    name: 'order_state',
    type: 'select',
    options: ORDER_STATE_LIST,
    hidden: false,
  },
  {
    label: '应用组织',
    name: 'org_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择组织',
      url: '/erp-mdm/hxl.erp.org.tree',
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
    },
  },
  {
    label: '门店',
    name: 'store_ids',
    type: 'inputDialog',
    dialogParams: {
      type: 'store',
      dataType: 'lists',
      isLeftColumn: true,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        status: true,
      },
      nullable: false,
    },
    allowClear: false,
    fieldNames: {
      // @ts-ignore
      idKey: 'id',
      nameKey: 'store_name',
    },
    removeIcon: null,
    width: 200,
  },
  {
    label: '货主',
    name: 'cargo_owner_ids',
    type: 'inputDialog',
    dialogParams: {
      dataType: 'lists',
      primaryKey: 'id',
      type: 'cargoOwner',
      isMultiple: true,
      isLeftColumn: false,
      data: {
        owner_type: 'ORGANIZATION',
      },
    },
    fieldNames: {
      idKey: 'id',
      nameKey: 'source_name',
    } as any,
    width: 200,
  },
  {
    label: '仓库',
    name: 'storehouse_ids',
    type: 'select',
    multiple: true,
    hidden: true,
    dependencies: ['store_ids'],
    dropdownStyle: { width: 200 },
    disabled: (form) => {
      const store_ids = form.getFieldValue('store_ids');
      return !store_ids || store_ids?.length > 1;
    },
    handleDefaultValue: (data: any, formData: any) => {
      if (data?.length === 0) {
        return null;
      }
      const defaultStoreHouse =
        data.find((item: any) => item.default_flag) || data[0];
      return defaultStoreHouse?.value;
    },
    // @ts-ignore
    selectRequestParams: (params: any, form) => {
      form?.setFieldsValue({
        storehouse_ids: null,
      });
      if (params?.store_ids?.length == 1) {
        return {
          url: '/erp/hxl.erp.storehouse.store.find',
          postParams: {
            store_id: params?.store_ids?.[0],
          },
          responseTrans(data: any) {
            const options = data.map((item: any) => ({
              label: item.name,
              value: item.id,
              default_flag: item.default_flag,
            }));
            return options;
          },
        };
      }
    },
  },
  {
    label: '供应商',
    name: 'supplier_ids',
    type: 'inputDialog',
    check: true,
    dialogParams: {
      type: 'supplier',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品部门',
    name: 'item_dept_ids',
    type: 'inputDialog',
    hidden: true,
    check: true,
    dialogParams: {
      type: 'productDept',
      dataType: 'lists',
      isLeftColumn: false,
      isMultiple: true,
      primaryKey: 'id',
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品档案',
    name: 'item_ids',
    type: 'inputDialog',
    check: true,
    dialogParams: {
      type: 'goods',
      dataType: 'lists',
      isMultiple: true,
      data: {
        enabled: true,
      },
    },
  },
  {
    label: '商品类别',
    name: 'item_category_ids',
    type: 'inputDialog',
    treeModalConfig: {
      title: '选择商品分类', // 标题
      url: '/erp-mdm/hxl.erp.category.find', // 请求地址
      dataType: 'lists',
      checkable: true, // 是否多选
      primaryKey: 'id',
      data: {
        enabled: true,
      },
      width: 360, // 模态框宽度
    } as any,
    width: 200,
  },
  {
    label: '查询单位',
    name: 'unit_type',
    type: 'select',
    options: queryUnit,
    allowClear: false,
    check: true,
    hidden: false,
  },
  {
    label: '单据号',
    name: 'fid',
    type: 'input',
    check: true,
    placeholder: '请输入单据号',
  },
  {
    label: '是否过期',
    name: 'expired',
    type: 'select',
    // @ts-ignore
    options: [
      { label: '是', value: 'true' },
      { label: '否', value: 'false' },
    ],
    clear: true,
    check: true,
    hidden: false,
  },
  {
    label: '过滤',
    name: 'check_filter',
    className: 'check_filter_box_row',
    clear: true,
    check: true,
    colon: false,
    type: 'checkbox',
    options: ORDER_STATE_FILTER_LIST,
  },
];

// table
export const ddzt: XlbTableColumnProps<any>[] = [
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '保质期',
    code: 'period',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '一级分类',
    code: 'one_item_category_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '二级分类',
    code: 'two_item_category_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '三级分类',
    code: 'three_item_category_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '交货周期',
    code: 'purchase_period',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '采购数量',
    code: 'quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },

  {
    name: '采购金额',
    code: 'money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货数量',
    code: 'receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货金额',
    code: 'receive_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未到货数量',
    code: 'un_receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未到货金额',
    code: 'un_receive_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货率',
    code: 'receive_rate',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货次数',
    code: 'receive_count',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '赠品数量',
    code: 'present_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '赠品到货数量',
    code: 'present_receive_quantity',
    width: 200,
    align: 'right',
    features: { sortable: true },
  },
];

export const dddhl: XlbTableColumnProps<any> = [
  // {
  //   name: '序号',
  //   code: '_index',
  //   width: 60
  //   // align: 'center'
  // },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购数量',
    code: 'quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },

  {
    name: '采购金额',
    code: 'money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货数量',
    code: 'receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货金额',
    code: 'receive_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未到货数量',
    code: 'un_receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未到货金额',
    code: 'un_receive_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货率',
    code: 'receive_rate',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货次数',
    code: 'receive_count',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
];

export const gqdd: XlbTableColumnProps<any> = [
  // {
  //   name: '序号',
  //   code: '_index',
  //   width: 60,
  //   align: 'center'
  // },
  {
    name: '供应商',
    code: 'supplier_name',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '单号',
    code: 'fid',
    width: columnWidthEnum.fid,
    features: { sortable: true },
  },
  {
    name: '到期日期',
    code: 'delivery_deadline',
    width: 160,
    features: { sortable: true },
  },
  {
    name: '过期天数',
    code: 'expire_day',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '完成情况',
    code: 'receive_state',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '',
  },
];

export const ddsphz: XlbTableColumnProps<any> = [
  {
    name: '序号',
    code: '_index',
    width: 60,
    align: 'center',
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '商品条码',
    code: 'item_bar_code',
    width: columnWidthEnum.ITEM_BAR_CODE,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'item_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '采购单位',
    code: 'unit',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '采购数量',
    code: 'quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },

  {
    name: '采购金额',
    code: 'money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货数量',
    code: 'receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '到货金额',
    code: 'receive_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未到货数量',
    code: 'un_receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未到货金额',
    code: 'un_receive_money',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '数量到货率',
    code: 'receive_rate',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '金额到货率',
    code: 'receive_money_rate',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '采购赠品量',
    align: 'right',
    code: 'purchase_present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品到货量',
    align: 'right',
    code: 'receive_present_quantity',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '赠品未到货量',
    align: 'right',
    code: 'un_receive_present_quantity',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '赠品到货率',
    align: 'right',
    code: 'receive_present_rate',
    width: 200,
    features: { sortable: true },
  },
  {
    name: '最早下单日期',
    code: 'earliest_order_date',
    width: 200,
    features: { sortable: true },
  },
];

export const djhddmx: XlbTableColumnProps<any> = [
  {
    name: '序号',
    code: '_index',
    width: columnWidthEnum.INDEX,
    align: 'center',
  },
  {
    name: '组织',
    code: 'org_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '收货门店',
    code: 'store_name',
    width: 120,
    features: { sortable: true },
  },

  {
    name: '货主',
    code: 'cargo_owner_id',
    width: 120,
    features: { sortable: true },
    render: (_text: any, record: any) => <span>{record.cargo_owner_name}</span>,
  },

  {
    name: '收货仓库',
    code: 'storehouse_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '采购订单号',
    code: 'purchase_fid',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '发货单单号',
    code: 'scm_fid',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '制单人',
    code: 'create_by',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '供应商代码',
    code: 'supplier_code',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '供应商名称',
    code: 'supplier_name',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '商品代码',
    code: 'item_code',
    width: columnWidthEnum.ITEM_CODE,
    features: { sortable: true },
  },
  {
    name: '商品名称',
    code: 'item_name',
    width: 180,
    features: { sortable: true },
  },
  {
    name: '采购规格',
    code: 'purchase_spec',
    width: columnWidthEnum.ITEM_SPEC,
    features: { sortable: true },
  },
  {
    name: '保质期',
    code: 'period',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '交货周期',
    code: 'purchase_period',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '一级分类',
    code: 'one_item_category_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '二级分类',
    code: 'two_item_category_name',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '三级分类',
    code: 'three_item_category_name',
    width: 100,
    features: { sortable: true },
  },

  {
    name: '采购数量',
    code: 'purchase_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },

  {
    name: '实发数量',
    align: 'right',
    code: 'basic_ship_quantity',
    width: 100,
    features: { sortable: true },
  },

  {
    name: '已到货数量',
    code: 'receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },
  {
    name: '未收货数量',
    code: 'un_receive_quantity',
    width: 100,
    align: 'right',
    features: { sortable: true },
  },

  {
    name: '创建日期',
    code: 'create_date',
    width: 120,
    features: { sortable: true },
  },
  {
    name: '审核日期',
    code: 'audit_time',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '订单失效日',
    code: 'delivery_deadline',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '交货日期',
    code: 'purchase_deadline',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '订单状态',
    code: 'state',
    width: 100,
    features: { sortable: true },
  },
  {
    name: '预约送货时间',
    code: 'appoint_time',
    width: 200,
    features: { sortable: true },
  },
];
