import Download from '@/utils/downloadBlobFile';
import { hasAuth } from '@/utils/kit';

import {
  XlbBasicForm,
  XlbBlueBar,
  XlbButton,
  XlbForm,
  XlbInput,
  XlbModal,
  XlbPageContainer,
  XlbProPageContainerRef,
  XlbProPageModal,
  XlbProPageModalRef,
  XlbTabs,
} from '@xlb/components';
import { Space } from 'antd';
import { useRef, useState } from 'react';

import {
  contractStatusList,
  contractTypes,
  formList,
  formListPre,
  moduleList,
  signFormList,
  signFormList2,
  tableList,
  tableList2,
} from './data';
import Item from './item';
import Api from './server';

const { Table, ToolBtn, SearchForm } = XlbPageContainer;
/**
 * xlbForm dateRange hideToggle
 */

const Index = () => {
  const [whiteVisible, setWhiteVisible] = useState(false);
  const [tabKey, setTabKey] = useState('1');
  const [url, setUrl] = useState('/erp-mdm/hxl.erp.contract.page');
  const [form] = XlbBasicForm.useForm<any>();
  const [signForm] = XlbBasicForm.useForm<any>();
  const template_id = XlbBasicForm.useWatch('template_id', signForm);

  const [visible, setVisible] = useState(false);
  const [signFormListInfo, setSignFormList] = useState([...signFormList]);
  let getData = () => {};
  //   const userInfo = LStorage.get('userInfo');
  const pageConatainerRef = useRef<XlbProPageContainerRef>(null);
  const pageModalRef = useRef<XlbProPageModalRef>(null);
  const tabKeyChange = async (e) => {
    console.log(e);
    if (e === '1') {
      tableList.map((v) => Render(v));
      pageConatainerRef.current.setColumns(tableList);
      setUrl('/erp-mdm/hxl.erp.contract.page');
    } else {
      tableList2.map((v) => Render(v));
      pageConatainerRef.current.setColumns(tableList2);

      setUrl('/erp-mdm/hxl.erp.contract.whitelist.page');
    }
    setTabKey(e);
    setTimeout(() => {
      console.log(pageConatainerRef);
      pageConatainerRef.current?.fetchData();
    }, 100);
  };

  // let refresh = () => {};

  const prevPost = () => {
    const formData = form.getFieldsValue(true);
    // console.log('🚀 ~ prevPost ~ formData:', formData);
    return {
      ...formData,
      contract_status_list: formData?.contract_status_list?.length
        ? formData?.contract_status_list
        : undefined,
    };
  };

  const down = async (record: any) => {
    const res = await Api.getUrl({ id: record?.id });
    console.log(res, 'res view');
    if (res.code === 0) {
      window.open(res?.data, '_blank');
    }
    // window.location.href = record?.contract_file_url;
  };

  // const handleViewContract = async (record: any) => {
  //   const res = await Api.viewContract(record);
  //   // console.log(res, 'res view');
  //   if (res.code === 0) {
  //     window.open(res?.data, '_blank');
  //   }
  // };

  /**
   *
   */
  const Render = (item: any) => {
    switch (item.code) {
      case 'contract_code':
        item.render = (value: string, record: any) => {
          return (
            <XlbProPageModal
              style={{ overflow: 'hidden' }}
              ref={pageModalRef}
              Content={({ onClose }) => {
                return (
                  <div>
                    <Item
                      onBack={onClose}
                      // onBack={() => {
                      //   // pageConatainerRef?.current?.pageContainerRef?.current?.fetchData?.();
                      //   // pageModalRef.current?.setOpen(false);
                      //   onClose();
                      // }}
                      record={record}
                    ></Item>
                  </div>
                );
              }}
            >
              <div className="xlb-table-clickBtn">{value}</div>
            </XlbProPageModal>
          );
        };
        break;
      case 'contract_type':
        item.render = (value: string) => {
          return <div className="overwidth">{contractTypes[`${value}`]}</div>;
        };
        break;
      case 'module':
        item.render = (value: string) => {
          return <div className="overwidth">{moduleList[`${value}`]}</div>;
        };
        break;
      case 'contract_status':
        item.render = (value) => {
          const curItem = contractStatusList.find((y) => y.value === value);
          return <div>{curItem?.label}</div>;
        };
        break;
      case 'action':
        return (item.render = (_, record: any) => (
          <Space>
            {/* {['PENDING_SIGN', 'PARTIALLY_SIGNED'].includes(
              record?.contract_status,
            ) && (
              <XlbButton type="link" onClick={() => handleViewContract(record)}>
                查看合同
              </XlbButton>
            )} */}
            {/* {['ALL'].includes(record?.contract_status) && ( */}
            <XlbButton type="link" onClick={() => down(record)}>
              下载
            </XlbButton>
            {/* )} */}
          </Space>
        ));
      default:
        return (item.render = (value: string) => (
          <div className="info overwidth">{value}</div>
        ));
    }
  };

  tableList.map((v) => Render(v));
  const handleSaveWhite = async () => {
    // fentchWhitelist
    try {
      const result = await signForm.validateFields();
      const { store_ids, supplier_ids, ...rest } = result;
      const sign_list = [
        { type: 'STORE', ids: store_ids },
        { type: 'SUPPLIER', ids: supplier_ids },
      ];
      const data = { ...rest, sign_list };
      Api.createWhite(data).then((res) => {
        if (res.code === 0) {
          signForm.resetFields();
          setVisible(false);
          setWhiteVisible(false);
          getData();
        }
      });
      console.log(result);
    } catch (error) {}
  };
  // /erp-mdm/hxl.erp.contract.whitelist.page
  const handleSave = async () => {
    try {
      const result = await signForm.validateFields();
      const { store_ids, supplier_ids, ...rest } = result;
      const sign_list = [
        { type: 'STORE', ids: store_ids },
        { type: 'SUPPLIER', ids: supplier_ids },
      ];
      const data = { ...rest, sign_list };
      Api.fentchModuleCreate(data).then((res) => {
        if (res.code === 0) {
          signForm.resetFields();
          setVisible(false);
          getData();
        }
      });
      console.log(result);
    } catch (error) {}
  };

  return (
    <>
      <XlbModal
        cancelText={'取消'}
        isCancel
        onCancel={() => {
          signForm.resetFields();
          setWhiteVisible(false);
        }}
        onOk={handleSaveWhite}
        title="合同白名单"
        width={900}
        open={whiteVisible}
      >
        <XlbBlueBar.SubTitle
          style={{ marginTop: 20, marginBottom: 12, fontSize: 20 }}
          title="合同模版"
        />
        <XlbForm
          isHideDate
          name="signFormListInfo"
          formList={signFormListInfo}
          form={signForm}
        ></XlbForm>
        <XlbBlueBar.SubTitle
          title="签署方"
          style={{ fontSize: 20, marginBottom: 12 }}
        />
        <XlbForm
          key={2}
          disabled={!template_id}
          name="signFormListInfo2"
          isHideDate
          formList={signFormList2}
          form={signForm}
        ></XlbForm>
        <XlbBasicForm form={signForm}>
          <XlbBasicForm.Item name="code" label="公司">
            <XlbInput
              width={270}
              disabled
              defaultValue={'南京众丞信息科技有限公司'}
            ></XlbInput>
            <div style={{ color: 'rgba(30, 33, 38, 0.7)' }}>
              社会信用代码：91320105MA27GN2F2T
            </div>
            <div style={{ color: 'rgba(30, 33, 38, 0.7)' }}>法人：陈延东</div>
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </XlbModal>

      <XlbModal
        cancelText={'取消'}
        isCancel
        onCancel={() => {
          signForm.resetFields();
          setVisible(false);
        }}
        onOk={handleSave}
        title="发起合同签署"
        width={900}
        open={visible}
      >
        <XlbBlueBar.SubTitle
          style={{ marginTop: 20, marginBottom: 12, fontSize: 20 }}
          title="合同模版"
        />
        <XlbForm
          isHideDate
          name="signFormListInfo"
          formList={signFormListInfo}
          form={signForm}
        ></XlbForm>
        <XlbBlueBar.SubTitle
          title="签署方"
          style={{ fontSize: 20, marginBottom: 12 }}
        />
        <XlbForm
          key={2}
          disabled={!template_id}
          name="signFormListInfo2"
          isHideDate
          formList={signFormList2}
          form={signForm}
        ></XlbForm>
        <XlbBasicForm form={signForm}>
          <XlbBasicForm.Item name="code" label="公司">
            <XlbInput
              width={270}
              disabled
              defaultValue={'南京众丞信息科技有限公司'}
            ></XlbInput>
            <div style={{ color: 'rgba(30, 33, 38, 0.7)' }}>
              社会信用代码：91320105MA27GN2F2T
            </div>
            <div style={{ color: 'rgba(30, 33, 38, 0.7)' }}>法人：陈延东</div>
          </XlbBasicForm.Item>
        </XlbBasicForm>
      </XlbModal>
      <XlbPageContainer
        ref={pageConatainerRef}
        url={url}
        tableColumn={tableList}
        prevPost={prevPost}
        immediatePost={true}
      >
        <ToolBtn showColumnsSetting={false}>
          {({ fetchData, loading }) => {
            getData = fetchData;
            return (
              <XlbButton.Group>
                <XlbButton
                  key="query"
                  label="查询"
                  type="primary"
                  disabled={loading}
                  onClick={() => {
                    fetchData();
                  }}
                  icon={<span className="iconfont icon-sousuo" />}
                />
                {hasAuth(['合同管理/发起合同签署', '编辑']) && (
                  <XlbButton
                    key="sent"
                    label="发起合同签署"
                    type="primary"
                    disabled={loading}
                    onClick={() => {
                      // fetchData();
                      setVisible(true);
                    }}
                    icon={<span className="iconfont icon-edit" />}
                  />
                )}
                {hasAuth(['合同管理/设置白名单', '编辑']) ? (
                  <>
                    <XlbButton
                      key="white"
                      label="设置白名单"
                      type="primary"
                      disabled={loading}
                      onClick={() => {
                        // fetchData();
                        setWhiteVisible(true);
                      }}
                      icon={<span className="iconfont icon-edit" />}
                    />

                    <XlbButton
                      key="white"
                      label="导出白名单"
                      type="primary"
                      disabled={
                        false
                        // loading || !hasAuth(['合同管理/设置白名单', '编辑'])
                      }
                      onClick={async () => {
                        // fetchData();
                        // setWhiteVisible(true);
                        const formData = form.getFieldsValue(true);
                        // console.log('🚀 ~ prevPost ~ formData:', formData);
                        const data = {
                          ...formData,
                          contract_status_list: formData?.contract_status_list
                            ?.length
                            ? formData?.contract_status_list
                            : undefined,
                        };
                        // const res = await Api.exportPage(
                        //   { ...data },
                        //   {
                        //     // responseType: 'blob',
                        //   },
                        // );
                        // const download = new Download();
                        // download.filename = '白名单模板.xlsx';
                        // download.xlsx(res?.data);
                        console.log(data);
                        const res1 = await Api.exportWhite(data);
                        const download2 = new Download();
                        download2.filename = '白名单模板.xlsx';
                        download2.xlsx(res1.data);
                        // message.success('导出成功');
                      }}
                      icon={<span className="iconfont icon-daochu" />}
                    />
                  </>
                ) : null}
              </XlbButton.Group>
            );
          }}
        </ToolBtn>
        <SearchForm>
          <XlbForm
            formList={[formListPre[tabKey === '1' ? 0 : 1], ...formList]}
            form={form}
            isHideDate={true}
          />
        </SearchForm>
        <XlbTabs
          style={{ marginLeft: '16px' }}
          activeKey={tabKey}
          onTabClick={(e) => {
            tabKeyChange(e);
            form.setFieldValue('contract_status_list', []);
            form.setFieldValue('contract_template_name', null);
          }}
          items={[
            {
              label: '签署信息',
              key: '1',
            },
            {
              label: '白名单信息',
              key: '2',
            },
          ]}
        ></XlbTabs>
        <Table primaryKey="id" key="id" />
      </XlbPageContainer>
    </>
  );
};
export default Index;
