/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-06-15 16:25:24
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2024-06-15 17:22:34
 * @FilePath: \xlb_tms_web\src\pages\report-forms-management\driver-check-in-statistics\ErrorBoundary\index.tsx
 * @Description:
 */
import { CloseCircleFilled } from '@ant-design/icons';
import { Component } from 'react';
import './index.less';

type StateType = {
  hasError: boolean;
  errorMsg: string | undefined;
};

class ErrorBoundary extends Component<{ children: any }, StateType> {
  state: StateType;
  constructor(props: any) {
    super(props);
    this.state = {
      hasError: false,
      errorMsg: '',
    };
  }

  static getDerivedStateFromError(error: any) {
    // 更新 state 使下一次渲染能够展示降级后的 UI
    console.log('getDerivedStateFromError', error);

    return { hasError: true };
  }

  componentDidCatch(error: any, errorInfo: any) {
    // 你可以同样将这些信息发送到远程日志记录服务
    console.error('捕获到异常:', error, errorInfo);

    this.setState({
      ...this.state,
      errorMsg: error.message,
    });
  }

  render() {
    if (this.state.hasError) {
      console.log('error', this.state.errorMsg);

      // 你可以自定义降级后的 UI 并渲染
      return (
        <div className="error-boundary-result">
          <CloseCircleFilled className="error-boundary-icon" />
          <div className="error-boundary-title">Something went wrong.</div>
          <div className="error-boundary-extra">{this.state.errorMsg}</div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
